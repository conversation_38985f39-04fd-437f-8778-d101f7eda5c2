<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<!-- 页面标题和新增按钮 -->
<div style="display: flex; justify-content: space-between; align-items: center; margin: 80px 20px 20px 20px; padding-bottom: 12px; border-bottom: 1px solid #ddd;">
    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">关于我们管理</h1>
    <div>
        <a href="about.php?action=add" style="display: inline-block; padding: 8px 16px; background-color: #1b68ff; color: #fff; text-decoration: none; font-size: 14px; font-weight: 500; border-radius: 4px;">
            <i class="fas fa-plus"></i> 添加页面
        </a>
    </div>
</div>

<style>
/* 筛选表单样式 - 与新闻分类管理保持一致 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

.filter-form-item .btn {
    height: 32px;
    padding: 4px 12px;
    font-size: 14px;
    line-height: 1.5;
}

/* 快速筛选标签样式 */
.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.filter-tag {
    display: inline-block;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    border-radius: 20px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.filter-tag.active-all {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}

.filter-tag.inactive-all {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
}

.filter-tag.active-enabled {
    background: #3ad29f;
    color: white;
    border-color: #3ad29f;
}

.filter-tag.inactive-enabled {
    background: #f8f9fa;
    color: #3ad29f;
    border-color: #3ad29f;
}

.filter-tag.active-disabled {
    background: #f82f58;
    color: white;
    border-color: #f82f58;
}

.filter-tag.inactive-disabled {
    background: #f8f9fa;
    color: #f82f58;
    border-color: #f82f58;
}

.filter-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 表格样式 - 与新闻分类管理保持一致 */
.content-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-table thead {
    background: #f8f9fa;
}

.content-table th {
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e9ecef;
    font-size: 14px;
}

.content-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    font-size: 14px;
}

.content-table tbody tr:hover {
    background-color: #f8f9fa;
}

.content-table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态标签样式 */
.tag {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    text-align: center;
    min-width: 50px;
}

.tag-success {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border: 1px solid rgba(58, 210, 159, 0.2);
}

.tag-danger {
    background: rgba(248, 47, 88, 0.1);
    color: #f82f58;
    border: 1px solid rgba(248, 47, 88, 0.2);
}

/* 操作按钮样式 */
.page-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 4px;
    min-width: 180px;
}

.page-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
    white-space: nowrap;
    text-decoration: none !important;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-light-info {
    color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.btn-light-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-light-primary {
    color: #1b68ff;
    background-color: rgba(27, 104, 255, 0.1);
    border: 1px solid rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-light-danger {
    color: #f82f58;
    background-color: rgba(248, 47, 88, 0.1);
    border: 1px solid rgba(248, 47, 88, 0.2);
}

.btn-light-danger:hover {
    color: #fff;
    background-color: #f82f58;
    border-color: #f82f58;
}

/* PC端专用样式 - 固定布局 */
.container {
    min-width: 1200px;
    width: 100%;
}

.page-title {
    min-width: 1000px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ddd;
}

.filter-form {
    min-width: 800px;
}

.content-table {
    min-width: 1000px;
    table-layout: fixed;
}

/* 固定列宽 */
.content-table th:nth-child(1),
.content-table td:nth-child(1) { width: 50px; }
.content-table th:nth-child(2),
.content-table td:nth-child(2) { width: 60px; }
.content-table th:nth-child(3),
.content-table td:nth-child(3) { width: 200px; }
.content-table th:nth-child(4),
.content-table td:nth-child(4) { width: 180px; }
.content-table th:nth-child(5),
.content-table td:nth-child(5) { width: 80px; }
.content-table th:nth-child(6),
.content-table td:nth-child(6) { width: 60px; }
.content-table th:nth-child(7),
.content-table td:nth-child(7) { width: 120px; }
.content-table th:nth-child(8),
.content-table td:nth-child(8) { width: 200px; }

/* PC端优化 */
body {
    min-width: 1200px;
}

.main-content {
    min-width: 1200px;
    padding: 20px;
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.d-flex {
    display: flex;
}

.gap-2 {
    gap: 8px;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #1b68ff;
    border-color: #1b68ff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    text-decoration: none;
    color: #fff;
}

.filter-form-item .form-control {
    min-width: 120px;
}

.btn {
    min-width: 80px;
    white-space: nowrap;
}

.pagination {
    justify-content: center;
    margin-top: 20px;
}

.page-link {
    min-width: 40px;
    text-align: center;
}
</style>


<!-- 单页管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速筛选标签 -->
        <div class="filter-tags">
            <a href="about.php" class="filter-tag <?php if($status == -1 && !$keyword): ?>active-all<?php else: ?>inactive-all<?php endif; ?>">全部页面</a>
            <a href="about.php?status=1" class="filter-tag <?php if($status == 1): ?>active-enabled<?php else: ?>inactive-enabled<?php endif; ?>">已启用</a>
            <a href="about.php?status=0" class="filter-tag <?php if($status == 0): ?>active-disabled<?php else: ?>inactive-disabled<?php endif; ?>">已禁用</a>
        </div>

        <!-- 紧凑筛选表单 -->
        <form method="GET" action="about.php" class="filter-form">
            <input type="hidden" name="action" value="list">

            <div class="filter-form-item">
                <label>关键词:</label>
                <input type="text" name="keyword" class="form-control" placeholder="标题、路径或URL" value="<?php echo $keyword ?? ""; ?>" style="width: 200px;">
            </div>

            <div class="filter-form-item">
                <label>状态:</label>
                <select name="status" class="form-control" style="width: 100px;">
                    <option value="-1" <?php if($status == -1): ?>selected<?php endif; ?>>全部</option>
                    <option value="1" <?php if($status == 1): ?>selected<?php endif; ?>>启用</option>
                    <option value="0" <?php if($status == 0): ?>selected<?php endif; ?>>禁用</option>
                </select>
            </div>

            <div class="filter-form-item">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="about.php" class="btn btn-outline">
                    <i class="fas fa-redo"></i> 重置
                </a>
            </div>
        </form>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> <?php echo $message ?? ""; ?>
</div>
<?php endif; ?>

<?php if($error): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i> <?php echo $error ?? ""; ?>
</div>
<?php endif; ?>

        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="content-table">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="checkAll" onchange="toggleAll(this)">
                        </th>
                        <th width="60">ID</th>
                        <th>页面标题</th>
                        <th width="200">访问路径</th>
                        <th width="80">状态</th>
                        <th width="60">排序</th>
                        <th width="100">创建时间</th>
                        <th width="160">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if($pages): ?>
                    <form id="batchForm" method="POST" action="about.php?action=batch_delete">
                    <?php if(null !== ($pages ?? null) && is_array($pages)): foreach($pages as $page): ?>
                    <tr>
                        <td>
                            <input type="checkbox" name="ids[]" value="<?php echo (isset($page['id'])) ? $page['id'] : ""; ?>" class="item-checkbox">
                        </td>
                        <td><strong><?php echo (isset($page['id'])) ? $page['id'] : ""; ?></strong></td>
                        <td>
                            <div style="font-weight: 600; color: #333; margin-bottom: 2px;"><?php echo (isset($page['title'])) ? $page['title'] : ""; ?></div>
                            <?php if($page['meta_description']): ?>
                            <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                <?php echo mb_substr($page['meta_description'], 0, 60, 'utf-8') . (mb_strlen($page['meta_description'], 'utf-8') > 60 ? '...' : ''); ?>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div style="margin-bottom: 3px;">
                                <code style="font-size: 12px; background: #f8f9fa; padding: 2px 4px; border-radius: 3px;"><?php echo (isset($page['display_path'])) ? $page['display_path'] : ""; ?></code>
                                <?php if(empty($page['path'])): ?>
                                <span style="font-size: 11px; color: #666; margin-left: 5px;">(使用ID访问)</span>
                                <?php endif; ?>
                            </div>
                            <a href="<?php echo (isset($page['url'])) ? $page['url'] : ""; ?>" target="_blank" class="btn btn-sm btn-light-info" style="font-size: 11px; padding: 2px 6px;">
                                <i class="fas fa-external-link-alt"></i> 预览
                            </a>
                        </td>
                        <td>
                            <?php if($page['status'] == 1): ?>
                                <span class="tag tag-success">启用</span>
                            <?php else: ?>
                                <span class="tag tag-danger">禁用</span>
                            <?php endif; ?>
                        </td>
                        <td style="text-align: center; font-weight: 600;"><?php echo (isset($page['sort_order'])) ? $page['sort_order'] : ""; ?></td>
                        <td style="font-size: 12px; color: #666;">
                            <?php echo date('m-d H:i', $page['created_at']); ?>
                        </td>
                        <td>
                            <div class="page-actions">
                                <a href="<?php echo (isset($page['url'])) ? $page['url'] : ""; ?>" target="_blank" class="btn btn-light-info" title="查看">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="about.php?action=edit&id=<?php echo (isset($page['id'])) ? $page['id'] : ""; ?>" class="btn btn-light-primary" title="编辑">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <a href="javascript:if(confirm('确定要删除这个页面吗？'))location='about.php?action=delete&id=<?php echo (isset($page['id'])) ? $page['id'] : ""; ?>'" class="btn btn-light-danger" title="删除">
                                    <i class="fas fa-trash"></i> 删除
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; endif; ?>
                    </form>
                    <?php else: ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-info-circle fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                            <div>暂无页面数据</div>
                            <div style="margin-top: 10px;">
                                <a href="about.php?action=add" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加页面
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- 批量操作和分页 -->
        <?php if($pages): ?>
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border-top: 1px solid #f0f0f0; background: #fafafa;">
            <div>
                <button type="button" class="btn btn-sm btn-danger" onclick="batchDelete()">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
                <span style="margin-left: 15px; color: #666; font-size: 13px;">
                    共 <?php echo $total ?? ""; ?> 条记录
                </span>
            </div>

            <!-- 分页 -->
            <?php if($total_pages > 1): ?>
            <div class="pagination" style="margin: 0;">
                <?php if($page > 1): ?>
                    <a href="about.php?action=list&page=1<?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">首页</a>
                    <a href="about.php?action=list&page={$page-1}<?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">上一页</a>
                <?php endif; ?>

                <!-- 页码显示 -->
                <?php if($page > 2): ?>
                    <a href="about.php?action=list&page={$page-1}<?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">{$page-1}</a>
                <?php endif; ?>

                <span class="page-link active"><?php echo $page ?? ""; ?></span>

                <?php if($page < $total_pages - 1): ?>
                    <a href="about.php?action=list&page={$page+1}<?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">{$page+1}</a>
                <?php endif; ?>

                <?php if($page < $total_pages): ?>
                    <a href="about.php?action=list&page={$page+1}<?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">下一页</a>
                    <a href="about.php?action=list&page=<?php echo $total_pages ?? ""; ?><?php if($keyword): ?>&keyword=<?php echo $keyword ?? ""; ?><?php endif; ?><?php if($status >= 0): ?>&status=<?php echo $status ?? ""; ?><?php endif; ?>" class="page-link">末页</a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// 全选/取消全选
function toggleAll(checkbox) {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);
}

// 批量删除
function batchDelete() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('请选择要删除的页面');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个页面吗？`)) {
        document.getElementById('batchForm').submit();
    }
}

// 监听复选框变化
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    const checkAll = document.getElementById('checkAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            checkAll.checked = checkedCount === checkboxes.length;
            checkAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
        });
    });
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
