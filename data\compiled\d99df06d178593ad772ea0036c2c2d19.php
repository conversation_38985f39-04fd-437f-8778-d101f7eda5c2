<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title><?php if($site_title): ?><?php echo $site_title ?? ""; ?><?php else: ?><?php echo $site_name ?? ""; ?> - 免费发布分类信息<?php endif; ?></title>
                <meta name="keywords" content="<?php if($site_keywords): ?><?php echo $site_keywords ?? ""; ?><?php else: ?>分类信息,免费发布,信息平台<?php endif; ?>" />
                <meta name="description" content="<?php if($site_description): ?><?php echo $site_description ?? ""; ?><?php else: ?>提供免费发布分类信息服务的网站<?php endif; ?>" />

				<link rel="stylesheet" href="/template/pc/css/common.css">
               <link rel="stylesheet" href="/template/pc/css/index.css?<?php echo time(); ?>">
               <script type="text/javascript" src="/template/pc/js/m.js"></script>
		
	</head>
	<body>
		    <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 使用缓存的导航数据（基于分类自动生成，永不过期）
			$navList = getCachedNavigation();

			// 输出导航菜单（跳过首页，因为已经单独显示）
			foreach ($navList as $nav) {
				if ($nav['id'] != 0) { // 跳过首页
					echo '<li><a href="'.$nav['url'].'">'.$nav['name'].'</a></li>';
				}
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

	<?php if($site_analytics): ?>
	<!-- 网站统计代码 -->
	<?php echo $site_analytics ?? ""; ?>
	<?php endif; ?>

		<div class="mo-box yui-1200">
			<a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank" title="泊头生活网广告招商"><img src="template/pc/images/2022112309104176804697_1240_90.png"> </a>
    	</div>
		<div class="yui-clear"></div>
		<!-- 主区域 -->
		<div class="yui-content yui-1200">
			<!-- 最新信息列表 -->
			<div class="latest-posts">

			
			</div>
			<!-- 页面顶部信息列表 -->
			<div class="yui-index">
				<!-- 左侧内容区域 -->
				<div class="yui-index-l">
					<!-- 左侧顶部区域 -->
					<div class="left-top-section">
						<div class="gonggao-wrap">
							<div class="flash-img bg-blue">
								<div id="slider-wrap">
									<ul id="slider">
										<li><img src="/template/pc/images/botou1.png" width="302" height="200" alt="泊头生活网" /></li>
										<li><img src="/template/pc/images/botou2.png" width="302" height="200" alt="泊头信息网" /></li>
										<li><img src="/template/pc/images/botou3.png" width="302" height="200" alt="泊头新闻" /></li>
									</ul>
									<!--controls-->
									<div class="btns" id="next">></div>
									<div class="btns" id="previous"><</div>
									<div id="pagination-wrap">
										<ul></ul>
									</div>
								</div>
							</div>
							<div class="gonggao mt10 bg-white">
								<div class="yui-h-title">
									<h3>网站公告</h3><span><a href="https://www.botou.net/gonggao/">更多</a></span>
								</div>
								<div class="yui-small-list">
									<ul>
										<li><a href="https://www.botou.net/gonggao/50.html" target="_blank">泊头市防范电信诈骗小课堂</a></li>
										<li><a href="https://www.botou.nethttps://www.botou.net/help/tiaoli.html" target="_blank">发布信息请遵守以下内容</a></li>
										<li><a href="https://www.botou.net/gonggao/23.html" target="_blank">四步轻松帮您初识虚假信息</a></li>
										<li><a href="https://www.botou.net/gonggao/23.html" target="_blank">四步轻松帮您初识虚假信息</a></li>
									</ul>
								</div>
							</div>
						</div>
						<div class="news-wrap bg-white">
							<div class="yui-index-topnews">
								<div class="yui-top-img yui-left">
									<img src="/template/pc/images/top-news-bg.png" />

								</div>
								<div class="yui-top-text yui-right">
									<a href="https://www.botou.net/news/114.html" target="_blank">泊头市开展乡村振兴驻村干部能力素质培训...</a>
									<p>              &amp;ensp;&amp;ensp;&amp;ensp;&amp;ensp;为进一步提升驻村干部的政策水平和业务能力，8月2日，泊头市举办乡村振兴驻村干部能力素质培训班，各乡镇分管班子成员、驻村工作队全体成员共计100余人参加培训。副市长姚晓雨出席</p>
								</div>

							</div>
							<div class="yui-clear"></div>
							<div class="yui-index-news">
								<ul>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/120.html" title="河北省家电焕新潮！看看泊头有哪些适用门店"
											target="_blank">河北省家电焕新潮！看看泊头有哪些适用门店</a><span class="yui-post-time">2024-09-27</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/119.html" title="泊头市举行2024年网络安全宣传周启动仪式"
											target="_blank">泊头市举行2024年网络安全宣传周启动仪式</a><span class="yui-post-time">2024-09-23</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/118.html" title="泊头市召开《河北省安全生产条例》专题培训会"
											target="_blank">泊头市召开《河北省安全生产条例》专题培训会</a><span class="yui-post-time">2024-09-07</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/117.html" title="泊头市王武庄镇走访慰问见义勇为司机赵广阔"
											target="_blank">泊头市王武庄镇走访慰问见义勇为司机赵广阔</a><span class="yui-post-time">2024-08-27</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/116.html" title="泊头市医院举办青年医生科普演讲大赛暨医师节表彰大会"
											target="_blank">泊头市医院举办青年医生科普演讲大赛暨医师节表彰大会</a><span class="yui-post-time">2024-08-22</span></li>
									<li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
											target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li><li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
											target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li><li><span class="yui-lm-name">泊头新闻</span><a href="https://www.botou.net/news/113.html" title="泊头市召开深化" 五个对接"政企恳谈会"
												target="_blank">泊头市召开深化"五个对接"政企恳谈会</a><span class="yui-post-time">2024-08-04</span></li>
								</ul></div>

					</div>
				</div>
				<div class="index-new-list-left bg-white pd10">
					<div class="index-new-title">
						<h3>分类信息MESSAGE</h3><a href="/help/tiaoli.html" target="_blank">信息为何不能审核通过</a>
					</div>
					<div class="info-list mt20">
						<ul class="ding">
							<?php if(null !== ($topPosts ?? null) && is_array($topPosts)): foreach($topPosts as $post): ?>
							<li><?php if($post['category_name']): ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/" style="color: #999; text-decoration: none; margin-right: 5px;">[<?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?>]</a><?php endif; ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a><span class="ding">顶</span><span class="text-dot">今天</span></li>
							<?php endforeach; endif; ?>
						</ul>
						<ul>
							<?php if(null !== ($normalPosts ?? null) && is_array($normalPosts)): foreach($normalPosts as $post): ?>
							<li><?php if($post['category_name']): ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/" style="color: #999; text-decoration: none; margin-right: 5px;">[<?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?>]</a><?php endif; ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" title="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>" target="_blank"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a><span class="text-dot"><?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?></span></li>
							<?php endforeach; endif; ?>
						</ul>
					</div>
				</div>
			</div>

			<!-- 右侧内容区域 -->
			<div class="yui-index-r">
				<div class="bianmin pd10">
					<div class="yui-h-title">
						<h3>快速导航</h3><span><a href="#">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							<li><a target="_blank" href="http://www.botou.gov.cn/botou/c100818/listCollect.shtml">泊头概况</a></li>
							<li><a target="_blank" href="http://www.botou.gov.cn/botou/c100821/202204/3b198500b7614957b919ae6d29826e6c.shtml">城市简介</a></li>
							<li><a target="_blank" href="http://czbt.hbzwfw.gov.cn/">政务服务</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
							<li><a target="_blank" href="https://www.botou.net/gongjiao/">泊头公交</a></li>
						</ul>
					</div>
				</div>
				<div class="zhuanti">
					<div class="yui-h-title">
						<h3>快速导航</h3><span><a href="#">更多</a></span>
					</div>
					<div class="zhuanti-content mt10">
					
						<div class="zhuanti-item">
							<div class="zhuanti-game">
								<a href="#">
									<div class="game-logo"></div>
									<h4>青少年活动中心</h4>
									<p>丰富多彩的青少年暑期活动</p>
									<div class="people-count">已有2589人参与</div>
								</a>
							</div>
						</div>
					</div>
				</div>

				<!-- 热门信息模块 -->
				<div class="bbs-hot">
					<div class="yui-h-title">
						<h3>热门信息</h3><span><a href="/">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							<?php if(null !== ($hotPosts ?? null) && !empty($hotPosts)): ?>
							<?php if(null !== ($hotPosts ?? null) && is_array($hotPosts)): foreach($hotPosts as $post): ?>
							<li>
								<a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" title="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>">
									<?php 
									$title = $post['title'];
									if (mb_strlen($title) > 30) {
										echo mb_substr($title, 0, 30) . '...';
									} else {
										echo $title;
									}
									 ?>
								</a>
								<span class="view-count">(<?php echo (isset($post['view_count'])) ? $post['view_count'] : ""; ?>次浏览)</span>
							</li>
							<?php endforeach; endif; ?>
							<?php else: ?>
							<li><a href="#">暂无热门信息</a></li>
							<?php endif; ?>
						</ul>
					</div>
				</div>

				<div class="bbs-hot">
					<div class="yui-h-title">
						<h3>资讯索引</h3><span><a href="/news">更多</a></span>
					</div>
					<div class="yui-small-list">
						<ul>
							<?php if(null !== ($latestNews ?? null) && !empty($latestNews)): ?>
							<?php if(null !== ($latestNews ?? null) && is_array($latestNews)): foreach($latestNews as $news): ?>
							<li>
								<a href="/news/<?php echo (isset($news['id'])) ? $news['id'] : ""; ?>.html" title="<?php echo (isset($news['title'])) ? $news['title'] : ""; ?>">
									<?php 
									$title = $news['title'];
									if (mb_strlen($title) > 35) {
										echo mb_substr($title, 0, 35) . '...';
									} else {
										echo $title;
									}
									 ?>
								</a>
								<span class="news-date">(<?php echo (isset($news['formatted_time'])) ? $news['formatted_time'] : ""; ?>)</span>
							</li>
							<?php endforeach; endif; ?>
							<?php else: ?>
							<li><a href="#">暂无最新资讯</a></li>
							<?php endif; ?>
						</ul>
					</div>
				</div>
			</div>
			<div class="yui-clear"></div>
		</div>
	</div>
		<!-- 分类信息区域 end -->
	</div>
	<!-- 主区域 yui-content end-->
	<div class="yui-clear"></div>

	<!-- 常用信息查询 -->
	<div class="yui-1200">
		<div class="bianmin-query bg-white mt10">
			<div class="yui-h-title">
				<h3>常用信息查询</h3>
			</div>
			<div class="query-list">
				<ul>
<?php echo get_block("changyongxinxi"); ?>
				</ul>
			</div>
		</div>
	</div>
	<!-- 常用信息查询 end -->

	<!-- 友情链接 -->
	<div class="yui-1200">
		<div class="bianmin-tel bg-white mt10">
			<div class="yui-h-title">
				<h3>便民电话</h3>
			</div>
			<div class="tel-list">
				<div class="tel-item">
					<div class="tel-name">火警</div>
					<div class="tel-number">119</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">报警</div>
					<div class="tel-number">110</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">急救</div>
					<div class="tel-number">120</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">交通事故</div>
					<div class="tel-number">122</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">市长热线</div>
					<div class="tel-number">12345</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">天气预报</div>
					<div class="tel-number">12121</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">供电服务</div>
					<div class="tel-number">95598</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">燃气服务</div>
					<div class="tel-number">96577</div>
				</div>
				<div class="tel-item">
					<div class="tel-name">水务服务</div>
					<div class="tel-number">96585</div>
				</div>
			</div>
		</div>
		<div class="friend-links bg-white pd10">
			<div class="yui-h-title">
				<h3>友情链接</h3><span><a href="#">更多</a></span>
			</div>
			<div class="yui-small-list">
				<?php 
				if (function_exists('getCachedFriendLinks')) {
					$friend_links = getCachedFriendLinks(20); // 获取最多20个友情链接
					$this->assign('friend_links', $friend_links);
				}
				 ?>
				
				<?php if($friend_links): ?>
				<ul><?php if(null !== ($friend_links ?? null) && is_array($friend_links)): foreach($friend_links as $link): ?>
					<li><a href="<?php echo (isset($link['url'])) ? $link['url'] : ""; ?>" target="_blank" ><?php echo (isset($link['name'])) ? $link['name'] : ""; ?></a></li>
					<?php endforeach; endif; ?>
				</ul>
				<?php endif; ?>
			</div>
		</div>

		

	</div>
	<!-- 友情链接 end -->

	<div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
<?php echo get_block('footer_nav'); ?>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
	<script type="text/javascript" src="/template/pc/js/slide.js"></script>
	<script type="text/javascript" src="/template/pc/js/common.js"></script>
	<script>
		$(function() {
			// 初始化轮播图
			$("#slider-wrap").slider();
		});
	</script>
	</body>
</html>
