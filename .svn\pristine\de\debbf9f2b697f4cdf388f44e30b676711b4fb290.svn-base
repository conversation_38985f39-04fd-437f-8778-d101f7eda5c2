<?php
/**
 * 统一前台应用入口
 * 整合各种前台功能，减少根目录文件数量
 */

define('IN_BTMPS', true);

// 性能监控开始
$app_start_time = microtime(true);
$app_start_memory = memory_get_usage();

// 引入公共文件
require_once('./include/common.inc.php');

// 获取功能模块
$module = isset($_GET['m']) ? $_GET['m'] : '';
$action = isset($_GET['a']) ? $_GET['a'] : 'index';

// 简单的页面缓存机制（可选）
$enable_cache = false; // 可以通过配置开启
$cache_key = '';
if ($enable_cache && in_array($module, ['category', 'news', 'about'])) {
    $cache_key = 'app_' . $module . '_' . md5(serialize($_GET));
    $cached_content = cache_get($cache_key);
    if ($cached_content !== false) {
        echo $cached_content;
        exit;
    }
    ob_start();
}

// 根据模块分发请求
switch ($module) {
    case 'news':
        // 新闻相关功能
        handleNewsModule($action);
        break;
        
    case 'category':
        // 分类相关功能
        handleCategoryModule($action);
        break;
        
    case 'post':
        // 信息发布相关功能
        include 'pages/post.php';
        break;

    case 'manage':
        // 信息管理功能（修改、删除、刷新等）
        include 'pages/manage.php';
        break;
        
    case 'search':
        // 搜索功能
        handleSearchModule($action);
        break;
        
    case 'report':
        // 举报功能
        handleReportModule($action);
        break;
        
    case 'about':
        // 关于我们
        handleAboutModule($action);
        break;
        
    case 'top':
        // 排行榜
        handleTopModule($action);
        break;
        

    case 'captcha':
        // 验证码
        handleCaptchaModule($action);
        break;
        
    case 'image':
        // 图片处理
        handleImageModule($action);
        break;
        
    default:
        // 默认首页
        include 'index.php';
        break;
}

/**
 * 处理新闻模块
 */
function handleNewsModule($action) {
    include_once 'pages/news.php';
}

/**
 * 处理分类模块
 */
function handleCategoryModule($action) {
    include_once 'pages/category.php';
}





/**
 * 处理搜索模块
 */
function handleSearchModule($action) {
    include_once 'pages/search.php';
}

/**
 * 处理举报模块
 */
function handleReportModule($action) {
    include_once 'pages/report.php';
}

/**
 * 处理关于我们模块
 */
function handleAboutModule($action) {
    include_once 'pages/about.php';
}

/**
 * 处理排行榜模块
 */
function handleTopModule($action) {
    include_once 'pages/top.php';
}



/**
 * 处理验证码模块
 */
function handleCaptchaModule($action) {
    include 'pages/captcha.php';
}

/**
 * 处理图片模块
 */
function handleImageModule($action) {
    switch ($action) {
        case 'phone':
            // 电话号码图片生成
            if (!isset($_GET['phone']) || empty($_GET['phone'])) {
                header("HTTP/1.0 403 Forbidden");
                exit;
            }
            
            $phone = $_GET['phone'];
            $fontSize = isset($_GET['size']) ? intval($_GET['size']) : 20;
            
            generatePhoneImage($phone, $fontSize);
            break;
            
        default:
            header("HTTP/1.0 404 Not Found");
            exit;
    }
}

// 缓存保存（如果启用了缓存）
if ($enable_cache && !empty($cache_key)) {
    $content = ob_get_contents();
    cache_set($cache_key, $content, 300); // 缓存5分钟
    ob_end_flush();
}

// 性能监控结束（开发环境可显示）
if (isset($_GET['debug']) && $_GET['debug'] === '1') {
    $app_end_time = microtime(true);
    $app_end_memory = memory_get_usage();

    $execution_time = round(($app_end_time - $app_start_time) * 1000, 2);
    $memory_used = round(($app_end_memory - $app_start_memory) / 1024, 2);

    echo "<!-- App.php Performance: {$execution_time}ms, Memory: {$memory_used}KB -->";
}
?>
