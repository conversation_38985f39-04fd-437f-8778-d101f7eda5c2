<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? ""; ?> - <?php echo $site_name ?? ""; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .block-demo { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
        .code { background: #f4f4f4; padding: 10px; font-family: monospace; border-radius: 3px; margin: 10px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前台块标签测试页面</h1>
        
        <div class="test-section">
            <h2>1. 底部导航块测试</h2>
            <p>标签: <code><?php echo get_block("footer_nav"); ?></code></p>
            <div class="block-demo">
                <strong>输出结果:</strong>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; margin-top: 10px;">
                    <?php echo get_block("footer_nav"); ?>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>2. 联系方式块测试</h2>
            <p>标签: <code><?php echo get_block("contact_info"); ?></code></p>
            <div class="block-demo">
                <strong>输出结果:</strong>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; margin-top: 10px;">
                    <?php echo get_block("contact_info"); ?>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>3. 版权信息块测试</h2>
            <p>标签: <code><?php echo get_block("copyright"); ?></code></p>
            <div class="block-demo">
                <strong>输出结果:</strong>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; margin-top: 10px;">
                    <?php echo get_block("copyright"); ?>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>4. 不存在的块测试</h2>
            <p>标签: <code><?php echo get_block("nonexistent"); ?></code></p>
            <div class="block-demo">
                <strong>输出结果:</strong>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; margin-top: 10px;">
                    "<?php echo get_block("nonexistent"); ?>" (应该为空)
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>5. 使用说明</h2>
            <div class="code">
                <strong>在模板中使用块标签：</strong><br>
                {literal}{block:标识符}{/literal}<br><br>
                
                <strong>示例：</strong><br>
                {literal}<?php echo get_block("footer_nav"); ?>{/literal} - 调用底部导航<br>
                {literal}<?php echo get_block("contact_info"); ?>{/literal} - 调用联系方式<br>
                {literal}<?php echo get_block("copyright"); ?>{/literal} - 调用版权信息<br>
            </div>
        </div>
        
        <div class="test-section">
            <h2>6. 管理链接</h2>
            <p><a href="admin/content_blocks.php" target="_blank">管理内容块</a></p>
            <p><a href="/">返回首页</a></p>
        </div>
    </div>
</body>
</html>
