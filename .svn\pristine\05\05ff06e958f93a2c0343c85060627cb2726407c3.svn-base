<?php
// 定义安全常量（如果未定义）
if (!defined('IN_BTMPS')) {
    define('IN_BTMPS', true);
}

/**
 * 关于我们单页显示页面
 * 支持伪静态URL访问
 */
require_once(dirname(__DIR__) . '/include/common.inc.php');

// 获取页面路径参数或ID参数
$path = isset($_GET['path']) ? trim($_GET['path']) : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (empty($path) && $id <= 0) {
    header("HTTP/1.0 404 Not Found");
    // 设置404页面需要的变量
    $tpl->assign('site_name', $config['site_name']);
    $tpl->display('404.htm');
    exit;
}

// 从数据库查询页面信息
if (!empty($path)) {
    // 通过路径查询
    $sql = "SELECT * FROM about WHERE path = ? AND status = 1";
    $result = $db->query($sql, array($path));
} else {
    // 通过ID查询
    $sql = "SELECT * FROM about WHERE id = ? AND status = 1";
    $result = $db->query($sql, array($id));
}

if (!$result || $db->num_rows($result) == 0) {
    header("HTTP/1.0 404 Not Found");
    // 设置404页面需要的变量
    $tpl->assign('site_name', $config['site_name']);
    $tpl->display('404.htm');
    exit;
}

$page = $db->fetch_array($result);

// 获取导航菜单数据
$nav_pages = array();
$nav_sql = "SELECT title, path, url FROM about WHERE status = 1 ORDER BY sort_order ASC, id ASC";
$nav_result = $db->query($nav_sql);
if ($nav_result && $db->num_rows($nav_result) > 0) {
    while ($nav_page = $db->fetch_array($nav_result)) {
        $nav_page['is_current'] = ($nav_page['path'] == $page['path']);
        $nav_pages[] = $nav_page;
    }
}

// 设置模板变量
$tpl->assign('page', $page);
$tpl->assign('title', $page['title']);
$tpl->assign('content', $page['content']);
$tpl->assign('meta_keywords', $page['meta_keywords']);
$tpl->assign('meta_description', $page['meta_description']);
$tpl->assign('nav_pages', $nav_pages);
$tpl->assign('current_page', 'about');

// 设置SEO信息
$tpl->assign('site_title', $page['title']);
$tpl->assign('site_keywords', $page['meta_keywords'] ?: '关于我们,单页信息');
$tpl->assign('site_description', $page['meta_description'] ?: $page['title']);

// 显示模板（系统会自动根据设备类型选择模板目录）
$tpl->display('about.htm');
?>
