{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>
        <i class="fas fa-flag"></i>
        举报详情
    </h1>
    <div class="page-actions">
        <a href="report.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回列表</span>
        </a>
    </div>
</div>




<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    {if $message}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <strong>操作成功</strong>
            <p>{$message}</p>
        </div>
    </div>
    {/if}

    <!-- 举报详情 -->
    <div class="card">
        <div class="card-header">
            <h3>
                <i class="fas fa-flag"></i>
                举报详情信息
            </h3>
            <div class="card-actions">
                <span class="status-tag {if $report.status == 0}warning{else}success{/if}">
                    {if $report.status == 0}未处理{else}已处理{/if}
                </span>
            </div>
        </div>

        <div class="card-body">
            <!-- 基本信息 -->
            <div class="detail-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    基本信息
                </h4>

                <div class="detail-grid">
                    <div class="detail-item">
                        <label class="detail-label">举报ID</label>
                        <div class="detail-value">#{$report.id}</div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">被举报信息ID</label>
                        <div class="detail-value">#{$report.post_id}</div>
                    </div>

                    <div class="detail-item full-width">
                        <label class="detail-label">被举报信息标题</label>
                        <div class="detail-value">
                            <div class="info-title">
                                {$report.post_title}
                                {if $report.post_id > 0 && $report.category_pinyin != ''}
                                <a href="../{$report.category_pinyin}/{$report.post_id}.html" target="_blank" class="btn btn-sm btn-primary">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看信息
                                </a>
                                {else}
                                <span class="text-muted">信息已删除或不存在</span>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">举报类型</label>
                        <div class="detail-value">
                            <span class="category-tag {if $report.type == '诈骗信息' || $report.type == '违法信息'}danger{elseif $report.type == '虚假信息' || $report.type == '广告信息' || $report.type == '违规信息'}warning{else}secondary{/if}">
                                {$report.type}
                            </span>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">联系方式</label>
                        <div class="detail-value">{$report.tel|default:'未提供'}</div>
                    </div>

                    <div class="detail-item full-width">
                        <label class="detail-label">举报内容</label>
                        <div class="detail-value">
                            <div class="content-box">
                                {$report.content}
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">提交时间</label>
                        <div class="detail-value">{$report.created_at}</div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">处理状态</label>
                        <div class="detail-value">
                            <span class="status-tag {if $report.status == 0}warning{else}success{/if}">
                                {if $report.status == 0}未处理{else}已处理{/if}
                            </span>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 状态管理 -->
            <div class="detail-section">
                <h4 class="section-title">
                    <i class="fas fa-cog"></i>
                    状态管理
                </h4>

                <form action="report.php?action=update_status" method="post">
                    <input type="hidden" name="id" value="{$report.id}">

                    <div class="form-group">
                        <label class="form-label">处理状态</label>
                        <div class="form-field">
                            <select name="status" class="form-select">
                                <option value="0" {if $report.status == 0}selected{/if}>未处理</option>
                                <option value="1" {if $report.status == 1}selected{/if}>已处理</option>
                            </select>
                        </div>
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            <span class="help-text">修改处理状态后需点击"更新状态"按钮保存</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check"></i>
                            <span>更新状态</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- 被举报信息预览 -->
            {if $post}
            <div class="detail-section">
                <h4 class="section-title">
                    <i class="fas fa-file-alt"></i>
                    被举报信息
                </h4>

                <div class="detail-grid">
                    <div class="detail-item">
                        <label class="detail-label">信息ID</label>
                        <div class="detail-value">#{$post.id}</div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">发布者</label>
                        <div class="detail-value">{$post.username|default:'匿名用户'}</div>
                    </div>

                    <div class="detail-item full-width">
                        <label class="detail-label">信息标题</label>
                        <div class="detail-value">{$post.title}</div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">发布时间</label>
                        <div class="detail-value">{$post.created_at}</div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">信息状态</label>
                        <div class="detail-value">
                            {if $post.status == 1}
                            <span class="status-tag success">正常</span>
                            {elseif $post.status == 0}
                            <span class="status-tag danger">已下架</span>
                            {else}
                            <span class="status-tag secondary">未知</span>
                            {/if}
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="../{$post.category_pinyin}/{$post.id}.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i>
                        <span>查看原信息</span>
                    </a>
                    <a href="info.php?action=edit&id={$post.id}" target="_blank" class="btn btn-outline">
                        <i class="fas fa-edit"></i>
                        <span>编辑信息</span>
                    </a>
                </div>
            </div>
            {else}
            <div class="detail-section">
                <h4 class="section-title">
                    <i class="fas fa-file-alt"></i>
                    被举报信息
                </h4>
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>信息不存在</h3>
                    <p>被举报的信息已被删除或不存在</p>
                </div>
            </div>
            {/if}

            <!-- 操作按钮 -->
            <div class="form-actions">
                <a href="report.php?action=toggle_status&id={$report.id}" class="btn {if $report.status == 0}btn-success{else}btn-warning{/if}">
                    <i class="fas {if $report.status == 0}fa-check{else}fa-undo{/if}"></i>
                    <span>{if $report.status == 0}标记为已处理{else}标记为未处理{/if}</span>
                </a>

                {if $report.post_id > 0}
                <a href="info.php?action=edit&id={$report.post_id}" target="_blank" class="btn btn-primary">
                    <i class="fas fa-edit"></i>
                    <span>编辑被举报信息</span>
                </a>
                {/if}

                <a href="report.php?action=delete&id={$report.id}" class="btn btn-danger" onclick="return confirm('确定要删除这条举报记录吗？此操作不可撤销！')">
                    <i class="fas fa-trash"></i>
                    <span>删除举报</span>
                </a>

                <a href="report.php" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回列表</span>
                </a>
            </div>
        </div>
    </div>
</div>

{include file="footer.htm"}