{if !empty($posts)}
    {foreach $posts as $post}
    <?php
    // 检查是否显示置顶标记（需要检查过期时间）
    $current_time = time();
    $show_top_tag = false;

    // 检查大分类置顶
    if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
        if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
            $show_top_tag = true;
        }
    }

    // 检查小分类置顶
    if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
        if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
            $show_top_tag = true;
        }
    }
    ?>
    <div class="post-item <?php if ($show_top_tag) echo 'is-top'; ?>">
        <div class="post-image">
            {if !empty($post.image_url)}
            <img src="{$post.image_url}" alt="{$post.title}">
            {else}
            <img src="/static/images/no-image.png" alt="无图片">
            {/if}
        </div>
        <div class="post-content">
            <?php
            $days = getRemainingDaysInt($post['expired_at']);
            if (!empty($post['expired_at']) && $days <= 0):
            ?>
            <a href="/{$category.pinyin}/{$post.id}.html" class="post-title post-expired">
                <?php if ($show_top_tag): ?><span class="top-tag">顶</span><?php endif; ?>
                {$post.title}
            </a>
            <?php else: ?>
            <a href="/{$category.pinyin}/{$post.id}.html" class="post-title">
                <?php if ($show_top_tag): ?><span class="top-tag">顶</span><?php endif; ?>
                {$post.title}
            </a>
            <?php endif; ?>
            <div class="post-info">
                <div class="post-meta">
                    {if !empty($post.region_name)}
                    <div class="post-region">{$post.region_name}</div>
                    {/if}
                    <div class="post-expire"><?php 
                        if (!empty($post['expired_at'])) {
                            $days = getRemainingDaysInt($post['expired_at']);
                            echo $days > 0 ? '剩余'.$days.'天' : '已过期';
                        } else {
                            echo '长期';
                        }
                    ?></div>
                </div>
                <div class="post-time">{$post.updated_at|friendlyTime}</div>
            </div>
        </div>
    </div>
    {/foreach}
{/if}
