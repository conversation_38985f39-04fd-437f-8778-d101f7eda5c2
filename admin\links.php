<?php
/**
 * 友情链接管理
 * 面向过程编程，支持PHP7.3-PHP8.2
 */

// 开启输出缓冲
ob_start();

// 定义常量
define('IN_BTMPS', true);

// 引入公共文件
require_once '../include/common.inc.php';

// 检查管理员权限
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header('Location: login.php');
    exit;
}

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

/**
 * 获取友情链接列表
 */
function getLinks($page = 1, $limit = 20) {
    global $db;

    $offset = ($page - 1) * $limit;

    // 获取总数
    $count_sql = "SELECT COUNT(*) as total FROM links";
    $count_result = $db->query($count_sql);
    $count_row = $db->fetch_array($count_result);
    $total = $count_row['total'];

    // 获取列表数据
    $sql = "SELECT * FROM links ORDER BY sort_order ASC, id DESC LIMIT {$offset}, {$limit}";
    $result = $db->query($sql);

    $links = array();
    while ($row = $db->fetch_array($result)) {
        $links[] = $row;
    }

    return array(
        'list' => $links,
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => ceil($total / $limit)
    );
}

/**
 * 添加友情链接
 */
function addLink($data) {
    global $db;

    $name = trim($data['name']);
    $url = trim($data['url']);
    $description = trim($data['description']);
    $sort_order = intval($data['sort_order']);
    $target = trim($data['target']);
    $status = intval($data['status']);
    $created_at = time();
    $updated_at = time(); // 添加updated_at字段

    $sql = "INSERT INTO links (name, url, description, sort_order, target, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $params = array($name, $url, $description, $sort_order, $target, $status, $created_at, $updated_at);

    return $db->query($sql, $params);
}

/**
 * 更新友情链接
 */
function updateLink($id, $data) {
    global $db;

    $name = trim($data['name']);
    $url = trim($data['url']);
    $description = trim($data['description']);
    $sort_order = intval($data['sort_order']);
    $target = trim($data['target']);
    $status = intval($data['status']);
    $updated_at = time();

    $sql = "UPDATE links SET name = ?, url = ?, description = ?, sort_order = ?, target = ?, status = ?, updated_at = ? WHERE id = ?";
    $params = array($name, $url, $description, $sort_order, $target, $status, $updated_at, $id);

    return $db->query($sql, $params);
}

/**
 * 获取单个友情链接
 */
function getLink($id) {
    global $db;

    $sql = "SELECT * FROM links WHERE id = ?";
    $result = $db->query($sql, array($id));

    return $db->fetch_array($result);
}

/**
 * 删除友情链接
 */
function deleteLink($id) {
    global $db;

    $sql = "DELETE FROM links WHERE id = ?";
    return $db->query($sql, array($id));
}

/**
 * 批量删除友情链接
 */
function batchDeleteLinks($ids) {
    global $db;

    if (empty($ids) || !is_array($ids)) {
        return false;
    }

    $ids = array_map('intval', $ids);
    $ids = array_filter($ids, function($id) { return $id > 0; });

    if (empty($ids)) {
        return false;
    }

    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
    $sql = "DELETE FROM links WHERE id IN ({$placeholders})";

    return $db->query($sql, $ids);
}

/**
 * 切换友情链接状态
 */
function toggleLinkStatus($id) {
    global $db;

    $sql = "UPDATE links SET status = 1 - status, updated_at = ? WHERE id = ?";
    return $db->query($sql, array(time(), $id));
}

// 处理AJAX请求
if (isset($_GET['ajax']) && $_GET['ajax'] == '1') {
    // 关闭错误显示，防止破坏JSON格式
    ini_set('display_errors', 'Off');
    // 清除任何之前的输出
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');

    $response = array('success' => false, 'message' => '');

    try {
        switch ($action) {
            case 'add':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $name = trim($_POST['name']);
                    $url = trim($_POST['url']);

                    if (empty($name)) {
                        $response['message'] = '友情链接名称不能为空';
                    } elseif (empty($url)) {
                        $response['message'] = '友情链接地址不能为空';
                    } else {
                        $data = array(
                            'name' => $name,
                            'url' => $url,
                            'description' => trim($_POST['description']),
                            'sort_order' => intval($_POST['sort_order']),
                            'target' => trim($_POST['target']),
                            'status' => intval($_POST['status'])
                        );

                        if (addLink($data)) {
                            $response['success'] = true;
                            $response['message'] = '添加成功';
                        } else {
                            $response['message'] = '添加失败，请重试';
                        }
                    }
                } else {
                    $response['message'] = '无效的请求方式';
                }
                break;

            case 'edit':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $id = intval($_POST['id']);
                    $name = trim($_POST['name']);
                    $url = trim($_POST['url']);

                    if ($id <= 0) {
                        $response['message'] = '无效的链接ID';
                    } elseif (empty($name)) {
                        $response['message'] = '友情链接名称不能为空';
                    } elseif (empty($url)) {
                        $response['message'] = '友情链接地址不能为空';
                    } else {
                        $data = array(
                            'name' => $name,
                            'url' => $url,
                            'description' => trim($_POST['description']),
                            'sort_order' => intval($_POST['sort_order']),
                            'target' => trim($_POST['target']),
                            'status' => intval($_POST['status'])
                        );

                        if (updateLink($id, $data)) {
                            $response['success'] = true;
                            $response['message'] = '更新成功';
                        } else {
                            $response['message'] = '更新失败，请重试';
                        }
                    }
                } else {
                    $response['message'] = '无效的请求方式';
                }
                break;

            case 'get':
                $id = intval($_GET['id']);
                if ($id > 0) {
                    $link = getLink($id);
                    if ($link) {
                        $response['success'] = true;
                        $response['data'] = $link;
                    } else {
                        $response['message'] = '友情链接不存在';
                    }
                } else {
                    $response['message'] = '无效的链接ID';
                }
                break;

            case 'delete':
                $id = intval($_GET['id']);
                if ($id > 0) {
                    if (deleteLink($id)) {
                        $response['success'] = true;
                        $response['message'] = '删除成功';
                    } else {
                        $response['message'] = '删除失败，请重试';
                    }
                } else {
                    $response['message'] = '无效的链接ID';
                }
                break;

            case 'batch_delete':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $input = json_decode(file_get_contents('php://input'), true);
                    $ids = isset($input['ids']) ? $input['ids'] : array();

                    if (empty($ids)) {
                        $response['message'] = '请选择要删除的项目';
                    } else {
                        if (batchDeleteLinks($ids)) {
                            $response['success'] = true;
                            $response['message'] = '批量删除成功';
                        } else {
                            $response['message'] = '批量删除失败，请重试';
                        }
                    }
                } else {
                    $response['message'] = '无效的请求方式';
                }
                break;

            case 'toggle_status':
                $id = intval($_GET['id']);
                if ($id > 0) {
                    if (toggleLinkStatus($id)) {
                        $response['success'] = true;
                        $response['message'] = '状态切换成功';
                    } else {
                        $response['message'] = '状态切换失败，请重试';
                    }
                } else {
                    $response['message'] = '无效的链接ID';
                }
                break;

            default:
                $response['message'] = '无效的操作';
                break;
        }
    } catch (Exception $e) {
        $response['message'] = '系统错误：' . $e->getMessage();
    }

    echo json_encode($response);
    exit;
}

// 处理普通页面请求
switch ($action) {
    case 'list':
    default:
        // 获取当前页码
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);

        // 获取友情链接列表
        $result = getLinks($page, 20);

        // 构建分页信息
        $pagination = array(
            'current_page' => $page,
            'total_pages' => $result['total_pages'],
            'total_items' => $result['total'],
            'has_prev' => $page > 1,
            'has_next' => $page < $result['total_pages'],
            'prev_page' => $page - 1,
            'next_page' => $page + 1
        );

        // 分配模板变量
        $tpl->assign('links', $result['list']);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('page_title', '友情链接管理');

        // 显示模板
        $tpl->display('links_list.htm');
        break;
}
?>
        // 清除任何之前的输出
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '无效的请求方式', 'method' => $_SERVER['REQUEST_METHOD']]);
            exit;
        }

        $name = trim($_POST['name']);
        $url = trim($_POST['url']);
        $sort_order = intval($_POST['sort_order']);

        // 验证数据
        if (empty($name)) {
            echo json_encode(['success' => false, 'error' => '友情链接名称不能为空']);
            exit;
        }

        if (empty($url)) {
            echo json_encode(['success' => false, 'error' => '友情链接地址不能为空']);
            exit;
        }

        // 插入数据库
        $sql = "INSERT INTO links (name, url, sort_order) VALUES (?, ?, ?)";
        $params = [$name, $url, $sort_order];

        try {
            if ($db->query($sql, $params)) {
                echo json_encode(['success' => true, 'message' => '添加成功', 'id' => $db->insert_id()]);
            } else {
                echo json_encode(['success' => false, 'error' => '数据库操作失败：' . $db->error()]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => '系统错误：' . $e->getMessage()]);
        }
        exit;
        break;

    // 更新友情链接
    case 'update':
        // 关闭错误显示，防止破坏JSON格式
        ini_set('display_errors', 'Off');
        // 清除任何之前的输出
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '无效的请求方式']);
            exit;
        }

        $id = intval($_POST['id']);
        $name = trim($_POST['name']);
        $url = trim($_POST['url']);
        $sort_order = intval($_POST['sort_order']);

        // 验证数据
        if ($id <= 0) {
            echo json_encode(['success' => false, 'error' => '无效的链接ID']);
            exit;
        }

        if (empty($name)) {
            echo json_encode(['success' => false, 'error' => '友情链接名称不能为空']);
            exit;
        }

        if (empty($url)) {
            echo json_encode(['success' => false, 'error' => '友情链接地址不能为空']);
            exit;
        }

        // 更新数据库
        $sql = "UPDATE links SET name = ?, url = ?, sort_order = ? WHERE id = ?";
        $params = [$name, $url, $sort_order, $id];

        if ($db->query($sql, $params)) {
            echo json_encode(['success' => true, 'message' => '更新成功']);
        } else {
            echo json_encode(['success' => false, 'error' => '更新失败，请重试']);
        }
        exit;
        break;

    // 批量删除友情链接
    case 'batch_delete':
        // 关闭错误显示，防止破坏JSON格式
        ini_set('display_errors', 'Off');
        // 清除任何之前的输出
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '无效的请求方式']);
            exit;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $ids = isset($input['ids']) ? $input['ids'] : [];

        if (empty($ids) || !is_array($ids)) {
            echo json_encode(['success' => false, 'error' => '请选择要删除的项目']);
            exit;
        }

        // 验证ID
        $ids = array_map('intval', $ids);
        $ids = array_filter($ids, function($id) { return $id > 0; });

        if (empty($ids)) {
            echo json_encode(['success' => false, 'error' => '无效的ID']);
            exit;
        }

        // 删除数据
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "DELETE FROM links WHERE id IN ($placeholders)";

        if ($db->query($sql, $ids)) {
            echo json_encode(['success' => true, 'message' => '删除成功']);
        } else {
            echo json_encode(['success' => false, 'error' => '删除失败，请重试']);
        }
        exit;
        break;

    // AJAX保存友情链接
    case 'save':
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'error' => '无效的请求方式']);
            exit;
        }

        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $result = save_link($id);

        echo json_encode($result);
        exit;
        break;

    // 获取单个友情链接数据
    case 'get':
        // 关闭错误显示，防止破坏JSON格式
        ini_set('display_errors', 'Off');
        // 清除任何之前的输出
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');

        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            echo json_encode(['success' => false, 'error' => '无效的链接ID']);
            exit;
        }

        $sql = "SELECT * FROM links WHERE id = ?";
        $result = $db->query($sql, [$id]);
        $link_data = $db->fetch_array($result);

        if ($link_data) {
            echo json_encode(['success' => true, 'data' => $link_data]);
        } else {
            echo json_encode(['success' => false, 'error' => '友情链接不存在']);
        }
        exit;
        break;

    // 删除友情链接
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id > 0) {
            $sql = "DELETE FROM links WHERE id = ?";
            $result = $db->query($sql, [$id]);
            
            if ($result) {
                // 清除友情链接缓存
                clearFriendLinksCache();
                $message = '友情链接删除成功';
            } else {
                $message = '友情链接删除失败';
            }
        } else {
            $message = '无效的友情链接ID';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    // 批量删除
    case 'batch_delete':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $link_ids = isset($_POST['link_ids']) ? $_POST['link_ids'] : [];
            
            if (!empty($link_ids)) {
                $ids = array_map('intval', $link_ids);
                $ids_str = implode(',', $ids);
                
                $sql = "DELETE FROM links WHERE id IN ({$ids_str})";
                $result = $db->query($sql);
                
                if ($result) {
                    // 清除友情链接缓存
                    clearFriendLinksCache();
                    $message = '批量删除成功，共删除 ' . count($ids) . ' 个友情链接';
                } else {
                    $message = '批量删除失败';
                }
            } else {
                $message = '请选择要删除的友情链接';
            }
        } else {
            $message = '无效的请求方式';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    // 更新状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id > 0) {
            // 获取当前状态
            $sql = "SELECT status FROM links WHERE id = ?";
            $result = $db->query($sql, [$id]);
            $row = $db->fetch_array($result);
            
            if ($row) {
                $new_status = $row['status'] == 1 ? 0 : 1;
                $update_sql = "UPDATE links SET status = ? WHERE id = ?";
                $update_result = $db->query($update_sql, [$new_status, $id]);
                
                if ($update_result) {
                    // 清除友情链接缓存
                    clearFriendLinksCache();
                    $message = '状态更新成功';
                } else {
                    $message = '状态更新失败';
                }
            } else {
                $message = '友情链接不存在';
            }
        } else {
            $message = '无效的友情链接ID';
        }
        
        header("Location: links.php?message=" . urlencode($message));
        exit;
        break;
        
    default:
        header('Location: links.php?action=list');
        exit;
}

/**
 * 保存友情链接信息
 */
function save_link($id = 0) {
    global $db;
    
    // 获取表单数据
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $url = isset($_POST['url']) ? trim($_POST['url']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
    $target = isset($_POST['target']) ? trim($_POST['target']) : '_blank';
    
    // 验证必填字段
    if (empty($name)) {
        return ['success' => false, 'error' => '请输入链接名称'];
    }
    
    if (empty($url)) {
        return ['success' => false, 'error' => '请输入链接地址'];
    }
    
    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return ['success' => false, 'error' => '请输入有效的链接地址'];
    }
    
    // 检查链接名称是否已存在
    $check_sql = "SELECT id FROM links WHERE name = ?";
    if ($id > 0) {
        $check_sql .= " AND id != ?";
        $check_result = $db->query($check_sql, [$name, $id]);
    } else {
        $check_result = $db->query($check_sql, [$name]);
    }
    
    if ($db->num_rows($check_result) > 0) {
        return ['success' => false, 'error' => '链接名称已存在'];
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        $now = time();
        
        if ($id > 0) {
            // 更新现有友情链接
            $sql = "UPDATE links SET name = ?, url = ?, description = ?, sort_order = ?, status = ?, target = ?, updated_at = ? WHERE id = ?";
            $result = $db->query($sql, [$name, $url, $description, $sort_order, $status, $target, $now, $id]);
        } else {
            // 添加新友情链接
            $sql = "INSERT INTO links (name, url, description, sort_order, status, target, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $result = $db->query($sql, [$name, $url, $description, $sort_order, $status, $target, $now, $now]);
        }
        
        if (!$result) {
            throw new Exception('保存友情链接失败');
        }
        
        // 提交事务
        $db->commit();

        // 清除友情链接缓存
        clearFriendLinksCache();

        return ['success' => true];
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
