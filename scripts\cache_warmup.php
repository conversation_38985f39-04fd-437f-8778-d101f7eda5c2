<?php
/**
 * 缓存预热脚本
 * 用于定时任务自动预热网站缓存
 * 
 * 使用方法：
 * 1. 命令行执行：php cache_warmup.php
 * 2. 定时任务：0 */6 /* * * /usr/bin/php /path/to/cache_warmup.php
 * 3. Web访问：http://yourdomain.com/scripts/cache_warmup.php?key=your_secret_key
 */

// 定义安全常量
define('IN_BTMPS', true);

// 设置脚本执行时间限制
set_time_limit(300); // 5分钟
ini_set('memory_limit', '256M');

// 获取项目根目录
$root_path = dirname(__DIR__) . '/';
define('ROOT_PATH', $root_path);
define('INCLUDE_PATH', ROOT_PATH . 'include/');
define('DATA_PATH', ROOT_PATH . 'data/');

// 加载必要的文件
require_once(ROOT_PATH . 'config/config.db.php');
require_once(ROOT_PATH . 'config/config.inc.php');
require_once(INCLUDE_PATH . 'mysql.class.php');
require_once(INCLUDE_PATH . 'cache.class.php');
require_once(INCLUDE_PATH . 'global.fun.php');

/**
 * 缓存预热管理类
 */
class CacheWarmup {
    private $db;
    private $config;
    private $log_file;
    private $start_time;
    private $stats = [
        'total_pages' => 0,
        'success_pages' => 0,
        'failed_pages' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0
    ];
    
    public function __construct() {
        global $config;
        $this->config = $config;
        $this->start_time = microtime(true);
        $this->log_file = DATA_PATH . 'logs/cache_warmup_' . date('Y-m-d') . '.log';
        
        // 确保日志目录存在
        $log_dir = dirname($this->log_file);
        if (!is_dir($log_dir)) {
            @mkdir($log_dir, 0755, true);
        }
        
        // 初始化数据库连接
        $this->initDatabase();
        
        $this->log("缓存预热任务开始执行");
    }
    
    /**
     * 初始化数据库连接
     */
    private function initDatabase() {
        global $config;
        $this->db = new MySQL();
        $this->db->connect(
            $config['db_host'],
            $config['db_user'], 
            $config['db_pass'],
            $config['db_name'],
            $config['db_charset'],
            $config['db_port']
        );
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[{$timestamp}] {$message}" . PHP_EOL;
        
        // 写入日志文件
        @file_put_contents($this->log_file, $log_message, FILE_APPEND | LOCK_EX);
        
        // 如果是命令行模式，同时输出到控制台
        if (php_sapi_name() === 'cli') {
            echo $log_message;
        }
    }
    
    /**
     * 执行缓存预热
     */
    public function warmup() {
        $this->log("开始缓存预热...");
        
        // 1. 预热基础数据缓存
        $this->warmupBaseData();
        
        // 2. 预热首页缓存
        $this->warmupHomePage();
        
        // 3. 预热分类页面缓存
        $this->warmupCategoryPages();
        
        // 4. 预热热门信息详情页
        $this->warmupPopularPosts();
        
        // 5. 预热地区页面缓存
        $this->warmupRegionPages();
        
        // 6. 清理过期缓存
        $this->cleanExpiredCache();
        
        $this->log("缓存预热完成");
        $this->outputStats();
    }
    
    /**
     * 预热基础数据缓存
     */
    private function warmupBaseData() {
        $this->log("预热基础数据缓存...");
        
        try {
            // 预热分类数据
            $categories = getCachedCategories(true);
            $this->stats['cache_misses']++;
            $this->log("分类数据缓存已预热，共 " . count($categories) . " 个分类");
            
            // 预热地区数据
            $regions = getCachedRegions(true);
            $this->stats['cache_misses']++;
            $this->log("地区数据缓存已预热，共 " . count($regions) . " 个地区");
            
        } catch (Exception $e) {
            $this->log("预热基础数据失败: " . $e->getMessage());
            $this->stats['failed_pages']++;
        }
    }
    
    /**
     * 预热首页缓存
     */
    private function warmupHomePage() {
        $this->log("预热首页缓存...");
        
        try {
            $cache_key = 'index_page_data';
            $cache_enable = isset($this->config['cache_enable']) ? intval($this->config['cache_enable']) : 1;
            $cache_index_time = isset($this->config['cache_index']) ? intval($this->config['cache_index']) : 3600;
            
            if ($cache_enable && $cache_index_time > 0) {
                // 删除现有缓存，强制重新生成
                cache_delete($cache_key);
                
                // 模拟首页数据获取
                $this->generateHomePageCache();
                $this->stats['success_pages']++;
                $this->stats['cache_misses']++;
                $this->log("首页缓存预热成功");
            }
        } catch (Exception $e) {
            $this->log("预热首页缓存失败: " . $e->getMessage());
            $this->stats['failed_pages']++;
        }
    }
    
    /**
     * 生成首页缓存数据
     */
    private function generateHomePageCache() {
        global $GLOBALS;
        
        $cache_key = 'index_page_data';
        $cache_index_time = isset($this->config['cache_index']) ? intval($this->config['cache_index']) : 3600;
        $indexSize = isset($this->config['index_size']) ? intval($this->config['index_size']) : 120;
        
        // 获取分类数据
        $categories = getCachedCategories();
        
        // 获取置顶信息
        $sql = "SELECT * FROM posts WHERE status = 1 AND is_top = 1 AND expired_at > ? ORDER BY top_level DESC, created_at DESC LIMIT 10";
        $topPosts = $this->db->query($sql, [time()]);
        
        // 获取最新信息
        $sql = "SELECT * FROM posts WHERE status = 1 AND is_top = 0 AND expired_at > ? ORDER BY created_at DESC LIMIT ?";
        $normalPosts = $this->db->query($sql, [time(), $indexSize]);
        
        // 缓存数据
        $cache_data = [
            'categories' => $categories,
            'topPosts' => $topPosts,
            'normalPosts' => $normalPosts,
            'generated_at' => time()
        ];
        
        cache_set($cache_key, $cache_data, $cache_index_time);
    }
    
    /**
     * 预热分类页面缓存
     */
    private function warmupCategoryPages() {
        $this->log("预热分类页面缓存...");
        
        try {
            $categories = getCachedCategories();
            $cache_category_time = isset($this->config['cache_category']) ? intval($this->config['cache_category']) : 7200;
            
            if ($cache_category_time > 0) {
                $count = 0;
                foreach ($categories as $category) {
                    if (isset($category['pinyin']) && !empty($category['pinyin'])) {
                        $this->generateCategoryPageCache($category['pinyin'], $category['id']);
                        $count++;
                        
                        // 避免过度占用资源，每处理10个分类休息一下
                        if ($count % 10 == 0) {
                            usleep(100000); // 休息0.1秒
                        }
                    }
                }
                
                $this->stats['success_pages'] += $count;
                $this->stats['cache_misses'] += $count;
                $this->log("分类页面缓存预热完成，共处理 {$count} 个分类");
            }
        } catch (Exception $e) {
            $this->log("预热分类页面缓存失败: " . $e->getMessage());
            $this->stats['failed_pages']++;
        }
    }
    
    /**
     * 生成分类页面缓存
     */
    private function generateCategoryPageCache($pinyin, $categoryId) {
        $cache_key = "category_posts_{$pinyin}_1_0"; // 第一页，所有地区
        $cache_category_time = isset($this->config['cache_category']) ? intval($this->config['cache_category']) : 7200;
        $pageSize = isset($this->config['list_page_size']) ? intval($this->config['list_page_size']) : 20;
        
        // 获取分类下的信息
        $sql = "SELECT * FROM posts WHERE category_id = ? AND status = 1 AND expired_at > ? ORDER BY is_top DESC, created_at DESC LIMIT ?";
        $posts = $this->db->query($sql, [$categoryId, time(), $pageSize]);
        
        // 获取总数
        $sql = "SELECT COUNT(*) as total FROM posts WHERE category_id = ? AND status = 1 AND expired_at > ?";
        $result = $this->db->get_one($sql, [$categoryId, time()]);
        $total = $result ? $result['total'] : 0;
        
        $cache_data = [
            'posts' => $posts,
            'total' => $total,
            'category_id' => $categoryId,
            'generated_at' => time()
        ];
        
        cache_set($cache_key, $cache_data, $cache_category_time);
    }
    
    /**
     * 预热热门信息详情页
     */
    private function warmupPopularPosts() {
        $this->log("预热热门信息详情页缓存...");
        
        try {
            $cache_post_time = isset($this->config['cache_post']) ? intval($this->config['cache_post']) : 1800;
            
            if ($cache_post_time > 0) {
                // 获取最近7天内浏览量最高的信息
                $sql = "SELECT id FROM posts WHERE status = 1 AND expired_at > ? AND created_at > ? ORDER BY views DESC LIMIT 50";
                $recent_time = time() - (7 * 24 * 3600); // 7天前
                $posts = $this->db->query($sql, [time(), $recent_time]);
                
                $count = 0;
                foreach ($posts as $post) {
                    $this->generatePostDetailCache($post['id']);
                    $count++;
                    
                    // 控制处理速度
                    if ($count % 5 == 0) {
                        usleep(50000); // 休息0.05秒
                    }
                }
                
                $this->stats['success_pages'] += $count;
                $this->stats['cache_misses'] += $count;
                $this->log("热门信息详情页缓存预热完成，共处理 {$count} 条信息");
            }
        } catch (Exception $e) {
            $this->log("预热热门信息详情页失败: " . $e->getMessage());
            $this->stats['failed_pages']++;
        }
    }
    
    /**
     * 生成信息详情页缓存
     */
    private function generatePostDetailCache($postId) {
        $cache_key = "post_detail_{$postId}";
        $cache_post_time = isset($this->config['cache_post']) ? intval($this->config['cache_post']) : 1800;
        
        // 获取信息详情
        $sql = "SELECT p.*, c.name as category_name, r.name as region_name 
                FROM posts p 
                LEFT JOIN categories c ON p.category_id = c.id 
                LEFT JOIN regions r ON p.region_id = r.id 
                WHERE p.id = ? AND p.status = 1";
        $post = $this->db->get_one($sql, [$postId]);
        
        if ($post) {
            cache_set($cache_key, $post, $cache_post_time);
        }
    }
    
    /**
     * 预热地区页面缓存
     */
    private function warmupRegionPages() {
        $this->log("预热地区页面缓存...");
        
        try {
            $regions = getCachedRegions();
            $cache_region_time = isset($this->config['cache_region']) ? intval($this->config['cache_region']) : 7200;
            
            if ($cache_region_time > 0) {
                $count = 0;
                foreach ($regions as $region) {
                    if (isset($region['id']) && $region['parent_id'] == 0) { // 只处理主要地区
                        $this->generateRegionPageCache($region['id']);
                        $count++;
                        
                        if ($count % 5 == 0) {
                            usleep(100000); // 休息0.1秒
                        }
                    }
                }
                
                $this->stats['success_pages'] += $count;
                $this->stats['cache_misses'] += $count;
                $this->log("地区页面缓存预热完成，共处理 {$count} 个地区");
            }
        } catch (Exception $e) {
            $this->log("预热地区页面缓存失败: " . $e->getMessage());
            $this->stats['failed_pages']++;
        }
    }
    
    /**
     * 生成地区页面缓存
     */
    private function generateRegionPageCache($regionId) {
        $cache_key = "region_posts_{$regionId}_1"; // 第一页
        $cache_region_time = isset($this->config['cache_region']) ? intval($this->config['cache_region']) : 7200;
        $pageSize = isset($this->config['list_page_size']) ? intval($this->config['list_page_size']) : 20;
        
        // 获取地区下的信息
        $sql = "SELECT * FROM posts WHERE region_id = ? AND status = 1 AND expired_at > ? ORDER BY is_top DESC, created_at DESC LIMIT ?";
        $posts = $this->db->query($sql, [$regionId, time(), $pageSize]);
        
        $cache_data = [
            'posts' => $posts,
            'region_id' => $regionId,
            'generated_at' => time()
        ];
        
        cache_set($cache_key, $cache_data, $cache_region_time);
    }
    
    /**
     * 清理过期缓存
     */
    private function cleanExpiredCache() {
        $this->log("清理过期缓存...");
        
        try {
            $cache_dir = DATA_PATH . 'cache/';
            $files = glob($cache_dir . '*.cache');
            $cleaned = 0;
            
            foreach ($files as $file) {
                if (file_exists($file)) {
                    $cache_data = @include $file;
                    if (is_array($cache_data) && isset($cache_data['expire'])) {
                        if (time() > $cache_data['expire']) {
                            if (@unlink($file)) {
                                $cleaned++;
                            }
                        }
                    }
                }
            }
            
            $this->log("清理过期缓存完成，共清理 {$cleaned} 个文件");
        } catch (Exception $e) {
            $this->log("清理过期缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 输出统计信息
     */
    private function outputStats() {
        $execution_time = round(microtime(true) - $this->start_time, 2);
        $this->stats['total_pages'] = $this->stats['success_pages'] + $this->stats['failed_pages'];
        
        $this->log("=== 缓存预热统计 ===");
        $this->log("执行时间: {$execution_time} 秒");
        $this->log("总页面数: {$this->stats['total_pages']}");
        $this->log("成功页面: {$this->stats['success_pages']}");
        $this->log("失败页面: {$this->stats['failed_pages']}");
        $this->log("缓存命中: {$this->stats['cache_hits']}");
        $this->log("缓存未命中: {$this->stats['cache_misses']}");
        
        if ($this->stats['total_pages'] > 0) {
            $success_rate = round(($this->stats['success_pages'] / $this->stats['total_pages']) * 100, 2);
            $this->log("成功率: {$success_rate}%");
        }
    }
}

// 安全检查
$secret_key = 'your_secret_key_here'; // 请修改为您的密钥
$is_cli = php_sapi_name() === 'cli';
$is_authorized = false;

if ($is_cli) {
    // 命令行模式直接允许
    $is_authorized = true;
} elseif (isset($_GET['key']) && $_GET['key'] === $secret_key) {
    // Web模式需要密钥验证
    $is_authorized = true;
    header('Content-Type: text/plain; charset=utf-8');
}

if (!$is_authorized) {
    http_response_code(403);
    die('Access Denied');
}

// 执行缓存预热
try {
    $warmup = new CacheWarmup();
    $warmup->warmup();
} catch (Exception $e) {
    error_log("缓存预热失败: " . $e->getMessage());
    if ($is_cli) {
        echo "缓存预热失败: " . $e->getMessage() . PHP_EOL;
    } else {
        echo "缓存预热失败: " . $e->getMessage();
    }
    exit(1);
}
