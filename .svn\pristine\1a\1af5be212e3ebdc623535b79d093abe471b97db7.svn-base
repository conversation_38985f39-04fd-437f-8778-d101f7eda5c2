{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>
        <i class="fas fa-flag"></i>
        举报管理
    </h1>
    <div class="page-actions">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回首页</span>
        </a>
    </div>
</div>











<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    {if $message}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <strong>操作成功</strong>
            <p>{$message}</p>
        </div>
    </div>
    {/if}

    <!-- 举报管理 -->
    <div class="card">
        <!-- 状态选项卡 -->
        <div class="filter-tabs">
            <a href="report.php?status=0" class="filter-tab {if !isset($status) || $status == 0}active{/if}">
                <i class="fas fa-exclamation-circle"></i>
                <span>未处理</span>
                {if isset($status_counts.pending) && $status_counts.pending > 0}
                <span class="tab-badge">{$status_counts.pending}</span>
                {/if}
            </a>
            <a href="report.php?status=1" class="filter-tab {if isset($status) && $status == 1}active{/if}">
                <i class="fas fa-check-circle"></i>
                <span>已处理</span>
            </a>
            <a href="report.php?status=-1" class="filter-tab {if isset($status) && $status == -1}active{/if}">
                <i class="fas fa-list"></i>
                <span>全部举报</span>
            </a>
        </div>

        <!-- 筛选面板 -->
        <div class="filter-panel">
            <form action="report.php" method="get" class="filter-form">
                <input type="hidden" name="status" value="{if isset($status)}{$status}{else}0{/if}">

                <div class="filter-group">
                    <label class="filter-label">举报类型</label>
                    <select name="type" class="form-select">
                        <option value="">全部类型</option>
                        <option value="虚假信息" {if isset($type) && $type == '虚假信息'}selected{/if}>虚假信息</option>
                        <option value="诈骗信息" {if isset($type) && $type == '诈骗信息'}selected{/if}>诈骗信息</option>
                        <option value="违法信息" {if isset($type) && $type == '违法信息'}selected{/if}>违法信息</option>
                        <option value="广告信息" {if isset($type) && $type == '广告信息'}selected{/if}>广告信息</option>
                        <option value="违规信息" {if isset($type) && $type == '违规信息'}selected{/if}>违规信息</option>
                        <option value="其他问题" {if isset($type) && $type == '其他问题'}selected{/if}>其他问题</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">信息ID</label>
                    <input type="number" name="post_id" value="{if isset($post_id)}{$post_id}{/if}" placeholder="输入ID" class="form-control">
                </div>

                <div class="filter-group">
                    <label class="filter-label">关键词</label>
                    <input type="text" name="keyword" value="{if isset($keyword)}{$keyword}{/if}" placeholder="搜索举报内容" class="form-control">
                </div>

                <div class="filter-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        <span>筛选</span>
                    </button>
                    <a href="report.php?status={if isset($status)}{$status}{else}0{/if}" class="btn btn-outline">
                        <i class="fas fa-undo"></i>
                        <span>重置</span>
                    </a>
                </div>
            </form>
        </div>

        <!-- 统计信息 -->
        <div class="table-stats">
            <div class="stats-left">
                <span class="stats-text">共 <strong>{$total_count|default:0}</strong> 条举报记录</span>
            </div>
            <div class="stats-right">
                {if $reports}
                <span class="stats-text">当前页显示 <strong>{$reports|count}</strong> 条</span>
                {/if}
            </div>
        </div>

        <!-- 举报列表 -->
        <form id="reportForm" action="report.php?action=batch_delete" method="post">
            <div class="table-responsive">
                <table class="table table-enhanced">
                    <thead>
                        <tr>
                            <th width="40" class="text-center">
                                <input type="checkbox" class="select-all-checkbox">
                            </th>
                            <th width="60" class="text-center">ID</th>
                            <th width="80" class="text-center">信息ID</th>
                            <th>信息标题</th>
                            <th width="100" class="text-center">举报类型</th>
                            <th width="200">举报内容</th>
                            <th width="120" class="text-center">联系方式</th>
                            <th width="140" class="text-center">提交时间</th>
                            <th width="80" class="text-center">状态</th>
                            <th width="160" class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if !$reports}
                        <tr>
                            <td colspan="10" class="text-center">
                                <div class="empty-state">
                                    <i class="fas fa-flag"></i>
                                    <h3>暂无举报数据</h3>
                                    <p>当前筛选条件下没有找到举报记录</p>
                                </div>
                            </td>
                        </tr>
                        {else}
                        {foreach $reports as $report}
                        <tr class="table-row">
                            <td class="text-center">
                                <input type="checkbox" name="report_ids[]" class="report-checkbox" value="{$report.id}">
                            </td>
                            <td class="text-center table-id">{$report.id}</td>
                            <td class="text-center">{$report.post_id}</td>
                            <td>
                                <div class="table-title">
                                    {if $report.post_id > 0 && $report.category_pinyin != ''}
                                    <a href="../{$report.category_pinyin}/{$report.post_id}.html" target="_blank" class="title-link">
                                        {$report.post_title}
                                    </a>
                                    {else}
                                    <span class="title-disabled">{$report.post_title}</span>
                                    {/if}
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="category-tag {if $report.type == '诈骗信息' || $report.type == '违法信息'}danger{elseif $report.type == '虚假信息' || $report.type == '广告信息' || $report.type == '违规信息'}warning{else}secondary{/if}">
                                    {$report.type}
                                </span>
                            </td>
                            <td>
                                <div class="table-content" title="{$report.content}">
                                    {$report.content}
                                </div>
                            </td>
                            <td class="text-center">{$report.tel|default:'--'}</td>
                            <td class="text-center table-time">{$report.created_at}</td>
                            <td class="text-center">
                                <a href="report.php?action=toggle_status&id={$report.id}" class="status-toggle" title="点击切换状态">
                                    {if $report.status == 0}
                                    <span class="status-tag warning">未处理</span>
                                    {else}
                                    <span class="status-tag success">已处理</span>
                                    {/if}
                                </a>
                            </td>
                            <td>
                                <div class="action-buttons-enhanced">
                                    {if $report.post_id > 0}
                                    <a href="info.php?action=edit&id={$report.post_id}" target="_blank" class="btn-action btn-edit">
                                        <i class="fas fa-edit"></i>
                                        <span>查看信息</span>
                                    </a>
                                    {/if}
                                    <a href="report.php?action=delete&id={$report.id}" class="btn-action btn-delete" onclick="return confirm('确定要删除这条举报记录吗？')">
                                        <i class="fas fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                        {/if}
                    </tbody>
                </table>
            </div>

            <!-- 表格底部 -->
            <div class="table-footer">
                <div class="batch-actions-bottom">
                    <label class="batch-select-all">
                        <input type="checkbox" class="select-all-bottom">
                        全选 (<span class="selected-count">0</span> 项已选中)
                    </label>
                    <div class="batch-buttons">
                        <button type="submit" class="btn btn-danger btn-sm batch-action-btn" disabled>
                            <i class="fas fa-trash"></i> 批量删除
                        </button>
                    </div>
                </div>

                <!-- 分页组件 -->
                {if $pagination.total_pages > 1}
                <div class="pagination-wrapper">
                    {include file="pagination.htm"}
                </div>
                {/if}
            </div>
        </form>
    </div>
</div>

<!-- 页面脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表格功能
    initTableSelection();
    initBatchActions();

    // 表格选择功能
    function initTableSelection() {
        const selectAllTop = document.querySelector('.select-all-checkbox');
        const selectAllBottom = document.querySelector('.select-all-bottom');
        const checkboxes = document.querySelectorAll('.report-checkbox');
        const selectedCount = document.querySelector('.selected-count');
        const batchButtons = document.querySelectorAll('.batch-action-btn');

        // 更新选中计数和按钮状态
        function updateSelection() {
            const checkedCount = document.querySelectorAll('.report-checkbox:checked').length;
            const totalCount = checkboxes.length;

            // 更新计数显示
            if (selectedCount) {
                selectedCount.textContent = checkedCount;
            }

            // 更新全选按钮状态
            const allChecked = checkedCount === totalCount && totalCount > 0;
            const someChecked = checkedCount > 0;

            if (selectAllTop) {
                selectAllTop.checked = allChecked;
                selectAllTop.indeterminate = someChecked && !allChecked;
            }
            if (selectAllBottom) {
                selectAllBottom.checked = allChecked;
                selectAllBottom.indeterminate = someChecked && !allChecked;
            }

            // 更新批量操作按钮状态
            batchButtons.forEach(btn => {
                btn.disabled = checkedCount === 0;
            });
        }

        // 全选/取消全选
        function toggleSelectAll(checked) {
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
            });
            updateSelection();
        }

        // 绑定事件
        if (selectAllTop) {
            selectAllTop.addEventListener('change', function() {
                toggleSelectAll(this.checked);
            });
        }

        if (selectAllBottom) {
            selectAllBottom.addEventListener('change', function() {
                toggleSelectAll(this.checked);
            });
        }

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelection);
        });

        // 初始化状态
        updateSelection();
    }

    // 批量操作功能
    function initBatchActions() {
        const form = document.getElementById('reportForm');
        if (!form) return;

        form.addEventListener('submit', function(e) {
            const checkedBoxes = document.querySelectorAll('.report-checkbox:checked');

            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('请至少选择一条记录进行操作');
                return false;
            }

            if (!confirm(`确定要删除选中的 ${checkedBoxes.length} 条举报记录吗？此操作不可撤销！`)) {
                e.preventDefault();
                return false;
            }

            return true;
        });
    }
});
</script>

{include file="footer.htm"}