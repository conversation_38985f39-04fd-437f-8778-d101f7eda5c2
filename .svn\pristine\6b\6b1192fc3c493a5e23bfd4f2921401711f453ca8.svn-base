{include file="header.htm"}

<style>
/* 筛选表单样式 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
    text-decoration: none;
}

.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
    text-decoration: none;
}

.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #6c757d;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #495057;
    text-decoration: none;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 3px;
}

.btn-danger {
    background-color: #dc3545;
    color: #fff;
    border: 1px solid #dc3545;
}
.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: #fff;
    text-decoration: none;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.content-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    border: 1px solid #dee2e6;
    table-layout: fixed;
}

.content-table th,
.content-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.content-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 设置列宽 */
.content-table .col-checkbox { width: 40px; }
.content-table .col-id { width: 80px; }
.content-table .col-title { width: 35%; }
.content-table .col-category { width: 120px; }
.content-table .col-time { width: 140px; }
.content-table .col-clicks { width: 80px; }
.content-table .col-actions { width: 220px; }

/* 操作按钮样式 */
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 3px;
    min-width: 200px;
}

.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}



/* 快捷操作按钮的激活状态 */
.info-actions .btn-sm.btn-light-success {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.info-actions .btn-sm.btn-light-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* 快捷操作按钮悬停效果 */
.info-actions .btn-sm:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* 加载状态的旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}

/* 标签样式 */
.label {
    display: inline-block;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 3px;
    margin-left: 5px;
}

.label-danger {
    background-color: #d9534f;
}

.label-success {
    background-color: #5cb85c;
}

.label-warning {
    background-color: #f0ad4e;
}

/* 批量操作样式 */
.batch-actions {
    padding: 20px 0;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 0 0 6px 6px;
}

.batch-actions-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.batch-actions-group:last-child {
    margin-bottom: 0;
}

.batch-actions-label {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    min-width: 80px;
    margin: 0;
    white-space: nowrap;
}

.batch-actions .btn {
    margin-bottom: 0;
}

.batch-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.batch-actions .btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.batch-actions .btn-success:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
}

.batch-actions .btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #fff;
}

.batch-actions .btn-info:hover:not(:disabled) {
    background-color: #138496;
    border-color: #117a8b;
}

.batch-actions .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.batch-actions .btn-warning:hover:not(:disabled) {
    background-color: #e0a800;
    border-color: #d39e00;
}

.batch-actions .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.batch-actions .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.batch-actions .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.batch-actions .btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
    border-color: #004085;
}

.batch-actions .btn-dark {
    background-color: #343a40;
    border-color: #343a40;
    color: #fff;
}

.batch-actions .btn-dark:hover:not(:disabled) {
    background-color: #23272b;
    border-color: #1d2124;
}

/* 快速筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
    transition: all 0.2s;
}

.filter-tag.active-all, .filter-tag.active-top, .filter-tag.active-recommend, .filter-tag.active-hidden {
    background-color: #1b68ff;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(27, 104, 255, 0.3);
}

.filter-tag.inactive-all, .filter-tag.inactive-top, .filter-tag.inactive-recommend, .filter-tag.inactive-hidden {
    background-color: #6c757d;
    font-weight: normal;
}

.filter-tag:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

/* 新闻标题链接样式 */
.news-title-link {
    color: #333;
    font-weight: 500;
    text-decoration: none !important;
}

.news-title-link:hover {
    color: #1b68ff;
    text-decoration: none !important;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-form-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .filter-form-item label {
        text-align: left;
    }

    .info-actions {
        flex-direction: column;
        gap: 3px;
    }

    .batch-actions-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .batch-actions-label {
        min-width: auto;
        text-align: center;
        margin-bottom: 5px;
    }

    .batch-actions-group .btn {
        width: 100%;
    }
}
</style>

<div class="container-fluid">
    <!-- 消息提示 -->
    {if $message}
    <div class="alert alert-success">
        {$message}
    </div>
    {/if}
    
    {if $error}
    <div class="alert alert-danger">
        {$error}
    </div>
    {/if}
    
    <!-- 新闻管理 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h3 class="card-title">新闻文章管理</h3>
            </div>
            <!-- <div>
                <a href="news.php?op=add" class="btn btn-primary btn-sm">添加文章</a>
                <a href="test_ueditor.php" class="btn btn-info btn-sm">UEditor测试</a>
            </div> -->
        </div>
        <div class="card-body">
            <!-- 快速筛选按钮 -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <a href="news.php" class="filter-tag {if !$smarty.get.is_top && !$smarty.get.is_recommend && !$smarty.get.is_show}active-all{else}inactive-all{/if}">全部文章</a>
                    <a href="news.php?is_top=1" class="filter-tag {if $smarty.get.is_top == '1'}active-top{else}inactive-top{/if}">置顶文章</a>
                    <a href="news.php?is_recommend=1" class="filter-tag {if $smarty.get.is_recommend == '1'}active-recommend{else}inactive-recommend{/if}">推荐文章</a>
                    <a href="news.php?is_show=0" class="filter-tag {if $smarty.get.is_show == '0'}active-hidden{else}inactive-hidden{/if}">隐藏文章</a>
                </div>
            </div>

            <!-- 高级筛选表单 -->
            <form action="news.php" method="get">
                <div class="filter-form">
                    <div class="filter-form-item">
                        <label>栏目:</label>
                        <select name="catid" id="catid" class="form-control" style="min-width: 150px;">
                            <option value="0">所有栏目</option>
                            {foreach from=$categories item=cat}
                            <option value="{$cat.catid}" {if $catid == $cat.catid}selected{/if}>{$cat.catname}</option>
                            {if isset($cat.children) && $cat.children}
                            {foreach from=$cat.children item=child}
                            <option value="{$child.catid}" {if $catid == $child.catid}selected{/if}>-- {$child.catname}</option>
                            {/foreach}
                            {/if}
                            {/foreach}
                        </select>
                    </div>

                    <div class="filter-form-item">
                        <label>标题:</label>
                        <input type="text" name="title" value="{$title}" placeholder="输入文章标题..." class="form-control" style="min-width: 200px;">
                    </div>

                    <div class="filter-form-item">
                        <label>ID:</label>
                        <input type="text" name="id" value="{if isset($id)}{$id}{/if}" placeholder="输入ID..." class="form-control" style="width: 80px;">
                    </div>

                    <div class="filter-form-item">
                        <button type="submit" class="btn btn-sm btn-light-primary">
                            <i class="fas fa-search"></i> 筛选
                        </button>
                        <a href="news.php" class="btn btn-sm btn-light-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </a>
                    </div>

                    <div style="margin-left: auto;">
                        <a href="news.php?op=add" class="btn btn-sm btn-light-success">
                            <i class="fas fa-plus"></i> 添加文章
                        </a>
                        <a href="javascript:if(confirm('确定要取消所有文章的置顶和推荐状态吗？此操作不可撤销！'))location='news.php?op=reset_all_status'" class="btn btn-sm btn-light-warning">
                            <i class="fas fa-undo"></i> 重置状态
                        </a>
                    </div>
                </div>
            </form>

            <!-- 新闻列表 -->
            <form id="batch-form" action="news.php?op=batch_delete" method="post">
                <div class="table-responsive">
                    <table class="content-table">
                        <thead>
                            <tr>
                                <th class="col-checkbox" style="width: 40px;"><input type="checkbox" id="check-all"></th>
                                <th class="col-id" style="width: 80px;">ID</th>
                                <th class="col-title">标题</th>
                                <th class="col-category">栏目</th>
                                <th class="col-time">发布时间</th>
                                <th class="col-clicks">点击</th>
                                <th class="col-actions">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {if $news_list}
                            {foreach from=$news_list item=news}
                            <tr>
                                <td><input type="checkbox" name="ids[]" value="{$news.id}" class="news-checkbox"></td>
                                <td>{$news.id}</td>
                                <td>
                                    <a href="news.php?op=edit&id={$news.id}" class="news-title-link">{$news.title}</a>
                                    {if $news.is_top}<span class="label label-danger">置顶</span>{/if}
                                    {if $news.is_recommend}<span class="label label-success">推荐</span>{/if}
                                    {if $news.is_show == 0}<span class="label label-warning">隐藏</span>{/if}
                                </td>
                                <td>{$news.catname}</td>
                                <td>{$news.addtime|date_format:'Y-m-d H:i'}</td>
                                <td class="text-center">{$news.click}</td>
                                <td class="text-right">
                                    <div class="info-actions">
                                        <!-- 快捷操作按钮 -->
                                        {if $news.is_recommend}
                                        <a href="news.php?op=toggle_recommend&id={$news.id}" class="btn btn-sm btn-light-success" title="取消推荐" onclick="return confirm('确定要取消推荐吗？')">
                                            推荐
                                        </a>
                                        {else}
                                        <a href="news.php?op=toggle_recommend&id={$news.id}" class="btn btn-sm btn-light-secondary" title="设为推荐" onclick="return confirm('确定要设为推荐吗？')">
                                            推荐
                                        </a>
                                        {/if}

                                        {if $news.is_top}
                                        <a href="news.php?op=toggle_top&id={$news.id}" class="btn btn-sm btn-light-danger" title="取消置顶" onclick="return confirm('确定要取消置顶吗？')">
                                            置顶
                                        </a>
                                        {else}
                                        <a href="news.php?op=toggle_top&id={$news.id}" class="btn btn-sm btn-light-secondary" title="设为置顶" onclick="return confirm('确定要设为置顶吗？')">
                                            置顶
                                        </a>
                                        {/if}

                                        <a href="/news/{$news.id}.html" target="_blank" class="btn btn-sm btn-light-info" title="查看">查看</a>
                                        <a href="news.php?op=edit&id={$news.id}" class="btn btn-sm btn-light-primary" title="编辑">编辑</a>
                                        <a href="javascript:if(confirm('确定要删除这篇文章吗？'))location='news.php?op=delete&id={$news.id}'" class="btn btn-sm btn-light-danger" title="删除">删除</a>
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                            {else}
                            <tr>
                                <td colspan="7" class="text-center">暂无记录</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>
                
                {if $news_list}
                <!-- 批量操作区域 -->
                <div class="batch-actions mt-3">
                    <div class="batch-actions-group">
                        <label class="batch-actions-label">选择操作：</label>
                        <button type="button" id="select-all" class="btn btn-sm btn-outline-secondary">全选</button>
                        <button type="button" id="select-none" class="btn btn-sm btn-outline-secondary">取消全选</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">推荐操作：</label>
                        <button type="button" id="batch-recommend-btn" class="btn btn-sm btn-success" disabled>批量推荐</button>
                        <button type="button" id="batch-unrecommend-btn" class="btn btn-sm btn-warning" disabled>取消推荐</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">置顶操作：</label>
                        <button type="button" id="batch-top-btn" class="btn btn-sm btn-info" disabled>批量置顶</button>
                        <button type="button" id="batch-untop-btn" class="btn btn-sm btn-secondary" disabled>取消置顶</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">显示操作：</label>
                        <button type="button" id="batch-show-btn" class="btn btn-sm btn-primary" disabled>批量显示</button>
                        <button type="button" id="batch-hide-btn" class="btn btn-sm btn-dark" disabled>批量隐藏</button>
                    </div>

                    <div class="batch-actions-group">
                        <label class="batch-actions-label">危险操作：</label>
                        <button type="button" id="batch-delete-btn" class="btn btn-sm btn-danger" disabled>批量删除</button>
                    </div>
                </div>
                {/if}
            </form>
            
            {if $total > 0}
            <div class="pagination-wrapper">
                <div class="pagination-container">
                    {$pagination_html}
                    <span class="pagination-total">共 {$pagination.total_page} 页</span>
                </div>
            </div>
            {/if}
        </div>
    </div>
</div>

<!-- 批量操作的JavaScript -->
<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // 全选按钮
    var checkAll = document.getElementById('check-all');
    var newsCheckboxes = document.getElementsByClassName('news-checkbox');
    var batchDeleteBtn = document.getElementById('batch-delete-btn');
    
    // 全选/取消全选
    checkAll.addEventListener('change', function() {
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = this.checked;
        }
        updateBatchDeleteButton();
    });
    
    // 全选按钮
    document.getElementById('select-all').addEventListener('click', function() {
        checkAll.checked = true;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = true;
        }
        updateBatchDeleteButton();
    });
    
    // 取消全选按钮
    document.getElementById('select-none').addEventListener('click', function() {
        checkAll.checked = false;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            newsCheckboxes[i].checked = false;
        }
        updateBatchDeleteButton();
    });
    
    // 更新批量操作按钮状态
    function updateBatchDeleteButton() {
        var checkedCount = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                checkedCount++;
            }
        }
        var hasSelection = checkedCount > 0;

        // 更新所有批量操作按钮状态
        batchDeleteBtn.disabled = !hasSelection;
        document.getElementById('batch-recommend-btn').disabled = !hasSelection;
        document.getElementById('batch-unrecommend-btn').disabled = !hasSelection;
        document.getElementById('batch-top-btn').disabled = !hasSelection;
        document.getElementById('batch-untop-btn').disabled = !hasSelection;
        document.getElementById('batch-show-btn').disabled = !hasSelection;
        document.getElementById('batch-hide-btn').disabled = !hasSelection;
    }
    
    // 当单个复选框状态改变时，更新全选复选框状态
    for (var i = 0; i < newsCheckboxes.length; i++) {
        newsCheckboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < newsCheckboxes.length; j++) {
                if (!newsCheckboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            checkAll.checked = allChecked;
            updateBatchDeleteButton();
        });
    }
    
    // 批量删除按钮点击事件
    batchDeleteBtn.addEventListener('click', function() {
        var checkedCount = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                checkedCount++;
            }
        }

        if (checkedCount === 0) {
            alert('请选择要删除的文章');
            return;
        }

        if (confirm('确定要删除选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_delete';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量推荐按钮点击事件
    document.getElementById('batch-recommend-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要推荐的文章');
            return;
        }

        if (confirm('确定要推荐选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_recommend';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量取消推荐按钮点击事件
    document.getElementById('batch-unrecommend-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要取消推荐的文章');
            return;
        }

        if (confirm('确定要取消推荐选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_unrecommend';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量置顶按钮点击事件
    document.getElementById('batch-top-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要置顶的文章');
            return;
        }

        if (confirm('确定要置顶选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_top';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量取消置顶按钮点击事件
    document.getElementById('batch-untop-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要取消置顶的文章');
            return;
        }

        if (confirm('确定要取消置顶选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_untop';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量显示按钮点击事件
    document.getElementById('batch-show-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要显示的文章');
            return;
        }

        if (confirm('确定要显示选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_show';
            document.getElementById('batch-form').submit();
        }
    });

    // 批量隐藏按钮点击事件
    document.getElementById('batch-hide-btn').addEventListener('click', function() {
        var checkedCount = getCheckedCount();
        if (checkedCount === 0) {
            alert('请选择要隐藏的文章');
            return;
        }

        if (confirm('确定要隐藏选中的 ' + checkedCount + ' 篇文章吗？')) {
            document.getElementById('batch-form').action = 'news.php?op=batch_hide';
            document.getElementById('batch-form').submit();
        }
    });

    // 获取选中数量的辅助函数
    function getCheckedCount() {
        var count = 0;
        for (var i = 0; i < newsCheckboxes.length; i++) {
            if (newsCheckboxes[i].checked) {
                count++;
            }
        }
        return count;
    }
    
    // 初始化批量删除按钮状态
    updateBatchDeleteButton();

    // 快捷操作按钮的加载状态
    document.querySelectorAll('.info-actions .btn-light-success, .info-actions .btn-light-secondary, .info-actions .btn-light-danger').forEach(function(btn) {
        // 只处理推荐和置顶按钮（包含"推荐"或"置顶"文字的按钮）
        if (btn.textContent.trim() === '推荐' || btn.textContent.trim() === '置顶') {
            btn.addEventListener('click', function(e) {
                // 为快捷操作按钮添加加载状态
                var originalText = this.textContent;
                this.textContent = '处理中...';
                this.style.opacity = '0.6';

                // 如果操作失败，恢复原始文本
                setTimeout(function() {
                    btn.textContent = originalText;
                    btn.style.opacity = '1';
                }, 3000);
            });
        }
    });
});
</script>

{include file="footer.htm"} 