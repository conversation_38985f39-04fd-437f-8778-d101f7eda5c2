/*
 * 后台管理系统统一样式文件
 * 包含所有UI组件的样式定义
 * 支持PHP7.3-PHP8.2，面向过程编程
 * PC端专用，无响应式设计
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 - 统一颜色和尺寸 */
:root {
    /* 主色调 */
    --primary-color: #1b68ff;
    --primary-hover: #0045ce;
    --primary-light: rgba(27, 104, 255, 0.1);

    /* 状态颜色 */
    --success-color: #3ad29f;
    --warning-color: #eea303;
    --danger-color: #f82f58;
    --info-color: #17a2b8;

    /* 文本颜色 */
    --text-color: #001a4e;
    --text-secondary: #6c757d;
    --text-muted: #999999;

    /* 背景和边框 */
    --bg-white: #ffffff;
    --bg-gray: #f8f9fa;
    --bg-light: #f5f5f5;
    --border-color: #e9ecef;
    --border-light: #f0f0f0;

    /* 布局尺寸 */
    --sidebar-width: 180px;
    --sidebar-collapsed-width: 64px;
    --sidebar-bg: #343a40;
    --header-height: 64px;

    /* 阴影和过渡 */
    --card-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.05);
    --hover-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.08);
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    background: var(--bg-gray);
}

/* Layout */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar - 左右两列布局 */
.sidebar {
    width: var(--sidebar-width); /* 使用变量宽度 */
    background: var(--sidebar-bg);
    color: #fff;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    padding-top: var(--header-height);
    overflow-y: auto;
    box-shadow: 2px 0 6px rgba(0,0,0,0.1);
    display: flex; /* 恢复两列布局 */
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    overflow-x: hidden;
    display: block; /* 改为单列布局 */
}

.sidebar.collapsed .menu-primary {
    width: 100%;
}

.sidebar.collapsed .menu-secondary {
    display: none;
}

/* 简单菜单项样式 - 兼容原有的sidebar.htm */
.sidebar > .menu-item {
    padding: 12px 16px;
    color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    border-left: 3px solid transparent;
    font-size: 13px;
}

.sidebar > .menu-item:hover {
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-left: 3px solid var(--primary-color);
}

.sidebar > .menu-item.active {
    color: #fff;
    background: var(--primary-color);
    border-left: 3px solid var(--primary-hover);
}

.sidebar > .menu-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.sidebar > .menu-item i {
    font-size: 14px;
    width: 18px;
    text-align: center;
    flex-shrink: 0;
}

.sidebar > .menu-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

/* 收起状态下的简单菜单样式 */
.sidebar.collapsed > .menu-item {
    padding: 16px 0;
    justify-content: center;
    border-left: none;
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed > .menu-item a {
    justify-content: center;
    gap: 0;
}

.sidebar.collapsed > .menu-item span {
    display: none;
}

.sidebar.collapsed > .menu-item i {
    font-size: 16px;
    width: auto;
}

.sidebar.collapsed > .menu-item:hover {
    border-left: none;
    background: rgba(255,255,255,0.1);
}

.sidebar.collapsed > .menu-item.active {
    background: var(--primary-color);
    border-left: none;
}

.sidebar-header {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width); /* 使用变量匹配侧边栏宽度 */
    height: var(--header-height);
    background: var(--sidebar-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    z-index: 1001;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar.collapsed .sidebar-header {
    width: var(--sidebar-collapsed-width);
    padding: 0;
    justify-content: center;
}

/* 左侧一级菜单列 */
.menu-primary {
    width: 60px; /* 再次缩小宽度 */
    background: var(--sidebar-bg);
    flex-shrink: 0;
    min-width: 60px;
}

/* 右侧二级菜单列 */
.menu-secondary {
    width: 120px; /* 进一步缩小宽度 */
    background: var(--bg-white);
    border-left: 1px solid rgba(255,255,255,0.1);
    padding: 0;
    flex: 1;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
}

.logo i {
    font-size: 16px;
    color: var(--primary-color);
}

.sidebar.collapsed .logo {
    justify-content: center;
    padding: 0;
    margin: 0;
}

.sidebar.collapsed .logo span {
    display: none;
}

/* 一级菜单项样式 */
.menu-primary .menu-item {
    padding: 10px 6px;
    color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-left: 3px solid transparent;
    font-size: 14px;
    text-align: center;
    flex-direction: column;
    gap: 4px;
    min-height: 55px;
    justify-content: center;
}

.menu-primary .menu-item:hover {
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-left: 3px solid var(--primary-color);
}

.menu-primary .menu-item.active {
    color: #fff;
    background: var(--primary-color);
    border-left: 3px solid var(--primary-hover);
}

.menu-primary .menu-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 2px;
    width: 100%;
    height: 100%;
    justify-content: center;
    padding: 4px;
}

.menu-primary .menu-item i {
    font-size: 13px;
    width: auto;
    text-align: center;
    flex-shrink: 0;
}

.menu-primary .menu-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
    font-weight: 500;
    max-width: 55px;
    text-align: center;
    line-height: 1.2;
}

/* 收起状态下的一级菜单样式 */
.sidebar.collapsed .menu-primary .menu-item {
    padding: 14px 0;
    justify-content: center;
    border-left: none;
    width: var(--sidebar-collapsed-width);
    display: flex;
    align-items: center;
    margin: 0;
}

.sidebar.collapsed .menu-primary .menu-item a {
    justify-content: center;
    width: 100%;
    gap: 0;
}

.sidebar.collapsed .menu-primary .menu-item span {
    display: none;
}

.sidebar.collapsed .menu-primary .menu-item i {
    margin: 0;
    width: auto;
    font-size: 16px;
}

.sidebar.collapsed .menu-primary .menu-item:hover {
    border-left: none;
    background: rgba(255,255,255,0.1);
}

.sidebar.collapsed .menu-primary .menu-item.active {
    background: var(--primary-color);
    border-left: none;
}

/* 二级菜单项样式 */
.menu-secondary .menu-item {
    padding: 8px 8px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    border-left: 3px solid transparent;
}

.menu-secondary .menu-item:hover {
    background: var(--primary-light);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.menu-secondary .menu-item.active {
    background: var(--primary-color);
    color: var(--bg-white);
    border-left: 3px solid var(--primary-hover);
}

.menu-secondary .menu-item a {
    color: inherit;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 4px;
    width: 100%;
}

.menu-secondary .menu-item i {
    font-size: 13px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.menu-secondary .menu-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 二级菜单组样式 */
.submenu-group {
    display: none;
}

.submenu-group.active {
    display: block;
}

.submenu-group-title {
    padding: 10px 8px 6px;
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Main content */
.main-content {
    margin-left: var(--sidebar-width); /* 使用变量匹配侧边栏宽度 */
    flex: 1;
    padding: 14px;
    padding-top: calc(var(--header-height) + 14px);
    transition: var(--transition);
}

.wrapper.collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top navigation */
.top-nav {
    height: var(--header-height);
    background: #fff;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width); /* 使用变量匹配侧边栏宽度 */
    z-index: 999;
    transition: var(--transition);
}

.wrapper.collapsed .top-nav {
    left: var(--sidebar-collapsed-width);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toggle-sidebar {
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.toggle-sidebar:hover {
    color: var(--text-color);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.breadcrumb i {
    font-size: 12px;
}

.admin-badge {
    background: var(--primary-color);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-item {
    position: relative;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-item:hover {
    color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.1);
}

.nav-item i {
    font-size: 18px;
}

.user-item {
    display: flex;
    align-items: center;
    background-color: var(--bg-gray);
    border-radius: 24px;
    padding: 6px 16px;
    margin-left: 8px;
    border: 1px solid var(--border-color);
}

.user-item:hover {
    border-color: var(--primary-color);
    background-color: rgba(27, 104, 255, 0.05);
}

.user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: 500;
}

.user-name {
    font-weight: 500;
    margin-right: 12px;
}

.logout-link {
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.logout-link:hover {
    color: var(--danger-color);
}

.badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--danger-color);
    color: #fff;
    font-size: 12px;
    height: 18px;
    min-width: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
}

/* Operation type badges */
.badge {
    position: static;
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid;
}

.badge-create {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border-color: rgba(58, 210, 159, 0.2);
}

.badge-update {
    background: rgba(238, 163, 3, 0.1);
    color: #eea303;
    border-color: rgba(238, 163, 3, 0.2);
}

.badge-delete {
    background: rgba(248, 47, 88, 0.1);
    color: #f82f58;
    border-color: rgba(248, 47, 88, 0.2);
}

.badge-login {
    background: rgba(27, 104, 255, 0.1);
    color: #1b68ff;
    border-color: rgba(27, 104, 255, 0.2);
}

.badge-logout {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.2);
}

.badge-test {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.2);
}

.badge-success {
    background: rgba(58, 210, 159, 0.1);
    color: #3ad29f;
    border-color: rgba(58, 210, 159, 0.2);
}

/* Page title */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

/* Sections */
.section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
}

/* Cards */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 24px;
    margin-bottom: 24px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.trend-up {
    color: var(--success-color);
}

.trend-down {
    color: var(--danger-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    background: transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-warning {
    background: var(--warning-color);
    color: #fff;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #d39e00;
    border-color: #d39e00;
}

.btn-danger {
    background: var(--danger-color);
    color: #fff;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #e11d48;
    border-color: #e11d48;
}

.btn-outline {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: #fff;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* Light buttons */
.btn-light-primary {
    background-color: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border-color: rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    background-color: rgba(27, 104, 255, 0.2);
    color: var(--primary-hover);
}

.btn-light-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.btn-light-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-white);
    font-size: 13px;
}

.table th,
.table td {
    padding: 10px 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.table th {
    background: var(--bg-gray);
    font-weight: 600;
    color: var(--text-color);
    font-size: 12px;
    white-space: nowrap;
}

.table th i {
    font-size: 10px;
    color: var(--text-secondary);
    margin-left: 4px;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 紧凑表格 */
.table-compact th,
.table-compact td {
    padding: 8px 6px;
}

.table-compact {
    font-size: 12px;
}

/* 表格标题样式 */
.table-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.title-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.title-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.title-meta {
    background: var(--warning-color);
    color: var(--bg-white);
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

/* 分类标签 */
.category-tag {
    background: var(--bg-light);
    color: var(--text-secondary);
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    white-space: nowrap;
}

/* 表格底部 */
.table-footer {
    border-top: 1px solid var(--border-color);
    background: var(--bg-light);
}

/* 增强表格样式 */
.table-enhanced {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid var(--border-light);
    table-layout: fixed;
    width: 100%;
}

.table-enhanced thead {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.table-enhanced th {
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--border-color);
    padding: 12px 8px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.table-enhanced td {
    padding: 12px 8px;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-enhanced th.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.table-enhanced th.sortable:hover {
    background: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
}

.sort-icon {
    font-size: 10px;
    margin-left: 4px;
    opacity: 0.6;
}

.table-enhanced th.sortable:hover .sort-icon {
    opacity: 1;
    color: var(--primary-color);
}

.table-row {
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
}

.table-row:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-row:last-child {
    border-bottom: none;
}

.table-id {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 13px;
}

.table-number {
    font-weight: 600;
    color: var(--text-color);
    font-family: 'Courier New', monospace;
}

.table-time {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.5;
    white-space: nowrap;
}

.table-time small {
    font-size: 10px;
    opacity: 0.8;
    margin-left: 4px;
}

.table-subtitle {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 2px;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

/* 状态标签优化 */
.status-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
}

.status-tag.success {
    background: rgba(58, 210, 159, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(58, 210, 159, 0.3);
}

.status-tag.warning {
    background: rgba(238, 163, 3, 0.15);
    color: var(--warning-color);
    border: 1px solid rgba(238, 163, 3, 0.3);
}

.status-tag.danger {
    background: rgba(248, 47, 88, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(248, 47, 88, 0.3);
}

/* 分类标签优化 */
.category-tag {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
}

.category-tag.primary {
    background: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(27, 104, 255, 0.2);
}

.category-tag.info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.category-tag.secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

/* 标题元标签优化 */
.title-meta {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
}

.title-meta.hot {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: var(--bg-white);
}

.title-meta.new {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
    color: var(--bg-white);
}

/* 增强操作按钮样式 */
.action-buttons-enhanced {
    display: flex;
    gap: 2px;
    justify-content: flex-end;
    flex-wrap: nowrap;
    white-space: nowrap;
    align-items: center;
}

.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid transparent;
    white-space: nowrap;
    min-width: 44px;
    justify-content: center;
}

.btn-action i {
    font-size: 9px;
}

.btn-action span {
    font-size: 10px;
}

/* 查看按钮 */
.btn-view {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
    border-color: rgba(23, 162, 184, 0.2);
}

.btn-view:hover {
    background: rgba(23, 162, 184, 0.2);
    color: #138496;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* 编辑按钮 */
.btn-edit {
    background: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border-color: rgba(27, 104, 255, 0.2);
}

.btn-edit:hover {
    background: rgba(27, 104, 255, 0.2);
    color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(27, 104, 255, 0.3);
}

/* 置顶按钮 */
.btn-top {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.2);
}

.btn-top:hover {
    background: rgba(255, 193, 7, 0.2);
    color: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* 审核按钮 */
.btn-approve {
    background: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.btn-approve:hover {
    background: rgba(58, 210, 159, 0.2);
    color: #2eb88a;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(58, 210, 159, 0.3);
}

/* 删除按钮 */
.btn-delete {
    background: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

.btn-delete:hover {
    background: rgba(248, 47, 88, 0.2);
    color: #e11d48;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(248, 47, 88, 0.3);
}

/* 操作按钮列宽度控制 */
.table-enhanced th:last-child,
.table-enhanced td:last-child {
    width: 220px;
    min-width: 220px;
    text-align: right;
    padding-right: 12px;
}

/* 响应式操作按钮 */
@media (max-width: 1600px) {
    .btn-action span {
        display: none;
    }

    .btn-action {
        min-width: 26px;
        padding: 4px 6px;
    }

    .action-buttons-enhanced {
        gap: 3px;
        justify-content: flex-end;
    }

    .table-enhanced th:last-child,
    .table-enhanced td:last-child {
        width: 140px;
        min-width: 140px;
    }
}

@media (max-width: 1200px) {
    .table-enhanced th:last-child,
    .table-enhanced td:last-child {
        width: 120px;
        min-width: 120px;
    }

    .action-buttons-enhanced {
        gap: 2px;
    }

    .btn-action {
        min-width: 24px;
        padding: 3px 5px;
    }
}

/* Tags */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.tag-success {
    background: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
}

.tag-warning {
    background: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
}

.tag-danger {
    background: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
}

/* Utility classes */
.d-flex {
    display: flex !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.gap-2 {
    gap: 8px !important;
}

.gap-3 {
    gap: 12px !important;
}

.text-center {
    text-align: center !important;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Alert styles */
.alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid transparent;
}

.alert i {
    font-size: 16px;
}

.alert-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border-color: rgba(58, 210, 159, 0.2);
}

.alert-warning {
    background-color: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
    border-color: rgba(238, 163, 3, 0.2);
}

.alert-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border-color: rgba(248, 47, 88, 0.2);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-color: rgba(23, 162, 184, 0.2);
}

/* Text utilities */
.text-muted {
    color: var(--text-secondary) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 240px; /* 匹配新的侧边栏宽度 */
        transform: translateX(-100%);
    }

    .sidebar.collapsed {
        width: var(--sidebar-collapsed-width);
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .wrapper.collapsed .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }

    .top-nav {
        left: 0;
    }

    .wrapper.collapsed .top-nav {
        left: var(--sidebar-collapsed-width);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}

/* Text color utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-muted {
    color: var(--text-secondary) !important;
}

/* Cache management styles */
.cache-operations {
    display: grid;
    gap: 32px;
}

.operation-group {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.operation-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--bg-gray);
}

.operation-header i {
    font-size: 20px;
    color: var(--primary-color);
}

.operation-header span {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.operation-header small {
    color: var(--text-secondary);
    font-size: 13px;
    margin-left: auto;
}

.operation-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.operation-btn {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.operation-btn:hover::before {
    left: 100%;
}

.refresh-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.clean-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.clean-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.danger-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.danger-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.btn-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.btn-content {
    flex: 1;
}

.btn-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.btn-desc {
    font-size: 13px;
    opacity: 0.9;
}

/* Enhanced stat cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 4px;
}

.stat-title {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Quick tips */
.quick-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-item {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-color);
}

.tip-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.tip-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.tip-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Cache management responsive */
@media (max-width: 768px) {
    .operation-buttons {
        grid-template-columns: 1fr;
    }

    .operation-btn {
        padding: 16px;
        gap: 12px;
    }

    .btn-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }

    .stat-card {
        padding: 16px;
        gap: 12px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .stat-value {
        font-size: 20px;
    }

    .quick-tips {
        grid-template-columns: 1fr;
    }
}

/* Setting Tabs Styles */
.setting-tabs {
    display: flex;
    margin-bottom: 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-tab {
    padding: 12px 24px;
    background: #f8f9fa;
    color: var(--text-secondary);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    margin-right: 2px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    white-space: nowrap;
}

.setting-tab:hover {
    background: #e9ecef;
    color: var(--text-color);
}

.setting-tab.active {
    background: #fff;
    color: var(--primary-color);
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    z-index: 1;
}

/* Form Styles */
.form-horizontal {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.form-label {
    flex: 0 0 150px;
    font-weight: 500;
    color: var(--text-color);
    padding-top: 8px;
    text-align: right;
}

.form-field {
    flex: 1;
    max-width: 400px;
}

.form-help {
    flex: 0 0 200px;
    padding-top: 8px;
    padding-left: 15px;
}

.help-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    min-height: 80px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background: #fff;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.1);
}

.form-check {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin-right: 20px;
    cursor: pointer;
}

.form-check input[type="radio"],
.form-check input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
}

.form-check-label {
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.help-block {
    margin-top: 5px;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    padding-left: 165px;
}

/* Responsive Form Styles */
@media (max-width: 768px) {
    .setting-tabs {
        flex-wrap: wrap;
        gap: 4px;
        border-bottom: none;
        margin-bottom: 10px;
    }

    .setting-tab {
        border-radius: 4px;
        border: 1px solid var(--border-color);
        margin-right: 0;
        margin-bottom: 0;
    }

    .setting-tab.active {
        border: 1px solid var(--primary-color);
        margin-bottom: 0;
    }

    .form-group {
        flex-direction: column;
        gap: 8px;
    }

    .form-label {
        flex: none;
        text-align: left;
        padding-top: 0;
    }

    .form-field {
        max-width: none;
    }

    .form-help {
        flex: none;
        padding-left: 0;
        padding-top: 5px;
    }

    .form-actions {
        padding-left: 0;
    }
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    min-width: 350px;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-gray);
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 紧凑统计样式 */
.stats-compact {
    display: flex;
    gap: 30px;
    padding: 15px 20px;
    background: var(--bg-gray);
    border-radius: 6px;
    align-items: center;
    flex-wrap: wrap;
}

.stat-compact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-text {
    font-size: 13px;
    color: var(--text-secondary);
}

/* 紧凑筛选表单样式 */
.filter-form-compact {
    padding: 15px 20px;
    background: var(--bg-gray);
    border-radius: 6px;
}

.filter-row-single {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-item-inline {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.filter-item-inline label {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 500;
    margin: 0;
}

.form-control-sm {
    padding: 4px 8px;
    font-size: 13px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-width: 100px;
}

.form-control-sm:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(27, 104, 255, 0.1);
}

.filter-buttons-inline {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

/* 日志表格优化 */
.table-logs {
    font-size: 13px;
}

.table-logs th {
    font-size: 12px;
    padding: 10px 8px;
    background: var(--bg-gray);
    border-bottom: 2px solid var(--border-color);
}

.table-logs td {
    padding: 8px;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-logs .badge {
    font-size: 9px;
    padding: 2px 6px;
}

/* 底部批量操作样式 */
.batch-actions-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-gray);
}

.batch-select-all {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0;
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.batch-select-all input[type="checkbox"] {
    margin: 0;
}

.batch-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 记录数量样式 */
.record-count {
    font-size: 13px;
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: 10px;
}

/* 日志表格优化 */
.table-logs {
    font-size: 13px;
}

.table-logs th {
    font-size: 12px;
    padding: 10px 8px;
    background: var(--bg-gray);
    border-bottom: 2px solid var(--border-color);
}

.table-logs td {
    padding: 8px;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-logs .badge {
    font-size: 9px;
    padding: 2px 6px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .filter-row-single {
        gap: 15px;
    }

    .filter-item-inline {
        gap: 4px;
    }

    .form-control-sm {
        min-width: 80px;
    }
}

@media (max-width: 768px) {
    .stats-compact {
        gap: 20px;
        justify-content: center;
    }

    .filter-row-single {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-item-inline {
        justify-content: space-between;
    }

    .filter-buttons-inline {
        margin-left: 0;
        justify-content: center;
    }
}

/* 扩展筛选表单样式 - 3行布局 */
.filter-form-expanded {
    padding: 20px !important;
    background: var(--bg-gray) !important;
    border-radius: 8px !important;
    margin-bottom: 0 !important;
}

.filter-row-expanded {
    display: flex !important;
    gap: 20px !important;
    margin-bottom: 16px !important;
    align-items: end !important;
    flex-wrap: wrap !important;
}

.filter-row-expanded:last-child {
    margin-bottom: 0 !important;
}

.filter-item-expanded {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
    min-width: fit-content !important;
}

.filter-item-expanded .form-control {
    width: auto !important;
    min-width: 120px !important;
}

.filter-item-expanded select.form-control {
    min-width: 100px !important;
}

.filter-item-expanded input[type="text"].form-control {
    min-width: 140px !important;
}

.filter-item-expanded input[type="date"].form-control {
    min-width: 150px !important;
}

.filter-item-expanded label {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: var(--text-color) !important;
    margin-bottom: 0 !important;
}

.filter-buttons-expanded {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

/* 日志表格优化 - 去除换行 */
.table-logs td {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 页面标题样式 */
.page-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--border-color);
}

.page-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title h1 i {
    color: var(--primary-color);
}

.page-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    margin: -24px -24px 20px -24px;
    background: var(--bg-light);
}

.card-header .card-title {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
}

.card-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 表单错误样式 */
.form-control.error,
.form-textarea.error,
.form-select.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(248, 47, 88, 0.1);
}

.field-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* 工具提示样式 */
.admin-tooltip {
    position: absolute;
    background: var(--text-color);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10000;
    pointer-events: none;
}

.admin-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: var(--text-color);
}

/* 消息提示样式 */
.admin-message {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    z-index: 10001;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease;
}

.admin-message .message-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 分页样式 - 紧凑版 */
.pagination-wrapper {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
}

.page-link {
    display: inline-block;
    padding: 6px 10px;
    margin: 0 1px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--bg-white);
    font-size: 12px;
    line-height: 1.3;
    transition: var(--transition);
    min-width: 32px;
    text-align: center;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: var(--bg-white);
    border-color: var(--primary-color);
    text-decoration: none;
}

.page-current {
    display: inline-block;
    padding: 6px 10px;
    margin: 0 1px;
    color: var(--bg-white);
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.3;
    font-weight: 500;
    min-width: 32px;
    text-align: center;
}

.page-info {
    margin-left: 12px;
    color: var(--text-secondary);
    font-size: 12px;
}

/* 页面底部样式 - 居中两行版 */
.admin-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: var(--bg-white);
    border-top: 1px solid var(--border-color);
    z-index: 998;
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 2px;
}

.footer-copyright,
.footer-version {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 主内容区底部留白，避免被footer遮挡 */
.main-content {
    padding-bottom: 70px; /* 给footer留出空间 */
}

/* 底部响应式设计 */
@media (max-width: 768px) {
    .footer-copyright,
    .footer-version {
        font-size: 11px;
    }

    .main-content {
        padding-bottom: 70px;
    }
}

@media (max-width: 480px) {
    .footer-copyright,
    .footer-version {
        font-size: 10px;
    }

    .admin-footer {
        height: 45px;
    }

    .main-content {
        padding-bottom: 65px;
    }
}

/* 按钮禁用状态 */
.btn.disabled,
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 成功按钮样式 */
.btn-success {
    background: var(--success-color);
    color: var(--bg-white);
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #2eb88a;
    border-color: #2eb88a;
}

/* 浅色成功按钮 */
.btn-light-success:hover {
    background-color: rgba(58, 210, 159, 0.2);
    color: #2eb88a;
}

/* 浅色危险按钮 */
.btn-light-danger:hover {
    background-color: rgba(248, 47, 88, 0.2);
    color: #e11d48;
}

/* 表格行悬停效果增强 */
.table tbody tr:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 统计卡片悬停效果 */
.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

/* 卡片悬停效果 */
.card:hover {
    box-shadow: var(--hover-shadow);
}

/* 进度条样式 */
.progress-item {
    margin-bottom: 16px;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 13px;
    color: var(--text-color);
}

.progress-bar {
    height: 8px;
    background: var(--bg-light);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 加载状态样式 */
.loading-spinner {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-size: 14px;
}

.loading-spinner i {
    font-size: 16px;
}

.loading-dots {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 14px;
}

.dots {
    display: flex;
    gap: 4px;
}

.dots span {
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: dotPulse 1.4s infinite ease-in-out;
}

.dots span:nth-child(1) { animation-delay: -0.32s; }
.dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 图表卡片样式 */
.chart-card {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-light);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chart-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.chart-period {
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--bg-light);
    padding: 4px 8px;
    border-radius: 12px;
}

.chart-placeholder {
    height: 200px;
    background: var(--bg-light);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.chart-placeholder i {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.chart-placeholder p {
    text-align: center;
    line-height: 1.5;
    margin: 0;
}

.chart-stats {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid var(--border-light);
    padding-top: 16px;
}

.chart-stat {
    text-align: center;
}

.chart-stat .stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.chart-stat .stat-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

/* 快捷操作面板 */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: 10px;
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
    border-color: var(--primary-color);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-white);
    font-size: 20px;
    flex-shrink: 0;
}

.quick-action-content h4 {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 4px 0;
}

.quick-action-content p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* 设置选项卡样式 - 优化版 */
.setting-tabs {
    display: flex;
    background: var(--bg-light);
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 0;
    gap: 2px;
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.setting-tab {
    padding: 12px 18px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 6px;
    transition: var(--transition);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    position: relative;
    background: transparent;
    display: flex;
    align-items: center;
    gap: 6px;
}

.setting-tab:hover {
    color: var(--primary-color);
    background: rgba(27, 104, 255, 0.08);
    text-decoration: none;
}

.setting-tab.active {
    color: var(--primary-color);
    background: var(--bg-white);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    font-weight: 600;
}

.tab-content {
    padding: 20px 0;
    border-top: 1px solid var(--border-light);
    margin-top: 20px;
}

/* 表单增强样式 */
.form-radio-group {
    display: flex;
    gap: 20px;
    align-items: center;
}

.form-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check input[type="radio"],
.form-check input[type="checkbox"] {
    margin: 0;
}

.form-check-label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
}

.form-check-label i {
    font-size: 14px;
}

.form-check input[type="radio"]:checked + label i.fa-check-circle {
    color: var(--success-color);
}

.form-check input[type="radio"]:checked + label i.fa-times-circle {
    color: var(--danger-color);
}

.help-icon {
    color: var(--text-secondary);
    cursor: help;
    font-size: 12px;
    margin-left: 4px;
}

.help-icon:hover {
    color: var(--primary-color);
}

.form-help {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    margin-top: 6px;
    padding: 8px 12px;
    background: var(--bg-light);
    border-radius: 4px;
    border-left: 3px solid var(--info-color);
}

.form-help i {
    color: var(--info-color);
    font-size: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.help-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    color: var(--border-color);
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 18px;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

.form-control.error,
.form-textarea.error,
.form-select.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

/* 页面动作按钮 */
.page-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 紧凑统计样式 */
.stats-compact {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 16px 0;
    gap: 20px;
}

.stat-compact {
    text-align: center;
    flex: 1;
}

.stat-compact .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1.2;
    margin-bottom: 4px;
}

.stat-compact .stat-text {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 输入组合样式 */
.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group-append {
    margin-left: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-left: 0;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
}

.input-group-text img {
    max-width: 100px;
    max-height: 32px;
    object-fit: contain;
}

.form-text {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 4px;
    line-height: 1.3;
}

/* 浅色按钮样式补充 */
.btn-light-primary {
    background-color: rgba(27, 104, 255, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(27, 104, 255, 0.2);
}

.btn-light-primary:hover {
    background-color: rgba(27, 104, 255, 0.2);
    color: var(--primary-hover);
    border-color: rgba(27, 104, 255, 0.3);
}

.btn-light-warning {
    background-color: rgba(238, 163, 3, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(238, 163, 3, 0.2);
}

.btn-light-warning:hover {
    background-color: rgba(238, 163, 3, 0.2);
    color: #cc8400;
    border-color: rgba(238, 163, 3, 0.3);
}

.btn-light-danger {
    background-color: rgba(248, 47, 88, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(248, 47, 88, 0.2);
}

.btn-light-danger:hover {
    background-color: rgba(248, 47, 88, 0.2);
    color: #e11d48;
    border-color: rgba(248, 47, 88, 0.3);
}

.btn-light-success {
    background-color: rgba(58, 210, 159, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(58, 210, 159, 0.2);
}

.btn-light-success:hover {
    background-color: rgba(58, 210, 159, 0.2);
    color: #2eb88a;
    border-color: rgba(58, 210, 159, 0.3);
}

.btn-light-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

.btn-light-info:hover {
    background-color: rgba(23, 162, 184, 0.2);
    color: #138496;
    border-color: rgba(23, 162, 184, 0.3);
}

.btn-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.btn-light-secondary:hover {
    background-color: rgba(108, 117, 125, 0.2);
    color: #495057;
    border-color: rgba(108, 117, 125, 0.3);
}

/* 文件上传组件样式 */
.upload-area {
    margin-bottom: 20px;
}

.upload-box {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: var(--bg-light);
    transition: var(--transition);
    position: relative;
    cursor: pointer;
}

.upload-box:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.upload-box i {
    font-size: 48px;
    color: var(--text-secondary);
    margin-bottom: 16px;
    display: block;
}

.upload-box p {
    margin: 8px 0;
    color: var(--text-color);
    font-size: 14px;
}

.upload-hint {
    font-size: 12px !important;
    color: var(--text-secondary) !important;
}

.upload-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.upload-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-white);
    gap: 12px;
}

.upload-preview {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    border-radius: 4px;
    flex-shrink: 0;
}

.upload-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.upload-info {
    flex: 1;
}

.upload-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2px;
}

.upload-size {
    font-size: 12px;
    color: var(--text-secondary);
}

.upload-actions {
    display: flex;
    gap: 6px;
}

/* 开关组件样式 */
.switch {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    line-height: 1.5;
    min-height: 24px; /* 确保最小高度 */
}

.switch input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #ddd;
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
    border: 1px solid #ccc;
}

.switch-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: var(--bg-white);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.switch input:checked + .switch-slider {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.switch input:checked + .switch-slider::before {
    transform: translateX(20px);
}

.switch:hover .switch-slider {
    box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.15);
}

.switch-label {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    line-height: 1.5;
}

/* 表单中的开关对齐 */
.form-group .form-field .switch {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    margin-right: 0;
}

/* 多个开关的布局 */
.form-field .switch {
    margin-right: 32px;
    margin-bottom: 12px;
}

.form-field .switch:last-child {
    margin-right: 0;
}

/* 确保表单标签与开关对齐 */
.form-group {
    align-items: flex-start;
}

.form-group .form-label {
    padding-top: 2px; /* 微调对齐 */
}

/* 筛选选项卡样式 */
.filter-tabs {
    display: flex;
    background: var(--bg-light);
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--border-color);
    border-bottom: none;
    padding: 4px;
    gap: 2px;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 6px;
    transition: var(--transition);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    background: transparent;
}

.filter-tab:hover {
    color: var(--primary-color);
    background: rgba(27, 104, 255, 0.08);
    text-decoration: none;
}

.filter-tab.active {
    color: var(--primary-color);
    background: var(--bg-white);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    font-weight: 600;
}

.tab-badge {
    background: var(--danger-color);
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 4px;
}

/* 数据筛选器样式 */
.filter-panel {
    background: var(--bg-white);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 20px;
}

.filter-form {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
    min-width: fit-content;
}

.filter-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

/* 表格统计信息 */
.table-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid var(--border-light);
}

.stats-left,
.stats-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stats-text {
    font-size: 14px;
    color: var(--text-secondary);
}

.stats-text strong {
    color: var(--text-color);
    font-weight: 600;
}

.filter-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid var(--border-color);
    background: var(--bg-white);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--bg-white);
}

.search-box {
    display: flex;
    align-items: stretch;
    max-width: 300px;
}

.search-input {
    flex: 1;
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-right: 0;
    border-radius: 4px 0 0 4px;
    font-size: 12px;
    outline: none;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(27, 104, 255, 0.1);
}

.search-btn {
    padding: 6px 12px;
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 0 4px 4px 0;
    color: var(--bg-white);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--primary-hover);
}

/* 空状态页面样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    font-size: 64px;
    color: var(--text-secondary);
    margin-bottom: 24px;
    opacity: 0.6;
}

.empty-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 12px;
}

.empty-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.5;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
    margin-bottom: 0;
}

.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    background: none;
    border-radius: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    font-size: 13px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '/';
    color: var(--text-secondary);
    margin: 0 8px;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

.breadcrumb-simple {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
}

.breadcrumb-text {
    font-size: 13px;
    color: var(--text-secondary);
}

.breadcrumb-text a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-text a:hover {
    text-decoration: underline;
}

/* 响应式调整 - PC端专用，最小宽度限制 */
@media (max-width: 1200px) {
    body {
        min-width: 1200px;
        overflow-x: auto;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .stats-compact {
        flex-wrap: wrap;
        gap: 16px;
    }

    .setting-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .filter-form {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        white-space: normal;
    }

    .filter-actions {
        margin-left: 0;
        justify-content: flex-start;
    }

    .filter-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* 简单分页样式 - 紧凑版 */
.simple-pagination {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pagination-btn {
    display: inline-block;
    padding: 6px 10px;
    margin: 0 1px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--bg-white);
    font-size: 12px;
    line-height: 1.3;
    transition: var(--transition);
    min-width: 32px;
    text-align: center;
}

.pagination-btn:hover {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    text-decoration: none;
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
    font-weight: 500;
}

.pagination-btn.disabled {
    color: var(--text-secondary);
    background-color: var(--bg-gray);
    border-color: var(--border-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.disabled:hover {
    background-color: var(--bg-gray);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

/* 标准分页模板样式 */
.pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    margin-bottom: 10px;
}

.page-item {
    margin: 0 2px;
}

.page-item:first-child .page-link {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--text-secondary);
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: var(--border-color);
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary-color);
    background-color: #fff;
    border: 1px solid var(--border-color);
    text-decoration: none;
    transition: var(--transition);
}

.page-link:hover {
    z-index: 2;
    color: var(--primary-hover);
    text-decoration: none;
    background-color: var(--bg-gray);
    border-color: var(--border-color);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}
