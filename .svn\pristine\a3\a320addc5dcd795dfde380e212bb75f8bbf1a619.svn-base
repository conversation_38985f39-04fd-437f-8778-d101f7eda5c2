{if !empty($posts)}
    {foreach $posts as $post}
    <?php
    // 检查是否显示置顶标记（需要检查过期时间）
    $current_time = time();
    $show_top_tag = false;

    // 检查大分类置顶
    if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
        if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
            $show_top_tag = true;
        }
    }

    // 检查小分类置顶
    if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
        if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
            $show_top_tag = true;
        }
    }

    // 计算有效期
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $validity = $days > 0 ? $days.'天' : '已过期';
    } else {
        $validity = '长期';
    }
    ?>
    <div class="list-item <?php if ($show_top_tag) echo 'is-top'; ?>">
        <div class="item-row">
            <div class="item-left">
                <div class="item-title">
                    <?php if ($validity == '已过期'): ?>
                    <a href="/{$category.pinyin}/{$post.id}.html" class="expired">{$post.title}</a>
                    <?php else: ?>
                    <a href="/{$category.pinyin}/{$post.id}.html">{$post.title}</a>
                    <?php endif; ?>
                    <?php if ($show_top_tag): ?>
                    <span class="top-tag">顶</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="item-right">
                <div class="item-time">
                    {$post.updated_at|friendlyTime}
                </div>
            </div>
        </div>
    </div>
    {/foreach}
{/if}