<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<div class="d-flex justify-content-between align-items-center mb-1">
    <h5 class="mb-0">手机号监控 <small class="text-muted">(限制: <?php echo $mobile_daily_limit ?? ""; ?>条/日)</small></h5>
    <div class="d-flex align-items-center">
        <input type="text" class="form-control form-control-sm me-1" id="reset-mobile" placeholder="手机号" style="width:120px;">
        <button class="btn btn-warning btn-sm me-2" onclick="resetMobileLimit()">重置</button>
        <a href="setting.php" class="btn btn-sm btn-outline-secondary">设置</a>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-sm table-striped table-bordered">
        <thead class="table-dark">
            <tr>
                <th width="15%">手机号</th>
                <th width="20%">发布时间</th>
                <th width="15%">IP地址</th>
                <th width="10%">发布数</th>
                <th width="12%">时间差异</th>
                <th width="10%">状态</th>
                <th width="8%">操作</th>
            </tr>
        </thead>
        <tbody>
            <?php if(null !== ($records ?? null) && is_array($records)): foreach($records as $record): ?>
            <tr>
                <td class="fw-bold"><?php echo (isset($record['mobile'])) ? $record['mobile'] : ""; ?></td>
                <td><small class="text-muted"><?php echo (isset($record['formatted_time'])) ? $record['formatted_time'] : ""; ?></small></td>
                <td><small><?php echo (isset($record['ip_address'])) ? $record['ip_address'] : ""; ?></small></td>
                <td class="text-center">
                    <span class="badge <?php if($record['post_count'] >= $mobile_daily_limit): ?>bg-danger<?php else: ?>bg-success<?php endif; ?>">
                        <?php echo (isset($record['post_count'])) ? $record['post_count'] : ""; ?>/<?php echo $mobile_daily_limit ?? ""; ?>
                    </span>
                </td>
                <td class="text-center">
                    <span class="badge bg-<?php echo (isset($record['time_diff_status'])) ? $record['time_diff_status'] : ""; ?>"><?php echo (isset($record['time_diff_text'])) ? $record['time_diff_text'] : ""; ?></span>
                </td>
                <td class="text-center">
                    <?php if($record['post_count'] >= $mobile_daily_limit): ?>
                        <span class="badge bg-danger">限制</span>
                    <?php else: ?>
                        <span class="badge bg-success">正常</span>
                    <?php endif; ?>
                </td>
                <td class="text-center">
                    <button class="btn btn-xs btn-warning" onclick="resetMobileLimit('<?php echo (isset($record['mobile'])) ? $record['mobile'] : ""; ?>')" style="font-size:11px;padding:2px 6px;">重置</button>
                </td>
            </tr>
            {foreachelse}
            <tr>
                <td colspan="7" class="text-center text-muted py-3">暂无记录</td>
            </tr>
            <?php endforeach; endif; ?>
        </tbody>
    </table>
</div>

<!-- 简约分页 -->
<?php if($total_pages > 1): ?>
<div class="d-flex justify-content-center mt-2">
    <nav>
        <ul class="pagination pagination-sm">
            <?php if($current_page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page={$current_page-1}">上一页</a>
            </li>
            <?php endif; ?>

            <?php for($i=1; $i<=$total_pages; $i++): ?>
            <li class="page-item <?php if($i == $current_page): ?>active<?php endif; ?>">
                <a class="page-link" href="?page=<?php echo $i ?? ""; ?>"><?php echo $i ?? ""; ?></a>
            </li>
            <?php endfor; ?>

            <?php if($current_page < $total_pages): ?>
            <li class="page-item">
                <a class="page-link" href="?page={$current_page+1}">下一页</a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
</div>
<?php endif; ?>

<script>
function resetMobileLimit(mobile) {
    if (!mobile) {
        mobile = document.getElementById('reset-mobile').value.trim();
    }
    
    if (!mobile) {
        alert('请输入手机号');
        return;
    }
    
    if (!confirm('确定要重置手机号 ' + mobile + ' 的发布限制吗？')) {
        return;
    }
    
    $.post('mobile_security.php', {
        action: 'reset_mobile_limit',
        mobile: mobile
    }, function(response) {
        if (response.success) {
            alert('重置成功');
            location.reload();
        } else {
            alert('重置失败: ' + response.message);
        }
    }, 'json');
}
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
