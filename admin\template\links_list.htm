{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-link"></i> 友情链接管理</h1>
        <div class="page-subtitle">管理网站友情链接</div>
    </div>
    <div class="page-actions">
        <button type="button" class="btn btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus"></i> 添加友情链接
        </button>
        <button type="button" class="btn btn-danger" onclick="batchDelete()" style="display: none;" id="batch-delete-btn">
            <i class="fas fa-trash"></i> 批量删除
        </button>
    </div>
</div>

<!-- 友情链接列表 -->
<div class="card">
    <div class="table-responsive">
        <table class="table table-enhanced">
            <thead>
                <tr>
                    <th width="40" class="text-center">
                        <input type="checkbox" id="select-all">
                    </th>
                    <th width="60" class="text-center">ID</th>
                    <th>链接名称</th>
                    <th>链接地址</th>
                    <th>描述</th>
                    <th width="80" class="text-center">排序</th>
                    <th width="80" class="text-center">状态</th>
                    <th width="100" class="text-center">打开方式</th>
                    <th width="120" class="text-center">创建时间</th>
                    <th width="180" class="text-center">操作</th>
                </tr>
            </thead>
            <tbody>
                {if !$links}
                <tr>
                    <td colspan="10" class="text-center" style="padding: 60px 20px; color: #999;">
                        <i class="fas fa-link" style="font-size: 48px; margin-bottom: 16px; color: #ddd;"></i>
                        <div style="font-size: 18px; margin-bottom: 8px;">暂无友情链接</div>
                        <div style="font-size: 14px;">点击上方按钮添加第一个友情链接</div>
                    </td>
                </tr>
                {else}
                {foreach $links as $item}
                <tr class="table-row">
                    <td class="text-center">
                        <input type="checkbox" name="ids[]" value="{$item.id}">
                    </td>
                    <td class="text-center table-id">{$item.id}</td>
                    <td>
                        <div class="table-title">
                            <a href="{$item.url}" target="{$item.target}" class="title-link">{$item.name}</a>
                        </div>
                    </td>
                    <td>
                        <a href="{$item.url}" target="{$item.target}" class="link-url" title="{$item.url}">
                            {php}echo mb_strlen($item['url']) > 40 ? mb_substr($item['url'], 0, 40) . '...' : $item['url'];{/php}
                        </a>
                    </td>
                    <td>
                        {if $item.description}
                            {php}echo mb_strlen($item['description']) > 30 ? mb_substr($item['description'], 0, 30) . '...' : $item['description'];{/php}
                        {else}
                            <span class="text-muted">--</span>
                        {/if}
                    </td>
                    <td class="text-center table-number">{$item.sort_order}</td>
                    <td class="text-center">
                        {if $item.status == 1}
                        <span class="status-tag success">启用</span>
                        {else}
                        <span class="status-tag danger">禁用</span>
                        {/if}
                    </td>
                    <td class="text-center">
                        {if $item.target == '_blank'}
                        <span class="badge bg-info">新窗口</span>
                        {else}
                        <span class="badge bg-secondary">当前窗口</span>
                        {/if}
                    </td>
                    <td class="text-center table-time">
                        {php}echo date('m-d H:i', $item['created_at']);{/php}
                    </td>
                    <td>
                        <div class="action-buttons-enhanced">
                            <a href="javascript:void(0)" onclick="showEditModal({$item.id})" class="btn-action btn-edit">
                                <i class="fas fa-edit"></i>
                                <span>编辑</span>
                            </a>
                            <a href="links.php?ajax=1&action=toggle_status&id={$item.id}" class="btn-action btn-{if $item.status == 1}warning{else}success{/if}" onclick="return confirm('确定要{if $item.status == 1}禁用{else}启用{/if}这个友情链接吗？')">
                                <i class="fas fa-{if $item.status == 1}eye-slash{else}eye{/if}"></i>
                                <span>{if $item.status == 1}禁用{else}启用{/if}</span>
                            </a>
                            <a href="links.php?ajax=1&action=delete&id={$item.id}" class="btn-action btn-delete btn-confirm" onclick="return confirm('确定要删除这个友情链接吗？')">
                                <i class="fas fa-trash"></i>
                                <span>删除</span>
                            </a>
                        </div>
                    </td>
                </tr>
                {/foreach}
                {/if}
            </tbody>
        </table>
    </div>
    
    {if $links && $pagination.total_pages > 1}
    <div class="card-footer">
        <div class="pagination-wrapper">
            <div class="pagination-info">
                共 {$pagination.total_items} 条记录
            </div>
            <div class="pagination">
                {if $pagination.has_prev}
                <a href="links.php?page={$pagination.prev_page}" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i>
                </a>
                {/if}
                
                {php}
                for ($i = 1; $i <= $pagination['total_pages']; $i++) {
                    if ($i == $pagination['current_page']) {
                        echo '<span class="pagination-btn active">' . $i . '</span>';
                    } else {
                        echo '<a href="links.php?page=' . $i . '" class="pagination-btn">' . $i . '</a>';
                    }
                }
                {/php}
                
                {if $pagination.has_next}
                <a href="links.php?page={$pagination.next_page}" class="pagination-btn">
                    <i class="fas fa-chevron-right"></i>
                </a>
                {/if}
            </div>
        </div>
    </div>
    {/if}
</div>

<!-- 添加友情链接模态框 -->
<div class="modal" id="addModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4><i class="fas fa-plus"></i> 添加友情链接</h4>
            <button type="button" class="modal-close" onclick="closeModal('addModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="addForm">
                <div class="form-group">
                    <label class="form-label">链接名称 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="text" name="name" class="form-control" placeholder="请输入链接名称" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接地址 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="url" name="url" class="form-control" placeholder="https://www.example.com" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接描述</label>
                    <div class="form-field">
                        <textarea name="description" class="form-control form-textarea" rows="3" placeholder="请输入链接描述（可选）"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">排序</label>
                    <div class="form-field">
                        <input type="number" name="sort_order" class="form-control" value="0" min="0" placeholder="数字越小排序越靠前">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">打开方式</label>
                    <div class="form-field">
                        <select name="target" class="form-control form-select">
                            <option value="_blank">新窗口打开</option>
                            <option value="_self">当前窗口打开</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div class="form-field">
                        <select name="status" class="form-control form-select">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeModal('addModal')">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitAddForm()">保存</button>
        </div>
    </div>
</div>

<!-- 编辑友情链接模态框 -->
<div class="modal" id="editModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4><i class="fas fa-edit"></i> 编辑友情链接</h4>
            <button type="button" class="modal-close" onclick="closeModal('editModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editForm">
                <input type="hidden" name="id" id="edit_id">
                
                <div class="form-group">
                    <label class="form-label">链接名称 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接地址 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="url" name="url" id="edit_url" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接描述</label>
                    <div class="form-field">
                        <textarea name="description" id="edit_description" class="form-control form-textarea" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">排序</label>
                    <div class="form-field">
                        <input type="number" name="sort_order" id="edit_sort_order" class="form-control" min="0" placeholder="数字越小排序越靠前">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">打开方式</label>
                    <div class="form-field">
                        <select name="target" id="edit_target" class="form-control form-select">
                            <option value="_blank">新窗口打开</option>
                            <option value="_self">当前窗口打开</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div class="form-field">
                        <select name="status" id="edit_status" class="form-control form-select">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeModal('editModal')">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitEditForm()">保存</button>
        </div>
    </div>
</div>

<script>
// 显示添加模态框
function showAddModal() {
    document.getElementById('addModal').style.display = 'flex';
}

// 显示编辑模态框
function showEditModal(id) {
    fetch('links.php?ajax=1&action=get&id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 填充表单数据
                document.getElementById('edit_id').value = data.data.id;
                document.getElementById('edit_name').value = data.data.name;
                document.getElementById('edit_url').value = data.data.url;
                document.getElementById('edit_description').value = data.data.description || '';
                document.getElementById('edit_sort_order').value = data.data.sort_order || 0;
                document.getElementById('edit_target').value = data.data.target || '_blank';
                document.getElementById('edit_status').value = data.data.status || 1;

                // 显示模态框
                document.getElementById('editModal').style.display = 'flex';
            } else {
                alert('获取数据失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            alert('请求失败：' + error.message);
        });
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 提交添加表单
function submitAddForm() {
    const form = document.getElementById('addForm');
    const formData = new FormData(form);

    fetch('links.php?ajax=1&action=add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('添加成功！');
            location.reload();
        } else {
            alert('添加失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 提交编辑表单
function submitEditForm() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);

    fetch('links.php?ajax=1&action=edit', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 批量删除
function batchDelete() {
    const checkboxes = document.querySelectorAll('input[name="ids[]"]:checked');
    if (checkboxes.length === 0) {
        alert('请选择要删除的项目');
        return;
    }

    if (!confirm('确定要删除选中的 ' + checkboxes.length + ' 个友情链接吗？此操作不可恢复！')) {
        return;
    }

    const ids = Array.from(checkboxes).map(cb => cb.value);

    fetch('links.php?ajax=1&action=batch_delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ids: ids})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('删除成功！');
            location.reload();
        } else {
            alert('删除失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('input[name="ids[]"]');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');

    // 全选/取消全选
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBatchDeleteBtn();
        });
    }

    // 单个复选框变化
    checkboxes.forEach(cb => {
        cb.addEventListener('change', function() {
            updateBatchDeleteBtn();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('input[name="ids[]"]:checked').length;
            if (selectAll) {
                selectAll.checked = checkedCount === checkboxes.length;
                selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            }
        });
    });

    // 更新批量删除按钮显示状态
    function updateBatchDeleteBtn() {
        const checkedCount = document.querySelectorAll('input[name="ids[]"]:checked').length;
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = checkedCount > 0 ? 'inline-flex' : 'none';
        }
    }
});

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
});
</script>

{include file="footer.htm"}
