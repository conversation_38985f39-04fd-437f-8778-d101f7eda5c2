<?php
/**
 * PC端AJAX提交问题调试工具
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 启动会话
session_start();

// 定义常量
define('IN_BTMPS', true);
define('ROOT_PATH', dirname(__FILE__) . '/');
define('INCLUDE_PATH', ROOT_PATH . 'include/');
define('CONFIG_PATH', ROOT_PATH . 'config/');

// 加载必要文件
require_once(INCLUDE_PATH . 'global.fun.php');

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    // 记录所有接收到的数据
    $debug_info = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $_SERVER['REQUEST_METHOD'],
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? '',
        'post_data' => $_POST,
        'files_data' => $_FILES,
        'session_data' => [
            'csrf_token' => $_SESSION['csrf_token'] ?? '无',
            'csrf_token_time' => $_SESSION['csrf_token_time'] ?? '无'
        ]
    ];
    
    // 记录到日志文件
    $log_file = ROOT_PATH . 'ajax_debug.log';
    file_put_contents($log_file, json_encode($debug_info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);
    
    // 验证CSRF令牌
    $token = $_POST['csrf_token'] ?? '';
    $csrf_valid = verify_csrf_token($token);
    
    // 验证Referer
    $referer_valid = validate_referer();
    
    // 返回调试信息
    echo json_encode([
        'success' => true,
        'message' => 'AJAX调试信息收集成功',
        'debug' => [
            'csrf_token_submitted' => $token ? substr($token, 0, 16) . '...' : '无',
            'csrf_token_session' => isset($_SESSION['csrf_token']) ? substr($_SESSION['csrf_token'], 0, 16) . '...' : '无',
            'csrf_valid' => $csrf_valid,
            'referer' => $_SERVER['HTTP_REFERER'] ?? '无',
            'referer_valid' => $referer_valid,
            'post_fields' => array_keys($_POST),
            'files_count' => count($_FILES),
            'session_active' => session_status() === PHP_SESSION_ACTIVE
        ]
    ]);
    exit;
}

// 生成CSRF令牌
$csrf_token = generate_csrf_token();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC端AJAX调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .debug-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        input, textarea, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; font-weight: bold; margin-bottom: 5px; }
        #debug-output { background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 20px; max-height: 400px; overflow-y: auto; }
        .loading { display: none; text-align: center; margin: 20px 0; }
    </style>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <h1>PC端AJAX调试工具</h1>
    
    <div class="debug-box">
        <h2>当前状态检查</h2>
        <p><strong>会话状态:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? '<span class="success">活跃</span>' : '<span class="error">非活跃</span>'; ?></p>
        <p><strong>CSRF令牌:</strong> <?php echo isset($_SESSION['csrf_token']) ? '<span class="success">存在</span>' : '<span class="error">不存在</span>'; ?></p>
        <p><strong>当前令牌:</strong> <?php echo $csrf_token ? substr($csrf_token, 0, 20) . '...' : '无'; ?></p>
        <p><strong>jQuery加载:</strong> <span id="jquery-status">检查中...</span></p>
    </div>
    
    <div class="debug-box">
        <h2>AJAX提交测试</h2>
        <form id="test-form">
            <div class="form-group">
                <label>标题:</label>
                <input type="text" name="title" value="AJAX测试标题" required>
            </div>
            
            <div class="form-group">
                <label>内容:</label>
                <textarea name="content" rows="4" required>这是AJAX测试内容</textarea>
            </div>
            
            <div class="form-group">
                <label>联系人:</label>
                <input type="text" name="contact_name" value="测试用户" required>
            </div>
            
            <div class="form-group">
                <label>手机号:</label>
                <input type="text" name="contact_mobile" value="13800138000" required>
            </div>
            
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="ajax" value="1">
            <input type="hidden" name="submit" value="1">
            
            <button type="submit">测试AJAX提交</button>
        </form>
        
        <div class="loading" id="loading">
            <p>正在提交，请稍候...</p>
        </div>
    </div>
    
    <div class="debug-box">
        <h2>调试输出</h2>
        <div id="debug-output">等待AJAX请求...</div>
    </div>
    
    <div class="debug-box">
        <h2>网络请求监控</h2>
        <p>请打开浏览器开发者工具的Network标签页来监控AJAX请求。</p>
        <p><strong>检查要点:</strong></p>
        <ul>
            <li>请求URL是否正确</li>
            <li>请求方法是否为POST</li>
            <li>Content-Type是否正确</li>
            <li>请求数据是否完整</li>
            <li>响应状态码</li>
            <li>响应内容</li>
        </ul>
    </div>
    
    <script>
        // 检查jQuery是否加载
        $(document).ready(function() {
            $('#jquery-status').html('<span class="success">已加载</span>');
            
            // AJAX表单提交
            $('#test-form').on('submit', function(e) {
                e.preventDefault();
                
                $('#loading').show();
                $('#debug-output').html('正在发送请求...');
                
                var formData = new FormData(this);
                
                // 显示发送的数据
                var debugInfo = '<h3>发送的数据:</h3>';
                for (var pair of formData.entries()) {
                    debugInfo += '<p><strong>' + pair[0] + ':</strong> ' + pair[1] + '</p>';
                }
                $('#debug-output').html(debugInfo);
                
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function(xhr) {
                        console.log('发送AJAX请求...');
                        console.log('URL:', window.location.href);
                    },
                    success: function(response, textStatus, xhr) {
                        $('#loading').hide();
                        
                        var output = '<h3>请求成功:</h3>';
                        output += '<p><strong>状态:</strong> ' + textStatus + '</p>';
                        output += '<p><strong>响应类型:</strong> ' + xhr.getResponseHeader('Content-Type') + '</p>';
                        output += '<h4>响应数据:</h4>';
                        output += '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                        
                        $('#debug-output').html(output);
                        
                        console.log('AJAX成功:', response);
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        $('#loading').hide();
                        
                        var output = '<h3 class="error">请求失败:</h3>';
                        output += '<p><strong>状态码:</strong> ' + xhr.status + '</p>';
                        output += '<p><strong>状态文本:</strong> ' + textStatus + '</p>';
                        output += '<p><strong>错误:</strong> ' + errorThrown + '</p>';
                        output += '<h4>响应内容:</h4>';
                        output += '<pre>' + xhr.responseText + '</pre>';
                        
                        $('#debug-output').html(output);
                        
                        console.error('AJAX失败:', textStatus, errorThrown);
                        console.error('响应:', xhr.responseText);
                    },
                    complete: function(xhr, textStatus) {
                        console.log('AJAX完成:', textStatus);
                    }
                });
            });
        });
        
        // 如果jQuery未加载，显示错误
        if (typeof jQuery === 'undefined') {
            document.getElementById('jquery-status').innerHTML = '<span class="error">未加载</span>';
        }
    </script>
    
    <hr>
    <p><small>调试完成后请删除此文件</small></p>
</body>
</html>
