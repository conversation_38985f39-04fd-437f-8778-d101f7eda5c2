<?php
/**
 * 发布信息调试测试文件
 * 用于诊断PC端和移动端发布信息失败的问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('IN_BTMPS', true);
define('ROOT_PATH', dirname(__FILE__) . '/');
define('INCLUDE_PATH', ROOT_PATH . 'include/');
define('CONFIG_PATH', ROOT_PATH . 'config/');

echo "<h1>发布信息功能调试测试</h1>";

// 1. 检查PHP扩展
echo "<h2>1. PHP扩展检查</h2>";
$required_extensions = ['mysqli', 'pdo', 'pdo_mysql', 'gd', 'json'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✓ 已加载' : '✗ 未加载';
    $color = extension_loaded($ext) ? 'green' : 'red';
    echo "<p style='color: {$color}'>{$ext}: {$status}</p>";
}

// 2. 检查配置文件
echo "<h2>2. 配置文件检查</h2>";
$config_file = CONFIG_PATH . 'config.db.php';
if (file_exists($config_file)) {
    echo "<p style='color: green'>✓ 数据库配置文件存在</p>";
    include $config_file;
    echo "<p>数据库主机: {$config['db_host']}</p>";
    echo "<p>数据库名: {$config['db_name']}</p>";
    echo "<p>数据库用户: {$config['db_user']}</p>";
} else {
    echo "<p style='color: red'>✗ 数据库配置文件不存在</p>";
    exit;
}

// 3. 检查数据库连接
echo "<h2>3. 数据库连接检查</h2>";
if (extension_loaded('mysqli')) {
    try {
        $mysqli = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name'], $config['db_port']);
        
        if ($mysqli->connect_error) {
            echo "<p style='color: red'>✗ 数据库连接失败: " . $mysqli->connect_error . "</p>";
        } else {
            echo "<p style='color: green'>✓ 数据库连接成功</p>";
            
            // 检查关键表是否存在
            $tables = ['posts', 'post_contents', 'categories', 'regions'];
            foreach ($tables as $table) {
                $result = $mysqli->query("SHOW TABLES LIKE '{$table}'");
                if ($result && $result->num_rows > 0) {
                    echo "<p style='color: green'>✓ 表 {$table} 存在</p>";
                } else {
                    echo "<p style='color: red'>✗ 表 {$table} 不存在</p>";
                }
            }
        }
        $mysqli->close();
    } catch (Exception $e) {
        echo "<p style='color: red'>✗ 数据库连接异常: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red'>✗ mysqli扩展未加载，无法测试数据库连接</p>";
}

// 4. 检查核心文件
echo "<h2>4. 核心文件检查</h2>";
$core_files = [
    'include/mysql.class.php',
    'include/common.inc.php',
    'include/global.fun.php',
    'post.php'
];

foreach ($core_files as $file) {
    if (file_exists(ROOT_PATH . $file)) {
        echo "<p style='color: green'>✓ {$file} 存在</p>";
    } else {
        echo "<p style='color: red'>✗ {$file} 不存在</p>";
    }
}

// 5. 检查模板文件
echo "<h2>5. 模板文件检查</h2>";
$template_files = [
    'template/pc/post.htm',
    'template/m/post.htm',
    'template/pc/edit.htm',
    'template/m/edit.htm'
];

foreach ($template_files as $file) {
    if (file_exists(ROOT_PATH . $file)) {
        echo "<p style='color: green'>✓ {$file} 存在</p>";
    } else {
        echo "<p style='color: red'>✗ {$file} 不存在</p>";
    }
}

// 6. 检查权限
echo "<h2>6. 目录权限检查</h2>";
$directories = [
    'data/logs',
    'data/compiled',
    'uploads'
];

foreach ($directories as $dir) {
    $full_path = ROOT_PATH . $dir;
    if (is_dir($full_path)) {
        if (is_writable($full_path)) {
            echo "<p style='color: green'>✓ {$dir} 可写</p>";
        } else {
            echo "<p style='color: orange'>⚠ {$dir} 不可写</p>";
        }
    } else {
        echo "<p style='color: red'>✗ {$dir} 目录不存在</p>";
    }
}

// 7. 模拟发布测试
echo "<h2>7. 模拟发布测试</h2>";
if (extension_loaded('mysqli') && isset($mysqli) && !$mysqli->connect_error) {
    echo "<p>开始模拟发布测试...</p>";
    
    // 模拟POST数据
    $_POST = [
        'title' => '测试信息标题',
        'content' => '这是一个测试信息的详细内容，用于测试发布功能是否正常工作。',
        'contact_name' => '测试用户',
        'contact_mobile' => '13800138000',
        'region_id' => 1,
        'expire_days' => 30,
        'password' => 'test123',
        'csrf_token' => 'test_token',
        'submit' => '1'
    ];
    
    echo "<p>模拟POST数据已设置</p>";
    echo "<p>标题: " . $_POST['title'] . "</p>";
    echo "<p>联系人: " . $_POST['contact_name'] . "</p>";
    echo "<p>手机号: " . $_POST['contact_mobile'] . "</p>";
    
} else {
    echo "<p style='color: red'>无法进行模拟发布测试，数据库连接失败</p>";
}

echo "<h2>8. 建议解决方案</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h3>根据检查结果，建议按以下步骤解决：</h3>";
echo "<ol>";
echo "<li><strong>修复PHP扩展问题：</strong>确保mysqli、pdo_mysql、gd等扩展正确加载</li>";
echo "<li><strong>检查数据库服务：</strong>确保MySQL服务正在运行</li>";
echo "<li><strong>验证数据库配置：</strong>检查config/config.db.php中的连接参数</li>";
echo "<li><strong>检查目录权限：</strong>确保data/logs、uploads等目录可写</li>";
echo "<li><strong>查看错误日志：</strong>检查PHP错误日志和应用日志</li>";
echo "</ol>";
echo "</div>";

?>
