<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>默认模板<?php echo $seo_title ?? ""; ?></title>
    <meta name="keywords" content="<?php if($category['seo_keywords']): ?><?php echo (isset($category['seo_keywords'])) ? $category['seo_keywords'] : ""; ?><?php else: ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?><?php endif; ?>">
    <meta name="description" content="<?php if($category['seo_description']): ?><?php echo (isset($category['seo_description'])) ? $category['seo_description'] : ""; ?><?php else: ?><?php echo $site_name ?? ""; ?>提供最新的<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>信息<?php endif; ?>">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/category.css?v=<?php echo time(); ?>">
   

    <script src="/static/js/common.js"></script>
    <script src="/static/js/list-item-clickable.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
  
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title"><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></div>
            <div class="header-right">
                <div class="header-search-icon" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- 搜索层 -->
    <div class="search-layer" id="searchLayer">
        <div class="search-header">
            <a href="javascript:void(0);" class="search-back" onclick="toggleSearch()">
                <i class="fas fa-chevron-left"></i>
            </a>
            <form action="/search.php" method="get" class="search-form">
                <i class="fas fa-search search-icon"></i>
                <input type="text" name="keyword" class="search-input" placeholder="在<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>中搜索..." autocomplete="off">
            </form>
            <button type="button" class="search-cancel" onclick="toggleSearch()">取消</button>
        </div>
        <div class="search-content">
            <div class="search-history">
                <div class="search-section-title">
                    <span>搜索历史</span>
                    <button type="button" class="search-clear" onclick="clearSearchHistory()">
                        <i class="fas fa-trash-alt"></i> 清空
                    </button>
                </div>
                <div class="search-tags" id="searchHistoryTags"></div>
            </div>
            <div class="search-hot">
                <div class="search-section-title">
                    <span>热门搜索</span>
                </div>
                <div class="search-tags">
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">招聘</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">兼职</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">房屋出租</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">二手车</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">手机</a>
                </div>
            </div>
        </div>
    </div>

    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator"></span>
            <?php if($category['parent_id'] > 0 && $parent_category): ?>
            <a href="/<?php echo (isset($parent_category['pinyin'])) ? $parent_category['pinyin'] : ""; ?>/"><?php echo (isset($parent_category['name'])) ? $parent_category['name'] : ""; ?></a>
            <span class="separator"></span>
            <?php endif; ?>
            
            <span class="current"><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></span>
        </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container" style="min-height: 46px;">
        <?php if(null !== ($subCategories ?? null) && !empty($subCategories)): ?>
        <div class="filter-section">
            <div class="filter-title">栏目：</div>
            <button class="filter-more" onclick="toggleFilterOptions(this)"><i class="fas fa-chevron-down"></i></button>
            <div class="filter-options">
                <?php if(null !== ($current_is_subcategory ?? null) && !empty($current_is_subcategory)): ?>
                <a href="<?php echo null !== ($parentUrl ?? null) && ($parentUrl ?? null) !== "" ? $parentUrl ?? null : '#'; ?>" <?php if(null !== ($category ?? null) && !empty($category) && null !== ($parent_category ?? null) && !empty($parent_category) && null !== ($category ?? null) && is_array($category) && array_key_exists('id', $category) && null !== ($parent_category ?? null) && is_array($parent_category) && array_key_exists('id', $parent_category) && $category['id'] == $parent_category['id']): ?>class="active"<?php endif; ?>>全部</a>
                <?php else: ?>
                <a href="<?php echo null !== ($baseUrl ?? null) && ($baseUrl ?? null) !== "" ? $baseUrl ?? null : '#'; ?>" class="active">全部</a>
                <?php endif; ?>
                
                <?php if(null !== ($subCategories ?? null) && is_array($subCategories)): foreach($subCategories as $subCat): ?>
                <a href="<?php echo null !== ((null !== ($subCat ?? null)) ? ($subCat['url']) : null) && ((null !== ($subCat ?? null)) ? ($subCat['url']) : null) !== "" ? (null !== ($subCat ?? null)) ? ($subCat['url']) : null : '#'; ?>" 
                   <?php if((null !== ($subCat ?? null) && is_array($subCat) && array_key_exists('is_current', $subCat) && $subCat['is_current']) || (null !== ($current_subcategory_id ?? null) && null !== ($current_subcategory_id ?? null) && !empty($current_subcategory_id) && null !== ($subCat ?? null) && is_array($subCat) && array_key_exists('id', $subCat) && $current_subcategory_id == $subCat['id'])): ?>class="active"<?php endif; ?>>
                   <?php echo null !== ((null !== ($subCat ?? null)) ? ($subCat['name']) : null) && ((null !== ($subCat ?? null)) ? ($subCat['name']) : null) !== "" ? (null !== ($subCat ?? null)) ? ($subCat['name']) : null : '未命名栏目'; ?>
                </a>
                <?php endforeach; endif; ?>
            </div>
        </div>
    <?php endif; ?>
        
        <?php if(null !== ($area_arr ?? null) && !empty($area_arr)): ?>
        <div class="filter-section">
            <div class="filter-title">区域：</div>
            <button class="filter-more" onclick="toggleFilterOptions(this)"><i class="fas fa-chevron-down"></i></button>
            <div class="filter-options">
                <?php if(null !== ($showCities ?? null) && !empty($showCities)): ?>
                    <?php if(null !== ($area_arr ?? null) && is_array($area_arr)): foreach($area_arr as $area): ?>
                        <?php if(null !== ($area ?? null) && is_array($area) && array_key_exists('is_unlimited', $area) && !empty($area['is_unlimited'])): ?>
                        <a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="<?php if(empty($areaId) || $areaId == 0): ?>active<?php endif; ?> back-to-all">全部</a>
                        <?php elseif(null !== ($area ?? null) && is_array($area) && array_key_exists('is_city', $area) && !empty($area['is_city'])): ?>
                        <a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" <?php if(null !== ($areaId ?? null) && !empty($areaId) && $areaId == $area['id']): ?>class="active"<?php endif; ?>><?php echo (isset($area['name'])) ? $area['name'] : ""; ?></a>
                        <?php endif; ?>
                    <?php endforeach; endif; ?>
                <?php else: ?>
                    <?php if(null !== ($area_arr ?? null) && is_array($area_arr)): foreach($area_arr as $area): ?>
                        <?php if(null !== ($area ?? null) && is_array($area) && array_key_exists('is_unlimited', $area) && !empty($area['is_unlimited'])): ?>
                        <a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="<?php if(empty($areaId) || $areaId == 0): ?>active<?php endif; ?> back-to-all">全部</a>
                        <?php elseif(null !== ($area ?? null) && is_array($area) && array_key_exists('is_province', $area) && !empty($area['is_province'])): ?>
                        <a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" <?php if(null !== ($areaId ?? null) && !empty($areaId) && $areaId == $area['id']): ?>class="active"<?php endif; ?>><?php echo (isset($area['name'])) ? $area['name'] : ""; ?></a>
                        <?php endif; ?>
                    <?php endforeach; endif; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="tab-container">
        <div class="tab-header">
            <div class="tab-title" style="font-weight: bold;">
                <span style="border-left: 2px solid #4285f4; padding-left: 15px; line-height: 1.2; display: inline-block;"><?php if(null !== ($category ?? null) && is_array($category) && array_key_exists('name', $category)): ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?><?php else: ?>信息列表<?php endif; ?></span>
            </div>
        </div>

        <div class="tab-content active">
            <div class="simple-list" id="simple-list">
                <?php if(null !== ($posts ?? null) && !empty($posts)): ?>
                    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
                    <?php
                    // 检查是否显示置顶标记（需要检查过期时间）
                    $current_time = time();
                    $show_top_tag = false;

                    // 检查大分类置顶
                    if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
                        if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
                            $show_top_tag = true;
                        }
                    }

                    // 检查小分类置顶
                    if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
                        if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
                            $show_top_tag = true;
                        }
                    }

                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <?php if($show_top_tag): ?>
                    <div class="list-item is-top">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?><span class="top-tag">顶</span></a>
                                    <?php else: ?>
                                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?><span class="top-tag">顶</span></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; endif; ?>
                    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
                    <?php
                    // 重新检查置顶状态（用于非置顶信息显示）
                    $current_time = time();
                    $show_top_tag = false;

                    // 检查大分类置顶
                    if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
                        if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
                            $show_top_tag = true;
                        }
                    }

                    // 检查小分类置顶
                    if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
                        if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
                            $show_top_tag = true;
                        }
                    }
                    ?>
                    <?php if(!$show_top_tag): ?>
                    <?php
                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <div class="list-item">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                                    <?php else: ?>
                                    <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; endif; ?>
        <?php else: ?>
            <div class="empty-state" style="text-align: center; padding: 35px 25px; margin: 20px 15px; border-radius: 12px; background: white; ">
                <div style="width: 80px; height: 80px; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; background: rgba(var(--primary-color-rgb), 0.08); border-radius: 50%;">
                    <i class="fas fa-inbox" style="font-size: 32px; color: var(--primary-color);"></i>
                </div>
                <h3 style="margin: 0 0 10px; font-size: 18px; font-weight: 500; color: #333;">暂无信息</h3>
                <p style="margin: 0 0 25px; font-size: 14px; color: #999; line-height: 1.5;">该分类下还没有相关信息，立即发布成为第一个吧</p>
                <a href="/post.php?category_id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" style="display: inline-block; padding: 12px 30px; background: var(--primary-color); color: white; text-decoration: none; border-radius: 25px; font-size: 15px; font-weight: 500; box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.2); transition: all 0.3s ease;">立即发布</a>
            </div>
        <?php endif; ?>
            </div>
        </div>
    </div>



    <?php if(null !== ($totalPages ?? null) && !empty($totalPages) && $totalPages > 1): ?>
    <div class="pagination">
        <?php echo $pagination ?? ""; ?>
    </div>
    <?php endif; ?>

    <!-- 底部版权 -->
    <footer>
    <div class="container">
       {literal}{block:{/literal}m_footer_nav{literal}}{/literal}
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <!-- 底部导航栏 -->
    <!-- 移动端底部导航栏 -->
<nav class="navbar">
    <a href="/" class="nav-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-home"></i></span>
        <span class="nav-text">首页</span>
    </a>
    <a href="/category.php" class="nav-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-th-large"></i></span>
        <span class="nav-text">分类</span>
    </a>
    <a href="/post.php" class="nav-item publish <?php if($current_page == 'post'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-plus"></i></span>
        <span class="nav-text">发布</span>
    </a>
    <a href="/message.php" class="nav-item <?php if($current_page == 'message'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-comment-alt"></i></span>
        <span class="nav-text">消息</span>
    </a>
    <a href="/member/" class="nav-item <?php if($current_page == 'member'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-user"></i></span>
        <span class="nav-text">我的</span>
    </a>
</nav>


    <script>
        // ===== 全局变量 =====
        let searchLayerShowing = false;
        const SEARCH_HISTORY_KEY = 'search_history';
        let isLoading = false;

        // 获取分页模式
        var paginationMode = '<?php echo $pagination_mode ?? ""; ?>';

        // ===== 页面初始化 =====
        document.addEventListener('DOMContentLoaded', function() {
            // 防止FOUC闪烁
            document.documentElement.classList.add('no-fouc');

            // 根据分页模式初始化不同功能
            if (paginationMode === 'infinite') {
                initInfiniteScroll();
            } else if (paginationMode === 'loadmore') {
                initLoadMoreButton();
            }

            // 初始化其他功能
            initPageFeatures();
        });

        // 初始化加载更多按钮
        function initLoadMoreButton() {
            const loadMoreBtn = document.querySelector('.load-more');
            if (loadMoreBtn) {
                console.log('找到加载更多按钮:', loadMoreBtn);
                console.log('按钮属性:', {
                    nextPage: loadMoreBtn.dataset.nextPage,
                    catId: loadMoreBtn.dataset.catId,
                    areaId: loadMoreBtn.dataset.areaId
                });

                loadMoreBtn.addEventListener('click', function() {
                    if (isLoading) return;

                    const nextPage = parseInt(this.dataset.nextPage);
                    const catId = parseInt(this.dataset.catId);
                    const areaId = parseInt(this.dataset.areaId);

                    console.log('点击加载更多，参数:', { nextPage, catId, areaId });

                    loadMorePosts(nextPage, catId, areaId);
                });
            } else {
                console.log('未找到加载更多按钮');
            }
        }
        
        // 加载更多文章函数
        function loadMorePosts(page, catId, areaId) {
            if (isLoading) return;
            
            const loadMoreBtn = document.querySelector('.load-more');
            if (!loadMoreBtn) return;
            
            isLoading = true;
            loadMoreBtn.classList.add('loading');
            loadMoreBtn.innerHTML = '加载中...';
            
            // 构建URL
            const url = `/api/posts_loadmore.php?cat_id=${catId}&page=${page}&area_id=${areaId}`;
            console.log('正在加载URL:', url);
            
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    isLoading = false;
                    console.log('收到数据:', data);

                    if (data.code === 200) {
                        // 获取列表容器
                        const simpleList = document.getElementById('simple-list');

                        console.log('列表容器:', simpleList);

                        if (!simpleList) {
                            console.error('未找到列表容器');
                            return;
                        }

                        const tempContainer = document.createElement('div');
                        tempContainer.innerHTML = data.html;
                        console.log('临时容器HTML:', data.html);

                        // 处理列表项目
                        const listItems = tempContainer.querySelectorAll('.list-item');
                        console.log('找到列表项目:', listItems.length);

                        listItems.forEach((item, index) => {
                            console.log(`添加列表项目${index + 1}:`, item);
                            simpleList.appendChild(item);
                        });

                        if (listItems.length === 0) {
                            console.warn('没有找到任何列表项目元素，检查返回的HTML结构');
                            console.log('返回的HTML:', data.html);
                        }

                        // 重新初始化列表项点击功能
                        if (typeof initSimpleItemsClickable === 'function') {
                            initSimpleItemsClickable();
                        }

                        // 更新加载更多按钮
                        if (data.has_more) {
                            loadMoreBtn.classList.remove('loading');
                            loadMoreBtn.innerHTML = '点击加载更多';
                            loadMoreBtn.dataset.nextPage = data.next_page;
                            console.log('设置下一页:', data.next_page);
                        } else {
                            // 没有更多内容
                            const container = loadMoreBtn.closest('.pagination-container');
                            if (container) {
                                container.innerHTML = '<div class="load-more-end">已加载全部内容</div>';
                            } else {
                                loadMoreBtn.parentNode.innerHTML = '<div class="load-more-end">已加载全部内容</div>';
                            }
                            console.log('没有更多内容');
                        }

                        // 隐藏无限滚动加载指示器
                        if (paginationMode === 'infinite') {
                            hideInfiniteLoadingIndicator();
                        }
                    } else {
                        // 加载失败
                        loadMoreBtn.classList.remove('loading');
                        loadMoreBtn.innerHTML = '加载失败，点击重试';
                        console.error('加载失败:', data.msg);

                        // 隐藏无限滚动加载指示器
                        if (paginationMode === 'infinite') {
                            hideInfiniteLoadingIndicator();
                        }
                    }
                })
                .catch(error => {
                    isLoading = false;
                    loadMoreBtn.classList.remove('loading');
                    loadMoreBtn.innerHTML = '加载失败，点击重试';
                    console.error('加载更多内容失败:', error);

                    // 隐藏无限滚动加载指示器
                    if (paginationMode === 'infinite') {
                        hideInfiniteLoadingIndicator();
                    }
                });
        }

        // ===== 无限滚动功能 =====

        // 初始化无限滚动
        function initInfiniteScroll() {
            console.log('初始化无限滚动模式');

            // 隐藏加载更多按钮（无限滚动不需要按钮）
            const loadMoreBtn = document.querySelector('.load-more');
            if (loadMoreBtn) {
                loadMoreBtn.style.display = 'none';
            }

            // 创建滚动监听器
            let scrollTimer = null;

            window.addEventListener('scroll', function() {
                // 防抖处理，避免频繁触发
                if (scrollTimer) {
                    clearTimeout(scrollTimer);
                }

                scrollTimer = setTimeout(function() {
                    checkScrollPosition();
                }, 100);
            });

            // 初始检查一次
            setTimeout(checkScrollPosition, 1000);
        }

        // 检查滚动位置
        function checkScrollPosition() {
            if (isLoading) return;

            // 获取页面滚动信息
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // 计算距离底部的距离
            const distanceFromBottom = documentHeight - (scrollTop + windowHeight);

            console.log('滚动检查:', {
                scrollTop: scrollTop,
                windowHeight: windowHeight,
                documentHeight: documentHeight,
                distanceFromBottom: distanceFromBottom
            });

            // 当距离底部小于200px时触发加载
            if (distanceFromBottom < 200) {
                triggerInfiniteLoad();
            }
        }

        // 触发无限加载
        function triggerInfiniteLoad() {
            const loadMoreBtn = document.querySelector('.load-more');
            if (!loadMoreBtn) {
                console.log('没有找到加载更多按钮，可能已经加载完毕');
                return;
            }

            // 检查是否还有更多内容
            if (loadMoreBtn.classList.contains('load-more-end') ||
                loadMoreBtn.textContent.includes('已加载全部内容')) {
                console.log('已经没有更多内容了');
                return;
            }

            const nextPage = parseInt(loadMoreBtn.dataset.nextPage);
            const catId = parseInt(loadMoreBtn.dataset.catId);
            const areaId = parseInt(loadMoreBtn.dataset.areaId);

            console.log('无限滚动触发加载，参数:', { nextPage, catId, areaId });

            // 显示加载指示器
            showInfiniteLoadingIndicator();

            // 调用加载更多函数
            loadMorePosts(nextPage, catId, areaId);
        }

        // 显示无限加载指示器
        function showInfiniteLoadingIndicator() {
            // 检查是否已经存在指示器
            let indicator = document.getElementById('infinite-loading-indicator');

            if (!indicator) {
                // 创建加载指示器
                indicator = document.createElement('div');
                indicator.id = 'infinite-loading-indicator';
                indicator.className = 'infinite-loading-indicator';
                indicator.innerHTML = `
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载更多内容...</div>
                `;

                // 添加样式
                indicator.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 10px 20px;
                    border-radius: 20px;
                    font-size: 14px;
                    z-index: 1000;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                `;

                // 添加旋转动画样式
                const style = document.createElement('style');
                style.textContent = `
                    .loading-spinner {
                        width: 16px;
                        height: 16px;
                        border: 2px solid #ffffff40;
                        border-top: 2px solid #ffffff;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);

                document.body.appendChild(indicator);
            }

            indicator.style.display = 'flex';
        }

        // 隐藏无限加载指示器
        function hideInfiniteLoadingIndicator() {
            const indicator = document.getElementById('infinite-loading-indicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        }


        
        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化页面其他功能
        function initPageFeatures() {
            // 记录筛选参数
            const areaParam = getUrlParam('area_id');
            const categoryParam = window.location.pathname.split('/')[1];
            
            // 识别筛选区域类型
            document.querySelectorAll('.filter-section').forEach(function(section, index) {
                const titleText = section.querySelector('.filter-title').textContent;
                section.setAttribute('data-filter-type', 
                    titleText.includes('区域') ? 'area' : 
                    titleText.includes('分类') ? 'category' : 
                    'other-' + index);
            });
            
            // 处理筛选区域显示
            document.querySelectorAll('.filter-options').forEach(function(el) {
                // 处理"更多"按钮显示
                if (el.scrollWidth <= el.clientWidth) {
                    const moreBtn = el.previousElementSibling;
                    if (moreBtn && moreBtn.classList.contains('filter-more')) {
                        moreBtn.style.display = 'none';
                    }
                    el.classList.add('no-more');
                }
                
                // 处理选中项位置
                const activeOption = el.querySelector('a.active');
                if (activeOption) {
                    const optionLeft = activeOption.offsetLeft;
                    const optionWidth = activeOption.offsetWidth;
                    const containerWidth = el.clientWidth;
                    
                    if (optionLeft + optionWidth > containerWidth) {
                        // 选中项超出可视区域，设置展开状态
                        const filterSection = el.closest('.filter-section');
                        if (filterSection) {
                            filterSection.setAttribute('data-keep-expanded', 'true');
                        }
                        el.classList.add('expanded');
                        
                        // 更新按钮状态
                        const moreBtn = el.previousElementSibling;
                        if (moreBtn && moreBtn.classList.contains('filter-more')) {
                            moreBtn.classList.add('active');
                            
                            const icon = moreBtn.querySelector('i');
                            if (icon) {
                                icon.classList.remove('fa-chevron-down');
                                icon.classList.add('fa-chevron-up');
                            }
                        }
                        
                        // 滚动到选中项
                        el.scrollLeft = Math.max(0, optionLeft - 20);
                    }
                }
            });
            
            // 保存筛选记录
            try {
                localStorage.setItem('browsed_filters', JSON.stringify({
                    lastCategory: categoryParam,
                    lastArea: areaParam
                }));
            } catch (e) {
                console.error('Failed to save browsed filters', e);
            }
            
            // 显示筛选区域
            document.querySelector('.filter-container').classList.add('loaded');
            
            // 显示文本列表
            document.querySelector('.simple-list').style.display = 'block';
            
            // 显示内容
            setTimeout(function() {
                document.documentElement.classList.remove('no-fouc');
                document.body.classList.add('content-loaded');
            }, 50);
            
            // 设置点击事件代理
            document.addEventListener('click', function(e) {
                // 处理搜索层外部点击关闭
                if (searchLayerShowing && document.getElementById('searchLayer').classList.contains('active')) {
                    const searchLayer = document.getElementById('searchLayer');
                    const isClickInside = searchLayer.contains(e.target) || e.target.closest('.header-search-icon');
                    if (!isClickInside) {
                        toggleSearch();
                    }
                }
                
                // 记录筛选区域点击
                if (e.target.tagName === 'A' && e.target.closest('.filter-options')) {
                    const filterSection = e.target.closest('.filter-section');
                    if (filterSection) {
                        filterSection.setAttribute('data-user-clicked', 'true');
                    }
                }
            });
        }
        
        // 搜索层控制
        function toggleSearch() {
            const searchLayer = document.getElementById('searchLayer');
            searchLayerShowing = !searchLayerShowing;
            
            if (searchLayerShowing) {
                // 显示搜索层
                searchLayer.style.display = 'block';
                setTimeout(() => {
                    searchLayer.classList.add('active');
                    // 聚焦输入框
                    searchLayer.querySelector('.search-input').focus();
                    // 渲染搜索历史
                    renderSearchHistory();
                    // 阻止背景滚动
                    document.body.style.overflow = 'hidden';
                }, 10);
            } else {
                // 隐藏搜索层
                searchLayer.classList.remove('active');
                setTimeout(() => {
                    searchLayer.style.display = 'none';
                    // 清空输入
                    searchLayer.querySelector('.search-input').value = '';
                    // 恢复滚动
                    document.body.style.overflow = '';
                }, 200);
            }
        }
        
        // 获取搜索历史
        function getSearchHistory() {
            try {
                const history = localStorage.getItem(SEARCH_HISTORY_KEY);
                return history ? JSON.parse(history) : [];
            } catch (e) {
                console.error('Failed to get search history:', e);
                return [];
            }
        }
        
        // 渲染搜索历史
        function renderSearchHistory() {
            const container = document.getElementById('searchHistoryTags');
            if (!container) return;
            
            const history = getSearchHistory();
            
            container.innerHTML = history.length ? 
                history.map(keyword => `<a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">${keyword}</a>`).join('') :
                '<div style="color: #999; font-size: 13px;">暂无搜索历史</div>';
        }
        
        // 搜索标签点击
        function searchTag(element) {
            const keyword = element.textContent;
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = keyword;
                // 添加搜索历史
                addSearchHistory(keyword);
                // 提交表单
                const searchForm = document.querySelector('.search-form');
                if (searchForm) {
                    searchForm.submit();
                }
            }
        }
        
        // 添加搜索历史
        function addSearchHistory(keyword) {
            if (!keyword) return;
            
            try {
                let history = getSearchHistory();
                history = history.filter(item => item !== keyword);
                history.unshift(keyword);
                history = history.slice(0, 10);
                localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
            } catch (e) {
                console.error('Failed to add search history:', e);
            }
        }
        
        // 筛选选项展开/收起
        function toggleFilterOptions(btn) {
            const section = btn.closest('.filter-section');
            const options = section.querySelector('.filter-options');
            
            // 如果需要保持展开，则不允许关闭
            if (options.classList.contains('expanded') && section.getAttribute('data-keep-expanded') === 'true') {
                // 如果已经被用户点击过，允许关闭
                if (section.getAttribute('data-user-clicked') === 'true') {
                    section.removeAttribute('data-keep-expanded');
                }
            }
            
            // 切换展开/收起状态
            options.classList.toggle('expanded');
            btn.classList.toggle('active');
            
            // 更新图标
            const icon = btn.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
            }
        }
    </script>
</body>
</html> 