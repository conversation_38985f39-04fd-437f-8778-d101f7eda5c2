<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<!-- 页面标题 -->
<div class="page-title">
    <h1>操作日志管理</h1>
    <div class="d-flex gap-2">
        <a href="?op=export<?php echo $url_params ?? ""; ?>" class="btn btn-sm btn-outline">
            <i class="fas fa-download"></i>
            <span>导出日志</span>
        </a>
    </div>
</div>

<!-- 统计信息 -->
<div class="section">
    <div class="card">
        <div class="stats-compact">
            <div class="stat-compact">
                <span class="stat-number"><?php if(null !== ($today_stats ?? null) && is_array($today_stats) && array_key_exists('total_today', $today_stats)): ?><?php echo (isset($today_stats['total_today'])) ? $today_stats['total_today'] : ""; ?><?php else: ?>0<?php endif; ?></span>
                <span class="stat-text">今日操作</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number"><?php if(null !== ($today_stats ?? null) && is_array($today_stats) && array_key_exists('unique_ips_today', $today_stats)): ?><?php echo (isset($today_stats['unique_ips_today'])) ? $today_stats['unique_ips_today'] : ""; ?><?php else: ?>0<?php endif; ?></span>
                <span class="stat-text">今日活跃IP</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number"><?php if(null !== ($today_stats ?? null) && is_array($today_stats) && array_key_exists('active_users_today', $today_stats)): ?><?php echo (isset($today_stats['active_users_today'])) ? $today_stats['active_users_today'] : ""; ?><?php else: ?>0<?php endif; ?></span>
                <span class="stat-text">今日活跃用户</span>
            </div>
            <div class="stat-compact">
                <span class="stat-number"><?php if(null !== ($logs_data ?? null) && is_array($logs_data) && array_key_exists('total', $logs_data)): ?><?php echo (isset($logs_data['total'])) ? $logs_data['total'] : ""; ?><?php else: ?>0<?php endif; ?></span>
                <span class="stat-text">日志总数</span>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <div><?php echo $message ?? ""; ?></div>
</div>
<?php endif; ?>

<?php if($error): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-circle"></i>
    <div><?php echo $error ?? ""; ?></div>
</div>
<?php endif; ?>

<!-- 筛选表单 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">
            筛选条件
            <span class="record-count">共 <?php if(null !== ($logs_data ?? null) && is_array($logs_data) && array_key_exists('total', $logs_data)): ?><?php echo (isset($logs_data['total'])) ? $logs_data['total'] : ""; ?><?php else: ?>0<?php endif; ?> 条记录</span>
        </h3>
        <div class="filter-form-expanded">
            <form method="GET" action="">
                <!-- 第一行：操作类型、目标类型 -->
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>操作类型:</label>
                        <select name="operation_type" class="form-control">
                            <option value="">全部</option>
                            <option value="create" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('operation_type', $filter_params) && $filter_params['operation_type'] == 'create'): ?>selected<?php endif; ?>>创建</option>
                            <option value="update" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('operation_type', $filter_params) && $filter_params['operation_type'] == 'update'): ?>selected<?php endif; ?>>修改</option>
                            <option value="delete" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('operation_type', $filter_params) && $filter_params['operation_type'] == 'delete'): ?>selected<?php endif; ?>>删除</option>
                            <option value="login" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('operation_type', $filter_params) && $filter_params['operation_type'] == 'login'): ?>selected<?php endif; ?>>登录</option>
                            <option value="logout" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('operation_type', $filter_params) && $filter_params['operation_type'] == 'logout'): ?>selected<?php endif; ?>>登出</option>
                        </select>
                    </div>

                    <div class="filter-item-expanded">
                        <label>目标类型:</label>
                        <select name="target_type" class="form-control">
                            <option value="">全部</option>
                            <option value="post" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('target_type', $filter_params) && $filter_params['target_type'] == 'post'): ?>selected<?php endif; ?>>信息</option>
                            <option value="news" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('target_type', $filter_params) && $filter_params['target_type'] == 'news'): ?>selected<?php endif; ?>>新闻</option>
                            <option value="category" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('target_type', $filter_params) && $filter_params['target_type'] == 'category'): ?>selected<?php endif; ?>>分类</option>
                            <option value="admin" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('target_type', $filter_params) && $filter_params['target_type'] == 'admin'): ?>selected<?php endif; ?>>管理员</option>
                            <option value="auth" <?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('target_type', $filter_params) && $filter_params['target_type'] == 'auth'): ?>selected<?php endif; ?>>认证</option>
                        </select>
                    </div>

                    <div class="filter-item-expanded">
                        <label>IP地址:</label>
                        <input type="text" name="ip_address" value="<?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('ip_address', $filter_params)): ?><?php echo (isset($filter_params['ip_address'])) ? $filter_params['ip_address'] : ""; ?><?php endif; ?>"
                               placeholder="IP地址" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <label>关键词:</label>
                        <input type="text" name="keyword" value="<?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('keyword', $filter_params)): ?><?php echo (isset($filter_params['keyword'])) ? $filter_params['keyword'] : ""; ?><?php endif; ?>"
                               placeholder="标题、描述、用户名" class="form-control">
                    </div>
                </div>

                <!-- 第二行：时间筛选 -->
                <div class="filter-row-expanded">
                    <div class="filter-item-expanded">
                        <label>开始日期:</label>
                        <input type="date" name="start_date" value="<?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('start_date', $filter_params)): ?><?php echo (isset($filter_params['start_date'])) ? $filter_params['start_date'] : ""; ?><?php endif; ?>" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <label>结束日期:</label>
                        <input type="date" name="end_date" value="<?php if(null !== ($filter_params ?? null) && is_array($filter_params) && array_key_exists('end_date', $filter_params)): ?><?php echo (isset($filter_params['end_date'])) ? $filter_params['end_date'] : ""; ?><?php endif; ?>" class="form-control">
                    </div>

                    <div class="filter-item-expanded">
                        <!-- 占位 -->
                    </div>
                </div>

                <!-- 第三行：操作按钮 -->
                <div class="filter-row-expanded">
                    <div class="filter-buttons-expanded">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            筛选
                        </button>
                        <a href="operation_logs.php" class="btn btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 日志列表 -->
<div class="section">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">日志列表</h3>
        </div>

        <!-- 批量操作表单 -->
        <form id="batchForm" method="POST" action="?op=batch_delete">
            <!-- 日志表格 -->
            <div class="table-responsive">
                <table class="table table-logs">
                    <thead>
                        <tr>
                            <th style="width: 40px;"><input type="checkbox" id="selectAllHeader"></th>
                            <th style="width: 60px;">ID</th>
                            <th style="width: 120px;">用户</th>
                            <th style="width: 80px;">操作</th>
                            <th style="width: 80px;">目标</th>
                            <th style="width: 70px;">目标ID</th>
                            <th style="min-width: 200px;">操作描述</th>
                            <th style="width: 160px;">IP地址</th>
                            <th style="width: 60px;">状态</th>
                            <th style="width: 90px;">时间</th>
                            <th style="width: 70px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if($logs_data['data'] && count($logs_data['data']) > 0): ?>
                            <?php if(null !== ($logs_data ?? null) && is_array($logs_data['data'])): foreach($logs_data['data'] as $log): ?>
                            <tr>
                                <td><input type="checkbox" name="ids[]" value="<?php echo (isset($log['id'])) ? $log['id'] : ""; ?>" class="log-checkbox"></td>
                                <td><?php echo (isset($log['id'])) ? $log['id'] : ""; ?></td>
                                <td title="用户ID: <?php echo (isset($log['user_id'])) ? $log['user_id'] : ""; ?>">
                                    <strong style="color: var(--text-color);"><?php echo (isset($log['username'])) ? $log['username'] : ""; ?></strong>
                                    <small style="color: var(--text-secondary); margin-left: 6px;">(<?php echo (isset($log['user_type'])) ? $log['user_type'] : ""; ?>)</small>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo (isset($log['operation_type'])) ? $log['operation_type'] : ""; ?>">
                                        <?php echo (isset($log['operation_type'])) ? $log['operation_type'] : ""; ?>
                                    </span>
                                </td>
                                <td><?php echo (isset($log['target_type'])) ? $log['target_type'] : ""; ?></td>
                                <td><?php if($log['target_id'] > 0): ?><?php echo (isset($log['target_id'])) ? $log['target_id'] : ""; ?><?php else: ?>-<?php endif; ?></td>
                                <td title="<?php echo (isset($log['operation_desc'])) ? $log['operation_desc'] : ""; ?>">
                                    <?php if($log['target_title']): ?>
                                        <strong style="color: var(--text-color);"><?php echo (isset($log['target_title'])) ? $log['target_title'] : ""; ?></strong>
                                        <span style="color: var(--text-secondary); margin-left: 8px;"><?php echo (isset($log['operation_desc_short'])) ? $log['operation_desc_short'] : ""; ?></span>
                                    <?php else: ?>
                                        <span style="color: var(--text-color);"><?php echo (isset($log['operation_desc_short'])) ? $log['operation_desc_short'] : ""; ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo (isset($log['ip_address'])) ? $log['ip_address'] : ""; ?><?php if($log['port'] > 0): ?>:<?php echo (isset($log['port'])) ? $log['port'] : ""; ?><?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-success">成功</span>
                                </td>
                                <td>
                                    <?php echo (isset($log['formatted_time'])) ? $log['formatted_time'] : ""; ?>
                                </td>
                                <td>
                                    <a href="?op=delete&id=<?php echo (isset($log['id'])) ? $log['id'] : ""; ?>"
                                       onclick="return confirm('确定要删除这条日志吗？')"
                                       class="btn btn-sm btn-light-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; endif; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">暂无日志记录</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- 批量操作按钮 -->
            <div class="batch-actions-bottom">
                <label class="batch-select-all">
                    <input type="checkbox" id="selectAll"> 全选
                </label>
                <div class="batch-buttons">
                    <button type="button" onclick="batchDelete()" class="btn btn-sm btn-light-danger">
                        <i class="fas fa-trash"></i>
                        <span>批量删除</span>
                    </button>
                    <button type="button" onclick="showCleanDialog()" class="btn btn-sm btn-warning">
                        <i class="fas fa-broom"></i>
                        <span>清理旧日志</span>
                    </button>
                </div>
            </div>
        </form>

        <!-- 分页 -->
        <?php if($logs_data['total_pages'] > 1): ?>
        <div class="pagination-wrapper">
            <?php echo $pagination_html ?? ""; ?>
            <div class="pagination-info">
                共 <?php echo (isset($logs_data['total'])) ? $logs_data['total'] : ""; ?> 条记录，每页 <?php echo (isset($logs_data['page_size'])) ? $logs_data['page_size'] : ""; ?> 条，共 <?php echo (isset($logs_data['total_pages'])) ? $logs_data['total_pages'] : ""; ?> 页，当前第 <?php echo (isset($logs_data['page'])) ? $logs_data['page'] : ""; ?> 页
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 清理对话框 -->
<div class="modal" id="cleanDialog" style="display:none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4>清理旧日志</h4>
            <button type="button" class="modal-close" onclick="hideCleanDialog()">&times;</button>
        </div>
        <form method="POST" action="?op=clean">
            <div class="modal-body">
                <div class="form-group">
                    <label>保留最近
                        <input type="number" name="days" value="90" min="7" max="365" class="form-control d-inline" style="width:80px;">
                        天的日志
                    </label>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>注意：此操作将永久删除超过指定天数的日志记录，无法恢复。</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="hideCleanDialog()" class="btn btn-outline">取消</button>
                <button type="submit" onclick="return confirm('确定要清理旧日志吗？此操作无法恢复！')" class="btn btn-danger">确定清理</button>
            </div>
        </form>
    </div>
</div>

<script>
// 全选功能
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.log-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

document.getElementById('selectAllHeader').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.log-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
    document.getElementById('selectAll').checked = this.checked;
});

// 批量删除
function batchDelete() {
    const checked = document.querySelectorAll('.log-checkbox:checked');
    if (checked.length === 0) {
        alert('请选择要删除的日志记录');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${checked.length} 条日志记录吗？`)) {
        document.getElementById('batchForm').submit();
    }
}

// 显示清理对话框
function showCleanDialog() {
    document.getElementById('cleanDialog').style.display = 'block';
}

// 隐藏清理对话框
function hideCleanDialog() {
    document.getElementById('cleanDialog').style.display = 'none';
}

// 点击对话框外部关闭
document.getElementById('cleanDialog').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCleanDialog();
    }
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
