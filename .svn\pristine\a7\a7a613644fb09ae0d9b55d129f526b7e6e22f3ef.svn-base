<?php
/**
 * 信息页面相关函数库
 * 包含category.php, index.php, view.php等页面使用的函数
 */

// 定义安全常量，禁止直接访问该文件
if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

// 引入图片处理相关函数
require_once(dirname(__FILE__) . '/image.fun.php');

/**
 * 处理区域选择逻辑
 * 
 * @param int $areaId 当前选择的区域ID
 * @param array $allRegions 所有区域数据
 * @return array 包含处理结果的数组 [showCities, currentProvince, isProvinceArea]
 */
function processAreaSelection($areaId, $allRegions) {
    // 参数验证：确保 $allRegions 是有效的数组
    if (!is_array($allRegions)) {
        $allRegions = array();
    }

    $showCities = false;  // 默认显示省份列表
    $currentProvince = null;  // 当前选中的省份
    $isProvinceArea = false;  // 当前选中的是否为省份

    if ($areaId > 0) {
        // 确定选中的是省份还是城市
        foreach ($allRegions as $province) {
            if ($province['id'] == $areaId) {
                $currentProvince = $province;
                $isProvinceArea = true;
                break;
            }

            foreach ($province['children'] as $city) {
                if ($city['id'] == $areaId) {
                    $currentProvince = $province;
                    $isProvinceArea = false;
                    break 2;
                }
            }
        }

        // 兼容单层区域（只有省份）和双层区域（省份+城市）
        // 有有效子区域就显示子区域，没有有效子区域就直接高亮省份
        if ($currentProvince !== null) {
            if ($isProvinceArea) {
                // 选中的是省份，检查是否有有效的子区域
                $hasValidChildren = false;
                if (!empty($currentProvince['children'])) {
                    // 过滤掉无效的子区域名称（如"江苏省市"这种格式）
                    $provinceName = $currentProvince['name'];
                    $provinceNameWithoutSuffix = str_replace(['省', '市', '自治区', '特别行政区'], '', $provinceName);

                    foreach ($currentProvince['children'] as $child) {
                        $childName = $child['name'];
                        // 排除"省名+市"格式的无效子区域
                        if ($childName !== $provinceNameWithoutSuffix . '市' &&
                            $childName !== $provinceName . '市' &&
                            $childName !== $provinceNameWithoutSuffix . '省市') {
                            $hasValidChildren = true;
                            break;
                        }
                    }
                }
                $showCities = $hasValidChildren;
            } else {
                // 选中的是城市，显示该省份的城市列表
                $showCities = true;
            }
        }
    }

    return array(
        'showCities' => $showCities,
        'currentProvince' => $currentProvince,
        'isProvinceArea' => $isProvinceArea
    );
}

/**
 * 构建区域导航数据
 * 
 * @param array $category 当前分类信息
 * @param bool $showCities 是否显示城市列表
 * @param array $currentProvince 当前选中的省份
 * @param int $areaId 当前选中的区域ID
 * @param array $allRegions 所有区域数据
 * @return array 区域导航数据数组
 */
function buildAreaNavigation($category, $showCities, $currentProvince, $areaId, $allRegions) {
    // 参数验证：确保 $allRegions 是有效的数组
    if (!is_array($allRegions)) {
        $allRegions = array();
    }

    // 静态缓存，避免同一请求中重复处理
    static $cachedResults = array();

    // 生成缓存键
    $cacheKey = md5(serialize($category) . '_' . $showCities . '_' . $areaId . '_' . (isset($currentProvince['id']) ? $currentProvince['id'] : 0));

    // 检查缓存
    if (isset($cachedResults[$cacheKey])) {
        return $cachedResults[$cacheKey];
    }

    $area_arr = array();
    
    if ($showCities && $currentProvince) {
        // 显示当前省份下的城市列表
        
        // 添加返回"全部"的链接
        $area_arr[] = array(
            'id' => 0,
            'areaid' => 0,
            'name' => '全部',
            'url' => buildCategoryUrl($category['pinyin']),
            'is_unlimited' => true,
            'active' => false
        );
        
        // 添加当前省份下的所有城市
        foreach ($currentProvince['children'] as $city) {
            $area_arr[] = array(
                'id' => $city['id'],
                'areaid' => $city['id'],
                'name' => $city['name'],
                'url' => buildCategoryUrl($category['pinyin'], 0, $city['id']),
                'active' => ($areaId == $city['id']),
                'is_city' => true,
                'parent_id' => $currentProvince['id']
            );
        }
    } else {
        // 显示所有省份列表
        
        // 添加"全部"选项
        $area_arr[] = array(
            'id' => 0,
            'areaid' => 0,
            'name' => '全部',
            'url' => buildCategoryUrl($category['pinyin']),
            'is_unlimited' => true,
            'active' => ($areaId == 0)
        );
        
        // 添加所有省份 - 预处理URL以减少重复计算
        $categoryUrl = $category['pinyin'];
        foreach ($allRegions as $province) {
            $area_arr[] = array(
                'id' => $province['id'],
                'areaid' => $province['id'],
                'name' => $province['name'],
                'url' => buildCategoryUrl($categoryUrl, 0, $province['id']),
                'active' => ($areaId == $province['id']),
                'is_province' => true
            );
        }
    }
    
    // 存入缓存
    $cachedResults[$cacheKey] = $area_arr;
    
    return $area_arr;
}

/**
 * 处理发布/编辑页面的区域数据
 * 为没有有效子区域的省份添加省份本身作为选项
 *
 * @param array $regions 原始区域数据
 * @return array 处理后的区域数据
 */
function processRegionsForPost($regions) {
    foreach ($regions as &$province) {
        if (!empty($province['children'])) {
            // 检查是否有有效的子区域（排除如"江苏省市"这种无效名称）
            $hasValidChildren = false;
            $provinceName = $province['name'];
            $provinceNameWithoutSuffix = str_replace(['省', '市', '自治区', '特别行政区'], '', $provinceName);

            foreach ($province['children'] as $child) {
                $childName = $child['name'];
                // 排除"省名+市"格式的无效子区域
                if ($childName !== $provinceNameWithoutSuffix . '市' &&
                    $childName !== $provinceName . '市' &&
                    $childName !== $provinceNameWithoutSuffix . '省市') {
                    $hasValidChildren = true;
                    break;
                }
            }

            // 如果没有有效的子区域，将省份本身作为选项
            if (!$hasValidChildren) {
                $province['children'] = array(
                    array(
                        'id' => $province['id'],
                        'parent_id' => $province['parent_id'],
                        'name' => $province['name'],
                        'level' => $province['level']
                    )
                );
            }
        } else {
            // 如果完全没有子区域，也将省份本身作为选项
            $province['children'] = array(
                array(
                    'id' => $province['id'],
                    'parent_id' => $province['parent_id'],
                    'name' => $province['name'],
                    'level' => $province['level']
                )
            );
        }
    }

    return $regions;
}

/**
 * 获取分类数据集合，用于首页分类展示
 */
function getCategoryData() {
    global $db;
    
    // 使用缓存的分类数据
    $allCategories = $GLOBALS['cached_categories'];
    if (empty($allCategories)) {
        // 如果缓存数据为空，尝试重新加载缓存
        $allCategories = getCachedCategories();
        $GLOBALS['cached_categories'] = $allCategories;
    }
    
    // 整理成树状结构
    $categories = array();
    $categoryMap = array();
    
    // 先将所有分类按ID索引
    foreach ($allCategories as $category) {
        $categoryMap[$category['id']] = $category;
    }
    
    // 找出所有顶级分类
    foreach ($allCategories as $category) {
        if ($category['parent_id'] == 0) {
            // 找出该分类下的所有子分类
            $subCategories = array();
            foreach ($allCategories as $subCat) {
                if ($subCat['parent_id'] == $category['id']) {
                    $subCategories[] = $subCat;
                }
            }
            
            $category['sub_categories'] = $subCategories;
            $categories[] = $category;
        }
    }
    
    return $categories;
}

/**
 * 根据分类名称获取合适的图标
 * 从index.php页面移过来
 */
function getCategoryIcon($categoryName) {
    // 分类名与图标的映射关系
    $iconMapping = [
        '房' => 'fa-home',
        '屋' => 'fa-home',
        '租' => 'fa-home',
        '职' => 'fa-briefcase',
        '聘' => 'fa-briefcase',
        '工作' => 'fa-briefcase',
        '车' => 'fa-car',
        '二手车' => 'fa-car',
        '教育' => 'fa-graduation-cap',
        '培训' => 'fa-graduation-cap',
        '服务' => 'fa-concierge-bell',
        '宠物' => 'fa-paw',
        '手机' => 'fa-mobile-alt',
        '数码' => 'fa-mobile-alt',
        '家居' => 'fa-couch',
        '家具' => 'fa-couch',
        '维修' => 'fa-tools',
        '食' => 'fa-utensils',
        '餐' => 'fa-utensils',
        '二手' => 'fa-shopping-bag',
        '旅游' => 'fa-plane',
        '休闲' => 'fa-film',
        '娱乐' => 'fa-film',
        '母婴' => 'fa-baby',
        '电器' => 'fa-tv',
        '保洁' => 'fa-broom',
        '美妆' => 'fa-spa'
    ];
    
    // 根据分类名寻找匹配的图标
    foreach ($iconMapping as $keyword => $icon) {
        if (mb_strpos($categoryName, $keyword) !== false) {
            return $icon;
        }
    }
    
    // 默认图标
    return 'fa-tag';
}

/**
 * 获取首页置顶信息
 * 从index.php页面移过来
 * 
 * @param int $limit 要获取的记录数量，默认为10
 * @param int $categoryId 分类ID，默认为0（不限分类）
 * @return array 置顶信息数组
 */
function getTopPosts($limit = 10, $categoryId = 0) {
    global $db;
    
    // 获取当前时间戳
    $now = time();
    
    // 限制为整数并确保大于0
    $limit = max(1, (int)$limit);
    $categoryId = (int)$categoryId;
    
    // 构建分类筛选条件
    $category_condition = '';
    if ($categoryId > 0) {
        $category_condition = " AND p.category_id = $categoryId";
    }
    
    // 参数化查询，防止SQL注入
    $sql = "SELECT p.id, p.title, pc.content, p.category_id, p.region_id, p.created_at, p.updated_at, p.is_top_home,
            c.name as category_name, c.pinyin as category_pinyin, r.name as region_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN area r ON p.region_id = r.id
            LEFT JOIN post_contents pc ON p.id = pc.post_id
            WHERE p.status = 1 AND p.is_top_home = 1
            AND (p.top_home_expire = 0 OR p.top_home_expire > ?)
            $category_condition
            ORDER BY p.updated_at DESC LIMIT " . $limit;
    $result = $db->query($sql, [$now]);
    
    $topPosts = array();
    while ($row = $db->fetch_array($result)) {
        // 处理内容，截取摘要
        $row['summary'] = mb_substr(strip_tags($row['content']), 0, 100) . '...';
        
        // 使用全局函数获取图片URL
        $row['image_url'] = getPostImage($row['id']);
        
        $topPosts[] = $row;
    }
    
    return $topPosts;
}

/**
 * 获取首页普通信息（非置顶）
 * 从index.php页面移过来
 * 
 * @param int $limit 要获取的记录数量，默认为20
 * @param int $categoryId 分类ID，默认为0（不限分类）
 * @return array 普通信息数组
 */
function getNormalPosts($limit = 20, $categoryId = 0) {
    global $db;
    
    // 获取当前时间戳
    $now = time();
    
    // 限制为整数并确保大于0
    $limit = max(1, (int)$limit);
    $categoryId = (int)$categoryId;
    
    // 构建分类筛选条件
    $category_condition = '';
    if ($categoryId > 0) {
        $category_condition = " AND p.category_id = $categoryId";
    }
    
    // 参数化查询，防止SQL注入
    $sql = "SELECT p.id, p.title, pc.content as description, p.category_id, p.region_id,
            p.created_at as add_time, p.updated_at,
            c.name as category_name, c.pinyin as category_pinyin, r.name as region_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN area r ON p.region_id = r.id
            LEFT JOIN post_contents pc ON p.id = pc.post_id
            WHERE p.status = 1 AND (p.is_top_home = 0 OR p.is_top_home IS NULL
            OR (p.is_top_home = 1 AND p.top_home_expire > 0 AND p.top_home_expire < ?))
            $category_condition
            ORDER BY p.updated_at DESC LIMIT " . $limit;
    $result = $db->query($sql, [$now]);
    
    $normalPosts = array();
    while ($row = $db->fetch_array($result)) {
        // 处理内容，截取摘要
        if (mb_strlen($row['description']) > 100) {
            $row['description'] = mb_substr(strip_tags($row['description']), 0, 100) . '...';
        }
        
        $normalPosts[] = $row;
    }
    
    return $normalPosts;
}

/**
 * 显示404页面
 * 从view.php页面移过来
 */
function include_404_page() {
    global $site_name;
    assign('site_name', $site_name);
    display('404.htm');
}

/**
 * 获取分类综合数据
 */
function getCategoryIntegratedData($categoryId, $page = 1, $perPage = 20, $subCatId = 0, $areaId = 0, $isProvinceArea = false) {
    // 获取分类信息
    $category = getCategoryInfo($categoryId);
    if (!$category) {
        return false;
    }
    
    // 获取子分类列表（如果是一级分类）
    $subCategories = array();
    if ($category['parent_id'] == 0) {
        // 使用缓存获取子分类
        foreach ($GLOBALS['cached_categories'] as $cat) {
            if ($cat['parent_id'] == $categoryId) {
                $subCategories[] = $cat;
            }
        }
    }
    
    // 获取帖子
    $posts = getCategoryPostsDirectly($categoryId, $page, $perPage, $subCatId, $areaId, $isProvinceArea);
    
    // 获取总帖子数
    $totalPosts = getCategoryPostsCount($categoryId, $subCatId, $areaId, $isProvinceArea);
    
    // 返回综合数据
    return array(
        'info' => $category,
        'subCategories' => $subCategories,
        'posts' => $posts,
        'totalPosts' => $totalPosts
    );
}

/**
 * 获取子分类ID列表
 * @param int $parentId 父分类ID
 * @return array 子分类ID数组
 */
function getSubcategoryIds($parentId) {
    global $db;
    
    // 尝试从subcategory_ids字段获取数据
    $sql = "SELECT subcategory_ids FROM categories WHERE id = ?";
    $result = $db->query($sql, [$parentId]);
    if ($result && $row = $db->fetch_array($result)) {
        if (!empty($row['subcategory_ids'])) {
            // 将逗号分隔的子栏目ID转换为数组并返回
            return array_map('intval', explode(',', $row['subcategory_ids']));
        }
    }
    
    // 如果上述方法失败，回退到传统方法
    // 使用全局变量获取子分类
    $subCategoryIds = array();
    foreach ($GLOBALS['cached_categories'] as $cat) {
        if ($cat['parent_id'] == $parentId) {
            $subCategoryIds[] = $cat['id'];
        }
    }
    
    return $subCategoryIds;
}

/**
 * 获取分类信息数据（优化版）- 保持原始数据结构但不使用置顶逻辑
 */
function getCategoryPostsDirectly($catId, $page, $perPage, $subCatId = 0, $areaId = 0, $isProvinceArea = false) {
    global $db, $cache, $config;

    // 检查全局缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_data_time = isset($config['cache_data']) ? intval($config['cache_data']) : 300;

    // 构建缓存键 - 添加版本号以强制更新缓存
    $cacheKey = "category_posts_v2_{$catId}_{$page}_{$perPage}_{$subCatId}_{$areaId}_" . ($isProvinceArea ? '1' : '0');

    // 只有在缓存开启且缓存时间大于0时才尝试读取缓存
    $cached = false;
    if ($cache_enable && $cache_data_time > 0) {
        $cached = cache_get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 获取当前分类及其所有子分类ID
    $categoryIds = array($catId);
    
    // 如果筛选了子分类，就只使用该子分类ID
    if ($subCatId > 0) {
        $categoryIds = array($subCatId);
    } else {
        // 使用优化的方法直接获取子分类ID，避免遍历所有分类
        $subIds = getSubcategoryIds($catId);
        if (!empty($subIds)) {
            $categoryIds = array_merge($categoryIds, $subIds);
        }
    }
    $categoryIdsStr = implode(',', $categoryIds);
    
    // 构建区域筛选SQL条件
    $area_sql = '';
    if ($areaId > 0) {
        if ($isProvinceArea) {
            // 使用缓存获取省份下城市ID，避免数据库查询
            static $provinceChildrenIds = array();
            if (!isset($provinceChildrenIds[$areaId])) {
                $cityIds = array();
                $cachedRegions = getCachedRegions();
                foreach ($cachedRegions as $province) {
                    if ($province['id'] == $areaId) {
                        foreach ($province['children'] as $city) {
                            $cityIds[] = $city['id'];
                        }
                        break;
                    }
                }
                $provinceChildrenIds[$areaId] = $cityIds;
            }

            if (empty($provinceChildrenIds[$areaId])) {
                // 如果省份没有子城市，直接使用省份ID筛选
                $area_sql = " AND p.region_id = $areaId";
            } else {
                // 如果省份有子城市，同时查找省份ID和子城市ID
                // 因为有些信息可能直接选择了省份，有些选择了具体城市
                $allIds = array_merge([$areaId], $provinceChildrenIds[$areaId]);
                $allIdsStr = implode(',', $allIds);
                $area_sql = " AND p.region_id IN ($allIdsStr)";
            }
        } else {
            // 如果选择了城市，只筛选该城市内容
            $area_sql = " AND p.region_id = $areaId";
        }
    }
    
    // 计算分页偏移
    $offset = ($page - 1) * $perPage;
    
    // 优化SQL查询 - 使用CASE表达式处理置顶排序，并JOIN area表获取区域名称
    // 添加置顶过期时间检查，确保只有未过期的置顶信息才会被置顶显示
    $current_time = time();
    $sql = "SELECT p.*, r.name as region_name
            FROM posts p
            LEFT JOIN area r ON p.region_id = r.id
            WHERE p.category_id IN ($categoryIdsStr) AND p.status = 1 AND p.is_expired = 0
            $area_sql
            ORDER BY (CASE
                WHEN p.is_top_category = 1 AND (p.top_category_expire = 0 OR p.top_category_expire > $current_time) THEN 3
                WHEN p.is_top_subcategory = 1 AND (p.top_subcategory_expire = 0 OR p.top_subcategory_expire > $current_time) THEN 2
                ELSE 1
            END) DESC, p.updated_at DESC
            LIMIT $offset, $perPage";
    
    $result = $db->query($sql);
    
    // 获取所有帖子ID，用于批量获取内容摘要
    $postIds = array();
    $posts = array();
    
    // 收集所有帖子的基本信息
    while ($row = $db->fetch_array($result)) {
        $postIds[] = $row['id'];

        // 添加默认摘要
        $row['summary'] = '查看详情...';

        // 设置默认图片占位符
        $row['image_url'] = '/uploads/images/default.jpg';

        // 确保region_name字段存在，如果为空则设置默认值
        if (empty($row['region_name'])) {
            $row['region_name'] = '未知地区';
        }
        
        // 为每条信息生成详情页URL
        // 先获取分类拼音和名称
        $category_pinyin = '';
        $category_name = '';
        foreach ($GLOBALS['cached_categories'] as $cat) {
            if ($cat['id'] == $row['category_id']) {
                $category_pinyin = $cat['pinyin'];
                $category_name = $cat['name'];
                break;
            }
        }
        $row['category_pinyin'] = $category_pinyin;
        $row['category_name'] = $category_name;
        $row['url'] = buildCategoryUrl($category_pinyin, $row['id']);
        $row['detail_url'] = $row['url']; 
        
        $posts[$row['id']] = $row;
    }
    
    // 如果有帖子数据，批量获取内容摘要和图片
    if (!empty($postIds)) {
        $postIdsStr = implode(',', $postIds);
        
        // 优化内容摘要查询 - 使用索引且只获取必要字段
        $contentSql = "SELECT post_id, LEFT(content, 100) AS summary 
                       FROM post_contents 
                       WHERE post_id IN ($postIdsStr)";
        $contentResult = $db->query($contentSql);
        
        while ($contentRow = $db->fetch_array($contentResult)) {
            if (isset($posts[$contentRow['post_id']]) && !empty($contentRow['summary'])) {
                $posts[$contentRow['post_id']]['summary'] = strip_tags($contentRow['summary']) . '...';
            }
        }
        
        // 优化图片查询 - 使用子查询找出每个帖子的第一张图片
        $imageSql = "SELECT a.post_id, a.thumb_path 
                     FROM attachments a
                     INNER JOIN (
                         SELECT post_id, MIN(id) AS min_id 
                         FROM attachments 
                         WHERE post_id IN ($postIdsStr) 
                         GROUP BY post_id
                     ) b ON a.id = b.min_id";
        
        $imageResult = $db->query($imageSql);
        
        while ($imageRow = $db->fetch_array($imageResult)) {
            if (isset($posts[$imageRow['post_id']])) {
                $imagePath = $imageRow['thumb_path'];
                if ($imagePath && substr($imagePath, 0, 1) !== '/') {
                    $imagePath = '/' . $imagePath;
                }
                $posts[$imageRow['post_id']]['image_url'] = $imagePath;
            }
        }
    }

    // 转换回索引数组
    $result = array_values($posts);

    // 只有在缓存开启且缓存时间大于0时才设置缓存
    if ($cache_enable && $cache_data_time > 0) {
        cache_set($cacheKey, $result, $cache_data_time);
    }

    return $result;
}

/**
 * 获取分类信息总数（优化版）
 */
function getCategoryPostsCount($catId, $subCatId = 0, $areaId = 0, $isProvinceArea = false) {
    global $db;
    
    // 获取当前分类及其所有子分类ID
    $categoryIds = array($catId);
    
    // 如果筛选了子分类，就只使用该子分类ID
    if ($subCatId > 0) {
        $categoryIds = array($subCatId);
    } else {
        // 使用subcategory_ids字段快速获取所有子分类ID
        $sql = "SELECT subcategory_ids FROM categories WHERE id = ?";
        $result = $db->query($sql, [$catId]);
        if ($result && $row = $db->fetch_array($result) && !empty($row['subcategory_ids'])) {
            // 将逗号分隔的字符串转为数组并与当前分类ID合并
            $subcategoryIds = explode(',', $row['subcategory_ids']);
            // 确保都是整数
            $subcategoryIds = array_map('intval', $subcategoryIds);
            // 合并数组
            $categoryIds = array_merge($categoryIds, $subcategoryIds);
        } else {
            // 回退方案：使用传统方法获取子分类ID
            $subIds = getSubcategoryIds($catId);
            if (!empty($subIds)) {
                $categoryIds = array_merge($categoryIds, $subIds);
            }
        }
    }
    
    $categoryIdsStr = implode(',', $categoryIds);
    
    // 构建区域筛选SQL条件
    $area_sql = '';
    if ($areaId > 0) {
        if ($isProvinceArea) {
            // 性能优化：避免子查询，先获取所有城市ID再构建IN条件
            static $provinceChildrenIds = array();
            if (!isset($provinceChildrenIds[$areaId])) {
                $cityIds = array();
                $result = $db->query("SELECT id FROM area WHERE parent_id = ?", [$areaId]);
                while ($row = $db->fetch_array($result)) {
                    $cityIds[] = $row['id'];
                }
                $provinceChildrenIds[$areaId] = $cityIds;
            }

            if (empty($provinceChildrenIds[$areaId])) {
                // 如果省份没有子城市，直接使用省份ID筛选
                $area_sql = " AND region_id = $areaId";
            } else {
                // 如果省份有子城市，同时查找省份ID和子城市ID
                // 因为有些信息可能直接选择了省份，有些选择了具体城市
                $allIds = array_merge([$areaId], $provinceChildrenIds[$areaId]);
                $allIdsStr = implode(',', $allIds);
                $area_sql = " AND region_id IN ($allIdsStr)";
            }
        } else {
            $area_sql = " AND region_id = $areaId";
        }
    }
    
    // 使用COUNT(*)优化查询性能
    $sql = "SELECT COUNT(*) AS total FROM posts 
            WHERE category_id IN ($categoryIdsStr) AND status = 1 $area_sql";
    
    $result = $db->query($sql);
    $row = $db->fetch_array($result);
    $total = $row ? intval($row['total']) : 0;
    
    return $total;
}

/**
 * 获取相关信息
 * @param int $categoryId 分类ID
 * @param int $currentId 当前信息ID
 * @param int $limit 条数限制
 * @return array 相关信息列表
 */
function getRelatedPosts($categoryId, $currentId, $limit = 10) {
    global $db;
    
    $sql = "SELECT id, title, created_at, updated_at
            FROM posts 
            WHERE category_id = ? AND id != ? AND status = 1 
            ORDER BY updated_at DESC 
            LIMIT ?";
    $result = $db->query($sql, [$categoryId, $currentId, $limit]);
    
    $posts = array();
    while ($result && $row = $db->fetch_array($result)) {
        $posts[] = $row;
    }
    
    return $posts;
}

/**
 * 更新浏览次数
 * @param int $id 信息ID
 */
function updateViewCount($id) {
    global $db;

    // 使用无锁更新技术，避免高并发下的锁竞争
    $sql = "UPDATE posts SET view_count = view_count + 1 WHERE id = ?";
    $db->query($sql, [$id]);
}

/**
 * 异步更新浏览次数（性能优化版）
 * @param int $id 信息ID
 */
function updateViewCountAsync($id) {
    global $db;

    // 使用队列或延迟更新机制，避免阻塞页面加载
    // 这里使用简单的无锁更新，实际生产环境可以考虑使用Redis队列
    try {
        $sql = "UPDATE posts SET view_count = view_count + 1 WHERE id = ?";
        $db->query($sql, [$id]);
    } catch (Exception $e) {
        // 静默处理错误，不影响页面显示
        error_log("更新浏览次数失败: " . $e->getMessage());
    }
}

/**
 * 获取当前浏览次数（实时查询）
 * @param int $id 信息ID
 * @return int 浏览次数
 */
function getCurrentViewCount($id) {
    global $db;

    $sql = "SELECT view_count FROM posts WHERE id = ?";
    $result = $db->query($sql, [$id]);
    if ($result && $row = $db->fetch_array($result)) {
        return intval($row['view_count']);
    }

    return 0;
}

/**
 * 获取热门信息（按浏览次数排序，未过期）
 * @param int $limit 要获取的记录数量，默认为10
 * @param int $categoryId 分类ID，默认为0（不限分类）
 * @return array 热门信息数组
 */
function getHotPosts($limit = 10, $categoryId = 0) {
    global $db;

    // 获取当前时间戳
    $now = time();

    // 限制为整数并确保大于0
    $limit = max(1, (int)$limit);
    $categoryId = (int)$categoryId;

    // 构建分类筛选条件
    $category_condition = '';
    if ($categoryId > 0) {
        $category_condition = " AND p.category_id = $categoryId";
    }

    // 参数化查询，防止SQL注入
    $sql = "SELECT p.id, p.title, p.view_count, p.category_id, p.region_id,
            p.created_at, p.updated_at, p.expired_at,
            c.name as category_name, c.pinyin as category_pinyin, r.name as region_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN area r ON p.region_id = r.id
            WHERE p.status = 1 AND p.expired_at > ?
            $category_condition
            ORDER BY p.view_count DESC, p.updated_at DESC LIMIT " . $limit;
    $result = $db->query($sql, [$now]);

    $hotPosts = array();
    while ($row = $db->fetch_array($result)) {
        // 格式化时间显示
        $row['add_time'] = $row['created_at'];

        // 使用全局函数获取图片URL
        $row['image_url'] = getPostImage($row['id']);

        $hotPosts[] = $row;
    }

    return $hotPosts;
}

/**
 * 获取最新新闻（用于首页资讯索引）
 * @param int $limit 要获取的记录数量，默认为10
 * @return array 最新新闻数组
 */
function getLatestNews($limit = 10) {
    global $db;

    // 限制为整数并确保大于0
    $limit = max(1, (int)$limit);

    // 查询最新新闻
    $sql = "SELECT id, title, addtime, click, description
            FROM news
            WHERE is_show = 1
            ORDER BY addtime DESC, id DESC
            LIMIT " . $limit;
    $result = $db->query($sql);

    $latestNews = array();
    while ($row = $db->fetch_array($result)) {
        // 格式化时间显示
        $row['formatted_time'] = date('Y-m-d', $row['addtime']);

        // 确保描述字段存在
        $row['description'] = $row['description'] ?? '';

        $latestNews[] = $row;
    }

    return $latestNews;
}

/**
 * 获取缓存的信息图片
 * @param int $postId 信息ID
 * @return array 图片数组
 */
function getCachedPostImages($postId) {
    global $config;

    // 检查缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_time = isset($config['cache_data']) ? intval($config['cache_data']) : 3600;

    $cache_key = "post_images_{$postId}";

    // 尝试从缓存获取
    if ($cache_enable && $cache_time > 0) {
        $cached = cache_get($cache_key);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 缓存不存在，从数据库获取
    $images = getPostImage($postId, true);

    // 设置缓存
    if ($cache_enable && $cache_time > 0) {
        cache_set($cache_key, $images, $cache_time);
    }

    return $images;
}

/**
 * 获取缓存的相关信息
 * @param int $categoryId 分类ID
 * @param int $currentId 当前信息ID
 * @param int $limit 条数限制
 * @return array 相关信息列表
 */
function getCachedRelatedPosts($categoryId, $currentId, $limit = 10) {
    global $config;

    // 检查缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_time = isset($config['cache_data']) ? intval($config['cache_data']) : 1800;

    $cache_key = "related_posts_{$categoryId}_{$currentId}_{$limit}";

    // 尝试从缓存获取
    if ($cache_enable && $cache_time > 0) {
        $cached = cache_get($cache_key);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 缓存不存在，从数据库获取
    $relatedPosts = getRelatedPosts($categoryId, $currentId, $limit);

    // 为相关信息添加详情链接
    foreach ($relatedPosts as &$relatedPost) {
        // 获取分类拼音
        $categoryInfo = getCategoryInfo($categoryId);
        if ($categoryInfo && !empty($categoryInfo['pinyin'])) {
            $relatedPost['detail_url'] = buildCategoryUrl($categoryInfo['pinyin'], $relatedPost['id']);
        } else {
            $relatedPost['detail_url'] = "/view.php?id=" . $relatedPost['id'];
        }
    }

    // 设置缓存
    if ($cache_enable && $cache_time > 0) {
        cache_set($cache_key, $relatedPosts, $cache_time);
    }

    return $relatedPosts;
}

/**
 * 获取帖子当前浏览次数
 * @param int $id 信息ID
 * @return int|false 浏览次数或失败时返回false
 */
function getPostViewCount($id) {
    global $db;
    
    $sql = "SELECT view_count FROM posts WHERE id = ? AND status = 1";
    $result = $db->query($sql, [$id]);
    if ($result) {
        $row = $db->fetch_array($result);
        if ($row) {
            return $row['view_count'];
        }
    }
    
    return false;
}

/**
 * 构建分类URL（伪静态或动态URL）
 * 
 * @param string $categoryPinyin 分类拼音
 * @param int $postId 信息ID（可选）
 * @param int $areaId 区域ID（可选）
 * @param int $page 页码（可选）
 * @param bool $isStatic 是否使用伪静态（目录式）URL，默认为true
 * @return string 生成的URL
 */
function buildCategoryUrl($categoryPinyin, $postId = 0, $areaId = 0, $page = 1, $isStatic = true) {
    global $config;
    
    // 如果在配置中有全局伪静态设置，优先使用
    if (isset($config['use_static_url'])) {
        $isStatic = (bool)$config['use_static_url'];
    }
    
    $baseUrl = getSiteUrl(); // 获取网站根URL
    
    // 信息详情页
    if ($postId > 0) {
        if ($isStatic) {
            // 伪静态: 域名/栏目拼音/信息ID.html
            return "{$baseUrl}/{$categoryPinyin}/{$postId}.html";
        } else {
            // 动态: 域名/detail.php?id=信息ID&cat=栏目拼音
            return "{$baseUrl}/detail.php?id={$postId}&cat={$categoryPinyin}";
        }
    }
    
    // 分类列表页（含区域和分页）
    if ($isStatic) {
        // 基础URL：域名/栏目拼音/
        $url = "{$baseUrl}/{$categoryPinyin}/";
        
        if ($areaId > 0 && $page > 1) {
            // 含区域和分页: 域名/栏目拼音/a区域IDp页码/
            $url .= "a{$areaId}p{$page}/";
        } elseif ($areaId > 0) {
            // 仅含区域: 域名/栏目拼音/a区域ID/
            $url .= "a{$areaId}/";
        } elseif ($page > 1) {
            // 仅含分页: 域名/栏目拼音/p页码/
            $url .= "p{$page}/";
        }
        
        return $url;
    } else {
        // 动态URL: 域名/category.php?pinyin=栏目拼音&area=区域ID&page=页码
        $url = "{$baseUrl}/category.php?pinyin={$categoryPinyin}";
        
        if ($areaId > 0) {
            $url .= "&area={$areaId}";
        }
        
        if ($page > 1) {
            $url .= "&page={$page}";
        }
        
        return $url;
    }
}

/**
 * 计算剩余天数（数字值）
 * 过期返回0，未过期返回剩余天数
 * 
 * @param int $expireTimestamp 过期时间戳
 * @return int 剩余天数，过期或无效返回0
 */
function getRemainingDaysInt($expireTimestamp) {
    if (empty($expireTimestamp) || $expireTimestamp <= 0) {
        return 0; // 无效时间戳返回0
    }
    
    $now = time();
    
    // 已过期
    if ($expireTimestamp <= $now) {
        return 0;
    }
    
    // 计算剩余秒数并转换为天数
    $remainingSeconds = $expireTimestamp - $now;
    $remainingDays = ceil($remainingSeconds / 86400); // 向上取整，不足1天显示1天
    
    return $remainingDays;
}

/**
 * 生成友好的剩余天数表示
 * @param int|string $days 剩余天数
 * @return string 友好的剩余天数表示
 */
function friendlyRemainingDays($days) {
    if (is_string($days)) {
        return $days; // 如果已经是字符串（如"已过期"、"永不过期"），直接返回
    }
    
    if ($days <= 0) {
        return '已过期';
    } elseif ($days == 1) {
        return '最后1天';
    } elseif ($days <= 7) {
        return '剩余' . $days . '天';
    } elseif ($days <= 30) {
        return '剩余' . floor($days / 7) . '周';
    } elseif ($days <= 365) {
        return '剩余' . floor($days / 30) . '个月';
    } else {
        return '剩余' . floor($days / 365) . '年';
    }
}

/**
 * 格式化相对时间（一天内显示小时，超过一天显示日期）
 * 
 * @param int|string $time 时间戳或日期字符串
 * @return string 格式化后的时间
 */
function formatRelativeTime($time) {
    // 兼容字符串时间格式
    $timestamp = is_numeric($time) ? $time : strtotime($time);
    $current = time();
    
    // 判断是今天、昨天还是更早
    $today = strtotime(date('Y-m-d', $current));
    $yesterday = $today - 86400; // 昨天开始时间
    $before_yesterday = $yesterday - 86400; // 前天开始时间
    
    $current_year = date('Y', $current);
    $time_year = date('Y', $timestamp);
    
    // 判断时间范围，返回不同格式
    if ($timestamp >= $today) {
        // 今天，显示小时分
        return '今天' . date('H:i', $timestamp);
    } elseif ($timestamp >= $yesterday) {
        // 昨天，显示"昨天 小时分"
        return '昨天' . date('H:i', $timestamp);
    } elseif ($timestamp >= $before_yesterday) {
        // 前天，显示"前天 小时分"
        return '前天' . date('H:i', $timestamp);
    } elseif ($current_year == $time_year) {
        // 今年，不显示年份
        return date('m-d', $timestamp);
    } else {
        // 往年，显示完整日期
        return date('Y-m-d', $timestamp);
    }
}

/**
 * 获取信息详情
 * @param int $id 信息ID
 * @param bool $checkStatus 是否检查状态（默认为true，用于前台显示）
 * @return array|false 信息详情或false
 */
function getPostDetail($id, $checkStatus = true) {
    global $db;
    
    // 构建SQL语句 - 联表查询获取所有数据，包括父分类信息
    $sql = "SELECT p.*, c.name as category_name, c.pinyin as category_pinyin,
            pc_parent.name as parent_category_name, pc_parent.pinyin as parent_category_pinyin,
            c.parent_id as parent_category_id, r.name as region_name,
            pc.content, pc.fields_data, pc.ip, pc.password,
            pc.contact_name, pc.contact_mobile, pc.contact_weixin, pc.contact_address
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN categories pc_parent ON c.parent_id = pc_parent.id
            LEFT JOIN area r ON p.region_id = r.id
            LEFT JOIN post_contents pc ON p.id = pc.post_id
            WHERE p.id = ?";
    
    // 仅在需要时检查状态
    if ($checkStatus) {
        $sql .= " AND p.status = 1";
    }
    
    $result = $db->query($sql, [$id]);
    
    if ($result && $row = $db->fetch_array($result)) {
        $post = $row;
        
        // 如果post_contents中没有内容，确保content字段有默认值
        if (!isset($post['content']) || $post['content'] === null) {
            $post['content'] = '';
        }
        
        // 处理自定义字段数据
        if (!empty($post['fields_data'])) {
            $post['fields'] = json_decode($post['fields_data'], true);
        } else {
            $post['fields'] = array();
        }
        
        // 如果有父分类，处理父分类信息
        if ($post['category_id'] > 0) {
            $parent_id = getCategoryParentId($post['category_id']);
            if ($parent_id > 0) {
                $post['parent_category_id'] = $parent_id;
                $parent_category = getCategoryInfo($parent_id);
                if ($parent_category) {
                    $post['parent_category_name'] = $parent_category['name'];
                    $post['parent_category_pinyin'] = $parent_category['pinyin'];
                }
            }
        }
        
        // 确保parent_category_id存在，避免notice
        if (!isset($post['parent_category_id'])) {
            $post['parent_category_id'] = 0;
            $post['parent_category_name'] = '';
            $post['parent_category_pinyin'] = '';
        }
        
        // 确保mobile字段存在，用于移动端模板
        if (isset($post['contact_mobile']) && !isset($post['mobile'])) {
            $post['mobile'] = $post['contact_mobile'];
        }
        
        // 确保wechat字段存在，用于移动端模板
        if (isset($post['contact_weixin']) && !isset($post['wechat'])) {
            $post['wechat'] = $post['contact_weixin'];
        }
        
        // 确保expire_days字段存在
        if (!isset($post['expire_days']) || empty($post['expire_days'])) {
            $post['expire_days'] = 30; // 默认30天
        }
        
        return $post;
    }
    
    return false;
}

/**
 * 获取分类信息
 * @param int $categoryId 分类ID
 * @return array|false 分类信息或false
 */
function getCategoryInfo($categoryId) {
    // 使用全局缓存变量
    if (isset($GLOBALS['cached_categories'])) {
        foreach ($GLOBALS['cached_categories'] as $category) {
            if ($category['id'] == $categoryId) {
                return $category;
            }
        }
    }
    
    // 回退到数据库查询
    global $db;
    $sql = "SELECT * FROM categories WHERE id = ?";
    $result = $db->query($sql, [$categoryId]);
    if ($result && $row = $db->fetch_array($result)) {
        return $row;
    }
    
    return false;
}

/**
 * 获取子分类列表
 * @param int $parentId 父分类ID
 * @return array 子分类列表
 */
function getSubCategories($parentId) {
    // 从全局缓存中获取
    $subCategories = array();
    foreach ($GLOBALS['cached_categories'] as $cat) {
        if ($cat['parent_id'] == $parentId) {
            $subCategories[] = $cat;
        }
    }
    
    // 如果缓存为空，回退到数据库查询
    if (empty($subCategories) && !empty($parentId)) {
        global $db;
        
        // 查询指定父分类下的所有子分类
        $sql = "SELECT id, parent_id, name, icon, sort_order, status, pinyin, subcategory_ids FROM categories WHERE parent_id = ? AND status = 1 ORDER BY sort_order DESC";
        $result = $db->query($sql, [$parentId]);
        
        $subCategories = array();
        while ($row = $db->fetch_array($result)) {
            $subCategories[] = $row;
        }
    }
    
    return $subCategories;
}

/**
 * 根据拼音获取分类ID
 */
function getCategoryIdByPinyin($pinyin) {
    // 从缓存中查找
    foreach ($GLOBALS['cached_categories'] as $category) {
        if ($category['pinyin'] == $pinyin) {
            return $category['id'];
        }
    }
    
    // 如果缓存中没有找到，返回0而不是查询数据库
    // 因为cached_categories应该包含所有分类
    return 0;
}

/**
 * 构建分页HTML
 * @param int $currentPage 当前页码
 * @param int $totalPages 总页数
 * @param string $urlPattern URL模板，使用{page}作为页码占位符
 * @param int $showPages 显示的页码数量（当前页前后各显示多少页）
 * @param callable $urlCallback 可选的URL生成回调函数，如果提供则优先使用
 * @return string 分页HTML代码
 */
function buildPagination($currentPage, $totalPages, $urlPattern, $showPages = 5, $urlCallback = null) {
    if ($totalPages <= 1) {
        return '';
    }
    
    // 确保当前页码在有效范围内
    $currentPage = max(1, min($currentPage, $totalPages));
    
    // 添加内联CSS样式
    $html = '<style>
        .pagination .dots {
            margin: 0 5px;
            color: #999;
        }
    </style>';
    
    $html .= '<div class="pagination">';
    
    // 上一页
    if ($currentPage > 1) {
        if ($urlCallback && is_callable($urlCallback)) {
            $prevUrl = $urlCallback($currentPage - 1);
        } else {
            $prevUrl = str_replace('{page}', $currentPage - 1, $urlPattern);
        }
        $html .= '<a href="' . $prevUrl . '">上一页</a>';
    }
    
    // 始终显示第一页
    if ($currentPage > 1) {
        if ($urlCallback && is_callable($urlCallback)) {
            $firstUrl = $urlCallback(1);
        } else {
            $firstUrl = str_replace('{page}', 1, $urlPattern);
        }
        $html .= '<a href="' . $firstUrl . '">1</a>';
    } else {
        $html .= '<span class="current">1</span>';
    }
    
    // 计算显示页码的范围
    $halfShow = intval($showPages / 2);
    $startPage = max(2, $currentPage - $halfShow); // 从2开始，因为1已经单独显示
    $endPage = min($totalPages - 1, $currentPage + $halfShow); // 到倒数第2页结束，因为最后一页单独显示
    
    // 特殊处理：当当前页接近尾页时，确保显示足够的页码
    if ($currentPage > $totalPages - $halfShow - 1) {
        $startPage = max(2, $totalPages - $showPages);
        $endPage = $totalPages - 1;
    }
    
    // 特殊处理：当当前页接近首页时，确保显示足够的页码
    if ($currentPage < $halfShow + 2) {
        $startPage = 2;
        $endPage = min($totalPages - 1, $startPage + $showPages - 1);
    }
    
    // 调整以确保显示足够的页码
    if ($endPage - $startPage + 1 < min($showPages, $totalPages - 2) && $totalPages > $showPages + 2) {
        if ($startPage == 2) {
            $endPage = min($totalPages - 1, $startPage + min($showPages, $totalPages - 2) - 1);
        } else if ($endPage == $totalPages - 1) {
            $startPage = max(2, $endPage - min($showPages, $totalPages - 2) + 1);
        }
    }
    
    // 添加省略号
    if ($startPage > 2) {
        $html .= '<span class="dots">...</span>';
    }
    
    // 显示中间页码
    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $currentPage) {
            $html .= '<span class="current">' . $i . '</span>';
        } else {
            if ($urlCallback && is_callable($urlCallback)) {
                $pageUrl = $urlCallback($i);
            } else {
                $pageUrl = str_replace('{page}', $i, $urlPattern);
            }
            $html .= '<a href="' . $pageUrl . '">' . $i . '</a>';
        }
    }
    
    // 添加省略号
    if ($endPage < $totalPages - 1) {
        $html .= '<span class="dots">...</span>';
    }
    
    // 始终显示最后一页
    if ($totalPages > 1) {
        if ($currentPage == $totalPages) {
            $html .= '<span class="current">' . $totalPages . '</span>';
        } else {
            if ($urlCallback && is_callable($urlCallback)) {
                $lastUrl = $urlCallback($totalPages);
            } else {
                $lastUrl = str_replace('{page}', $totalPages, $urlPattern);
            }
            $html .= '<a href="' . $lastUrl . '">' . $totalPages . '</a>';
        }
    }
    
    // 下一页
    if ($currentPage < $totalPages) {
        if ($urlCallback && is_callable($urlCallback)) {
            $nextUrl = $urlCallback($currentPage + 1);
        } else {
            $nextUrl = str_replace('{page}', $currentPage + 1, $urlPattern);
        }
        $html .= '<a href="' . $nextUrl . '">下一页</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * 验证信息管理密码
 * 
 * @param int $postId 信息ID
 * @param string $password 加密后的密码(MD5)
 * @return bool 验证是否通过
 */
function verifyPassword($postId, $password) {
    global $db;
    
    // 使用静态变量缓存验证结果
    static $verifiedPasswords = array();
    $cacheKey = $postId . '_' . $password;
    
    // 检查缓存
    if (isset($verifiedPasswords[$cacheKey])) {
        return $verifiedPasswords[$cacheKey];
    }
    
    // 参数验证
    $postId = intval($postId);
    if ($postId <= 0) {
        return false;
    }
    
    // 只查询post_contents表
    $sql = "SELECT id FROM post_contents WHERE post_id = ? AND password = ?";
    $result = $db->query($sql, [$postId, $password]);
    
    // 缓存结果
    $verifiedPasswords[$cacheKey] = ($result && $db->fetch_array($result)) ? true : false;
    
    return $verifiedPasswords[$cacheKey];
}

/**
 * 删除信息
 * @param int $postId 信息ID
 * @param bool $hardDelete 是否彻底删除（默认为false，表示软删除）
 * @return bool 操作是否成功
 */
function deletePost($postId, $hardDelete = false) {
    global $db;
    
    // 获取帖子信息
    $post = getPostDetail($postId, false);
    if (!$post) {
        return false;
    }
    
    // 记录帖子ID和分类ID，用于后续清除缓存
    $categoryId = $post['category_id'];
    
    if ($hardDelete) {
        // 开始事务
        $db->beginTransaction();
        
        try {
            // 首先删除post_contents表中的相关记录
            $content_sql = "DELETE FROM post_contents WHERE post_id = ?";
            $db->query($content_sql, [$postId]);
            
            // 然后删除posts表中的记录
            $sql = "DELETE FROM posts WHERE id = ?";
            $result = $db->query($sql, [$postId]);
            
            // 如果是硬删除，同时删除附件
            if (function_exists('deletePostAttachments')) {
                deletePostAttachments($postId);
            }
            
            // 更新分类计数表 - 减少计数
            updateCategoryCount($categoryId, -1);
            
            // 提交事务
            $db->commit();

            // 清理相关缓存
            clearPostRelatedCaches($postId, $categoryId, 'delete');
        } catch (Exception $e) {
            // 发生错误时回滚事务
            $db->rollback();
            return false;
        }
    } else {
        // 软删除 - 使用状态值3表示"用户删除"，而不是状态值0（待审核）
        $sql = "UPDATE posts SET status = 3, updated_at = ? WHERE id = ?";
        $result = $db->query($sql, [time(), $postId]);

        // 对于软删除也需要更新计数
        if ($result !== false) {
            updateCategoryCount($categoryId, -1);
            // 清理相关缓存
            clearPostRelatedCaches($postId, $categoryId, 'delete');
        }
    }

    return true;
}

/**
 * 刷新信息（更新时间）
 * @param int $postId 信息ID
 * @return bool 操作是否成功
 */
function refreshPost($postId) {
    global $db;
    
    // 获取帖子信息，用于后续清除缓存
    $post = getPostDetail($postId, false);
    if (!$post) {
        return false;
    }
    
    $categoryId = $post['category_id'];
    
    // 获取当前时间
    $currentTime = time();
    
    // 获取距离上次更新的时间
    $sql = "SELECT updated_at, expire_days FROM posts WHERE id = ? AND status = 1";
    $result = $db->query($sql, [$postId]);
    $row = $db->fetch_array($result);
    
    if (!$row) {
        return false; // 信息不存在或已被删除
    }
    
    $lastUpdateTime = $row['updated_at'];
    $expireDays = $row['expire_days'];
    $timeDiff = $currentTime - $lastUpdateTime;
    
    // 限制1小时内只能刷新一次
    if ($timeDiff < 3600) {
        return false;
    }
    
    // 计算新的过期时间
    $newExpireTime = $currentTime + ($expireDays * 86400); // 一天有86400秒
    
    // 使用事务确保数据一致性
    $db->beginTransaction();
    
    try {
        // 更新刷新时间和过期时间
        $sql = "UPDATE posts SET updated_at = ?, expired_at = ? WHERE id = ? AND status = 1";
        $result = $db->query($sql, [$currentTime, $newExpireTime, $postId]);
        
        if ($result === false) {
            throw new Exception("更新刷新时间失败");
        }
        
        // 提交事务
        $db->commit();

        // 清理相关缓存
        clearPostRelatedCaches($postId, $categoryId, 'refresh');

        return true;
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("刷新信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 更新信息
 * @param int $postId 信息ID
 * @param string $title 标题
 * @param string $content 内容
 * @param string $contactName 联系人
 * @param string $contactMobile 联系电话
 * @param string $contactWeixin 微信
 * @param string $contactAddress 地址
 * @param int $regionId 区域ID
 * @param array|null $fields 自定义字段
 * @return bool 操作是否成功
 */
function updatePost($postId, $title, $content, $contactName, $contactMobile, $contactWeixin = '', $contactAddress = '', $regionId = 0, $fields = null) {
    global $db;
    
    // 获取帖子信息，用于后续清除缓存
    $post = getPostDetail($postId, false);
    if (!$post) {
        return false;
    }
    
    $categoryId = $post['category_id'];
    $expireDays = isset($post['expire_days']) ? $post['expire_days'] : 30; // 默认30天
    
    // 获取当前时间
    $currentTime = time();
    
    // 计算新的过期时间
    $newExpireTime = $currentTime + ($expireDays * 86400); // 一天有86400秒
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 更新posts表，仅包含posts表中的字段
        $sql = "UPDATE posts SET 
                title = ?, 
                region_id = ?, 
                updated_at = ?,
                expired_at = ?
                WHERE id = ? AND status = 1";
                
        $result = $db->query($sql, [
            $title, 
            $regionId, 
            $currentTime, 
            $newExpireTime,
            $postId
        ]);
        
        if ($result === false) {
            throw new Exception("更新posts表失败");
        }
        
        // 处理自定义字段数据
        $fieldsData = ($fields !== null) ? json_encode($fields) : '';
        
        // 检查post_contents表中是否存在该记录
        $check_sql = "SELECT id FROM post_contents WHERE post_id = ?";
        $check_result = $db->query($check_sql, [$postId]);
        
        if ($check_result && $db->fetch_array($check_result)) {
            // 存在记录，更新
            $content_sql = "UPDATE post_contents SET 
                           content = ?, 
                           fields_data = ?,
                           contact_name = ?,
                           contact_mobile = ?,
                           contact_weixin = ?,
                           contact_address = ? 
                           WHERE post_id = ?";
            $content_result = $db->query($content_sql, [
                $content, 
                $fieldsData,
                $contactName,
                $contactMobile,
                $contactWeixin,
                $contactAddress,
                $postId
            ]);
            
            if ($content_result === false) {
                throw new Exception("更新post_contents表失败");
            }
        } else {
            // 不存在记录，插入
            $content_sql = "INSERT INTO post_contents (
                          post_id, content, fields_data,
                          contact_name, contact_mobile, contact_weixin, contact_address
                          ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $content_result = $db->query($content_sql, [
                $postId, $content, $fieldsData,
                $contactName, $contactMobile, $contactWeixin, $contactAddress
            ]);
            
            if ($content_result === false) {
                throw new Exception("插入post_contents表失败");
            }
        }
        
        // 提交事务
        $db->commit();

        // 清理相关缓存
        clearPostRelatedCaches($postId, $categoryId, 'update');

        return true;
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("更新信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 搜索信息
 * @param string $keyword 搜索关键词
 * @param int $limit 每页条数
 * @param int $offset 偏移量
 * @return array 搜索结果
 */
function searchPosts($keyword, $limit, $offset) {
    global $db;
    
    // 构建模糊搜索的参数
    $likeKeyword = '%' . $keyword . '%';
    
    // 修改SQL查询，从post_contents表中获取content字段，并支持手机号码搜索
    $sql = "SELECT p.*, c.name as category_name, c.pinyin as category_pinyin, r.name as region_name,
                   pc.content, pc.contact_mobile, pc.contact_name, pc.contact_weixin, pc.contact_address
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN area r ON p.region_id = r.id
            LEFT JOIN post_contents pc ON p.id = pc.post_id
            WHERE (p.title LIKE ? OR pc.content LIKE ? OR pc.contact_mobile LIKE ?)
            AND p.status = 1
            ORDER BY p.updated_at DESC, p.id DESC
            LIMIT ?, ?";

    $result = $db->query($sql, [$likeKeyword, $likeKeyword, $likeKeyword, $offset, $limit]);
    
    $searchResults = array();
    while ($row = $db->fetch_array($result)) {
        // 确保输出内容安全，防止XSS
        $safeRow = array();
        foreach ($row as $key => $value) {
            // 只对字符串类型进行处理
            $safeRow[$key] = is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $value;
        }
        
        // 从安全处理后的内容提取摘要
        $contentPreview = mb_substr(strip_tags($safeRow['content']), 0, 100, 'UTF-8') . '...';
        
        // 高亮关键词
        $safeRow['title_highlight'] = highlightKeyword($safeRow['title'], $keyword);
        $safeRow['content_highlight'] = highlightKeyword($contentPreview, $keyword);

        // 高亮联系方式中的关键词
        if (!empty($safeRow['contact_mobile'])) {
            $safeRow['contact_mobile_highlight'] = highlightKeyword($safeRow['contact_mobile'], $keyword);
        }
        
        // 添加格式化的时间显示
        if (isset($safeRow['created_at'])) {
            $safeRow['created_at_formatted'] = formatRelativeTime($safeRow['created_at']);
        }
        
        if (isset($safeRow['updated_at'])) {
            $safeRow['updated_at_formatted'] = formatRelativeTime($safeRow['updated_at']);
        }
        
        $searchResults[] = $safeRow;
    }
    
    return $searchResults;
}

/**
 * 计算搜索结果总数
 * @param string $keyword 搜索关键词
 * @return int 结果总数
 */
function countSearchResults($keyword) {
    global $db;
    
    // 构建模糊搜索的参数
    $likeKeyword = '%' . $keyword . '%';
    
    // 修改SQL查询，从post_contents表中获取content字段，并支持手机号码搜索
    $sql = "SELECT COUNT(*) as total
            FROM posts p
            LEFT JOIN post_contents pc ON p.id = pc.post_id
            WHERE (p.title LIKE ? OR pc.content LIKE ? OR pc.contact_mobile LIKE ?)
            AND p.status = 1";

    $result = $db->query($sql, [$likeKeyword, $likeKeyword, $likeKeyword]);
    $row = $db->fetch_array($result);
    
    // 限制最大结果数，防止过大结果集影响性能
    $maxResults = 2000;
    return min(intval($row['total']), $maxResults);
}

/**
 * 高亮关键词，安全处理正则表达式
 * @param string $text 原文本
 * @param string $keyword 关键词
 * @return string 高亮后的文本
 */
function highlightKeyword($text, $keyword) {
    if (empty($keyword) || empty($text)) {
        return $text;
    }
    
    // 对关键词进行正则表达式特殊字符转义
    $escapedKeyword = preg_quote($keyword, '/');
    
    // 使用安全的正则表达式替换
    $highlight = '<span class="highlight">$0</span>';
    
    // 添加正则表达式处理安全保护
    try {
        $result = @preg_replace("/($escapedKeyword)/iu", $highlight, $text);
        
        // 检查替换是否成功，如果失败则返回原文本
        if ($result === null || $result === false) {
            return $text;
        }
        
        return $result;
    } catch (Exception $e) {
        // 如果发生任何异常，返回原始文本
        return $text;
    }
}

/**
 * 获取分类的父分类ID
 * @param int $categoryId 分类ID
 * @return int 父分类ID，如果没有则返回0
 */
function getCategoryParentId($categoryId) {
    global $db;
    
    $sql = "SELECT parent_id FROM categories WHERE id = ?";
    $result = $db->query($sql, [$categoryId]);
    
    if ($result && $row = $db->fetch_array($result)) {
        return (int)$row['parent_id'];
    }
    
    return 0;
}

/**
 * 更新分类帖子数量
 * @param int $categoryId 分类ID
 * @param int $delta 增减数量，正数为增加，负数为减少
 * @return bool 更新是否成功
 */
function updateCategoryCount($categoryId, $delta = 1) {
    global $db;

    $categoryId = intval($categoryId);
    $delta = intval($delta);

    if ($categoryId <= 0) {
        return false;
    }

    try {
        // 检查post_count表中是否存在该分类的记录
        $check_sql = "SELECT post_count FROM post_count WHERE category_id = ?";
        $check_result = $db->query($check_sql, [$categoryId]);

        if ($check_result && $db->fetch_array($check_result)) {
            // 存在记录，更新计数
            $update_sql = "UPDATE post_count SET post_count = GREATEST(0, post_count + ?) WHERE category_id = ?";
            $result = $db->query($update_sql, [$delta, $categoryId]);
        } else {
            // 不存在记录，插入新记录
            $initial_count = max(0, $delta); // 确保初始计数不为负数
            $insert_sql = "INSERT INTO post_count (category_id, post_count) VALUES (?, ?)";
            $result = $db->query($insert_sql, [$categoryId, $initial_count]);
        }

        return $result !== false;
    } catch (Exception $e) {
        error_log("更新分类计数失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取列表分页信息
 * 根据配置中的列表分页数量进行分页
 *
 * @param int $total 总记录数
 * @param int $page 当前页码
 * @return array 分页信息数组
 */
function getPageInfo($total, $page = 1) {
    global $config;
    
    // 获取配置的每页数量，默认为20
    $page_size = isset($config['list_page_size']) ? intval($config['list_page_size']) : 20;
    
    // 确保每页数量至少为1
    $page_size = max(1, $page_size);
    
    // 确保当前页码至少为1
    $page = max(1, intval($page));
    
    // 计算总页数
    $total_pages = ceil($total / $page_size);
    
    // 确保当前页不超过总页数
    if ($total_pages > 0) {
        $page = min($page, $total_pages);
    }
    
    // 计算偏移量
    $offset = ($page - 1) * $page_size;
    
    // 返回分页信息
    return [
        'page' => $page,
        'page_size' => $page_size,
        'total' => $total,
        'total_pages' => $total_pages,
        'offset' => $offset,
        'has_prev' => $page > 1,
        'has_next' => $page < $total_pages
    ];
}

/**
 * 获取首页列表信息数量
 *
 * @return int 每个分类在首页显示的信息数量
 */
function getHomeListCount() {
    global $config;
    
    // 获取配置的首页列表数量，默认为10
    $count = isset($config['home_list_count']) ? intval($config['home_list_count']) : 10;
    
    // 确保数量至少为1
    return max(1, $count);
}

/**
 * 构建简化分页 - 专为移动端设计，只显示上一页和下一页
 * 
 * @param int $currentPage 当前页码
 * @param int $totalPages 总页数
 * @param string $urlPattern URL模式，使用{page}作为页码占位符
 * @param callable $urlCallback 可选的URL生成回调函数
 * @return string 简化分页HTML代码
 */
function buildSimplePagination($currentPage, $totalPages, $urlPattern, $urlCallback = null) {
    if ($totalPages <= 1) {
        return '';
    }
    
    // 确保当前页码在有效范围内
    $currentPage = max(1, min($currentPage, $totalPages));
    
    $html = '<div class="pagination simple-pagination">';
    
    // 上一页按钮
    if ($currentPage > 1) {
        if ($urlCallback && is_callable($urlCallback)) {
            $prevUrl = $urlCallback($currentPage - 1);
        } else {
            $prevUrl = str_replace('{page}', $currentPage - 1, $urlPattern);
        }
        $html .= '<a href="' . $prevUrl . '" class="prev-btn"><i class="fas fa-angle-left"></i> 上一页</a>';
    } else {
        $html .= '<span class="prev-btn disabled"><i class="fas fa-angle-left"></i> 上一页</span>';
    }
    
    // 下一页按钮
    if ($currentPage < $totalPages) {
        if ($urlCallback && is_callable($urlCallback)) {
            $nextUrl = $urlCallback($currentPage + 1);
        } else {
            $nextUrl = str_replace('{page}', $currentPage + 1, $urlPattern);
        }
        $html .= '<a href="' . $nextUrl . '" class="next-btn">下一页 <i class="fas fa-angle-right"></i></a>';
    } else {
        $html .= '<span class="next-btn disabled">下一页 <i class="fas fa-angle-right"></i></span>';
    }
    
    $html .= '</div>';
    
    return $html;
}







