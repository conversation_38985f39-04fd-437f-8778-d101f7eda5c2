<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1755597912,
  'data' => 
  array (
    0 => 
    array (
      'id' => '3',
      'name' => '阿里巴巴',
      'url' => 'https://www.alibaba.com',
      'description' => '全球领先的小企业电子商务公司',
      'logo' => 'https://www.alibaba.com/favicon.ico',
      'sort_order' => '0',
      'target' => '_blank',
    ),
    1 => 
    array (
      'id' => '7',
      'name' => '整租2222',
      'url' => 'http://www.fenlei.com/',
      'description' => '',
      'logo' => NULL,
      'sort_order' => '0',
      'target' => '_blank',
    ),
    2 => 
    array (
      'id' => '8',
      'name' => 'aaa2',
      'url' => 'http://www.fenlei.com/',
      'description' => '',
      'logo' => NULL,
      'sort_order' => '1',
      'target' => '_blank',
    ),
    3 => 
    array (
      'id' => '2',
      'name' => '腾讯',
      'url' => 'https://www.qq.com',
      'description' => '中国领先的互联网增值服务提供商',
      'logo' => 'https://www.qq.com/favicon.ico',
      'sort_order' => '2',
      'target' => '_blank',
    ),
    4 => 
    array (
      'id' => '6',
      'name' => '飒飒的',
      'url' => 'http://www.fenlei.com/',
      'description' => '',
      'logo' => NULL,
      'sort_order' => '2',
      'target' => '_blank',
    ),
  ),
);
