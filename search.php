<?php
define('IN_BTMPS', true);
require_once(__DIR__ . '/include/common.inc.php');

/**
 * 信息搜索页面
 * 处理信息搜索功能
 */

// 获取搜索关键词
$keyword = isset($_GET['keyword']) ? filter($_GET['keyword']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$page = max(1, $page); // 确保页码至少为1

// 搜索时间间隔检查
$search_error = '';
$is_new_search = !isset($_GET['page']) || $_GET['page'] <= 1; // 判断是否为新搜索（非分页）

if (!empty($keyword) && $is_new_search) {
    $search_interval = isset($config['search_interval']) ? intval($config['search_interval']) : 5;
    $client_ip = $_SERVER['REMOTE_ADDR'];
    $session_key = 'last_search_time_' . md5($client_ip);

    if (isset($_SESSION[$session_key])) {
        $last_search_time = $_SESSION[$session_key];
        $time_diff = time() - $last_search_time;

        if ($time_diff < $search_interval) {
            $remaining_time = $search_interval - $time_diff;
            $search_error = "搜索过于频繁，请等待 {$remaining_time} 秒后再试";
            $keyword = ''; // 清空关键词，不执行搜索
        }
    }

    // 记录本次搜索时间（只有新搜索才记录）
    if (empty($search_error)) {
        $_SESSION[$session_key] = time();
    }
}

// 使用配置的每页显示数量
$perPage = isset($config['list_page_size']) ? intval($config['list_page_size']) : 20;
// 设置最大页码限制，防止恶意请求大页码
$maxPage = 100; 
$page = min($page, $maxPage); // 限制最大页码
$offset = ($page - 1) * $perPage;

// 如果有搜索关键词
if (!empty($keyword)) {
    // 获取搜索配置
    $keyword_min_length = isset($config['search_keyword_min_length']) ? intval($config['search_keyword_min_length']) : 2;
    $keyword_max_length = isset($config['search_keyword_max_length']) ? intval($config['search_keyword_max_length']) : 50;
    $search_cache_time = isset($config['search_cache_time']) ? intval($config['search_cache_time']) : 600;

    // 检查关键词长度
    $keyword_length = mb_strlen($keyword, 'UTF-8');
    if ($keyword_length < $keyword_min_length) {
        $search_error = "搜索关键词至少需要 {$keyword_min_length} 个字符";
        $keyword = '';
    } elseif ($keyword_length > $keyword_max_length) {
        $keyword = mb_substr($keyword, 0, $keyword_max_length, 'UTF-8');
    }

    // 获取缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_search_time = isset($config['cache_search']) ? intval($config['cache_search']) :
                       (isset($config['search_cache_time']) ? intval($config['search_cache_time']) : 600);

    // 构建缓存键名（区分PC版和手机版）
    $template_suffix = TEMPLATE_DIR; // pc, m, wx, app
    $cache_key = "search_{$template_suffix}_" . md5($keyword . "_page_{$page}_size_{$perPage}");

    // 尝试从缓存获取搜索结果
    $cached_data = false;
    if ($cache_enable && $cache_search_time > 0) {
        $cached_data = cache_get($cache_key);
    }

    if ($cached_data !== false) {
        // 从缓存获取数据
        $searchResults = $cached_data['search_results'];
        $totalCount = $cached_data['total_count'];
        $totalPages = $cached_data['total_pages'];
    } else {
        // 缓存不存在，从数据库查询
        $searchResults = searchPosts($keyword, $perPage, $offset);
        $totalCount = countSearchResults($keyword);

        // 计算总页数
        $totalPages = min(ceil($totalCount / $perPage), $maxPage);

        // 获取缓存设置（优先使用新的配置键名）
        $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
        $cache_search_time = isset($config['cache_search']) ? intval($config['cache_search']) :
                           (isset($config['search_cache_time']) ? intval($config['search_cache_time']) : 600);

        // 缓存搜索结果（使用配置的缓存时间）
        if ($cache_enable && $cache_search_time > 0) {
            $cache_data = array(
                'search_results' => $searchResults,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'keyword' => $keyword,
                'cache_time' => time()
            );
            cache_set($cache_key, $cache_data, $cache_search_time);
        }
    }

    // 分配变量到模板
    assign('keyword', $keyword);
    assign('search_results', $searchResults);
    assign('total_count', $totalCount);
    assign('current_page', $page);
    assign('total_pages', $totalPages);
    assign('per_page', $perPage);
}

// 分配错误信息到模板
if (!empty($search_error)) {
    assign('search_error', $search_error);
}

// 设置当前页面标识
assign('current_page', 'search');

// 显示搜索页面
display('search.htm');


?> 