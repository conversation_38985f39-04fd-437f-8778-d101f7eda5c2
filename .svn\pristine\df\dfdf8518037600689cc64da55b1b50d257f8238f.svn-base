RewriteEngine On

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# 页面整合重写规则 - 保持向后兼容
# 将原来的页面请求重定向到新的统一入口

# 新闻页面（直接访问时）
RewriteRule ^news\.php$ app.php?m=news [L,QSA]

# 分类页面（直接访问时）
RewriteRule ^category\.php$ app.php?m=category [L,QSA]

# 信息发布页面
RewriteRule ^post\.php$ app.php?m=post [L,QSA]

# 搜索页面
RewriteRule ^search\.php$ app.php?m=search [L,QSA]

# 举报页面
RewriteRule ^report\.php$ app.php?m=report [L,QSA]

# 关于我们页面（直接访问时）
RewriteRule ^about\.php$ app.php?m=about [L,QSA]

# 排行榜页面
RewriteRule ^top\.php$ app.php?m=top [L,QSA]

# 信息管理页面
RewriteRule ^manage\.php$ app.php?m=manage [L,QSA]

# 验证码
RewriteRule ^captcha\.php$ app.php?m=captcha [L,QSA]

# 电话号码图片（向后兼容）
RewriteRule ^phone_image\.php$ app.php?m=image&a=phone [L,QSA]

# ����ϵͳα��̬����
# ������ҳ: /news/
RewriteRule ^news/$ pages/news.php [L,QSA]

# ������Ŀҳ: /news/��Ŀƴ��/
RewriteRule ^news/([a-zA-Z0-9_-]+)/$ pages/news.php?catpinyin=$1 [L,QSA]

# ������Ŀ��ҳ: /news/��Ŀƴ��/p2/
RewriteRule ^news/([a-zA-Z0-9_-]+)/p([0-9]+)/$ pages/news.php?catpinyin=$1&page=$2 [L,QSA]

# ��������ҳ: /news/id.html
RewriteRule ^news/([0-9]+)\.html$ pages/news.php?id=$1 [L,QSA]

# ��ҳϵͳα��̬����
# ��ҳ��ʾ: /page/�Զ���·��.html
# 单页系统伪静态规则
# 优先匹配ID访问: /about/123.html (纯数字)
RewriteRule ^about/([0-9]+)\.html$ pages/about.php?id=$1 [L,QSA]
# 然后匹配路径访问: /about/jianjie.html (包含字母)
RewriteRule ^about/([a-zA-Z0-9_/-]+)\.html$ pages/about.php?path=about/$1 [L,QSA]

# ������Ϣϵͳα��̬���򣨱���ԭ�й���
RewriteRule ^([a-zA-Z0-9_-]+)/$ pages/category.php?pinyin=$1 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/([0-9]+)\.html$ pages/view.php?id=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/p([0-9]+)/$ pages/category.php?pinyin=$1&page=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)/$ pages/category.php?pinyin=$1&area=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)p([0-9]+)/$ pages/category.php?pinyin=$1&area=$2&page=$3 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/page/([0-9]+)/$ pages/category.php?pinyin=$1&page=$2 [L,QSA]

RewriteCond %{REQUEST_URI} ^/admin/
RewriteRule ^ - [L]