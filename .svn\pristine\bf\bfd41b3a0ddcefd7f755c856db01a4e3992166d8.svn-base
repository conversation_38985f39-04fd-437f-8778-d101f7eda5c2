/**
 * 后台管理系统核心JavaScript文件
 * 统一管理所有交互功能
 * 支持PHP7.3-PHP8.2，面向过程编程
 * PC端专用，无移动端适配
 */

// 全局管理对象
window.AdminCore = {
    // 配置选项
    config: {
        confirmDeleteText: '确定要删除吗？此操作不可恢复！',
        confirmBatchDeleteText: '确定要批量删除选中的项目吗？此操作不可恢复！',
        loadingText: '处理中...',
        successText: '操作成功',
        errorText: '操作失败',
        noDataText: '暂无数据'
    },

    // 初始化函数
    init: function() {
        this.initSidebar();
        this.initMenuActive();
        this.initMenuGroups();
        this.initConfirmButtons();
        this.initBatchOperations();
        this.initFormValidation();
        this.initTooltips();
        this.initModals();
        this.initTables();
        this.initSwitches();
        this.initTabs();
        console.log('AdminCore initialized');
    },

    // 侧边栏功能
    initSidebar: function() {
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
    },

    // 菜单激活状态
    initMenuActive: function() {
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        let foundActive = false;

        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href && currentPath.endsWith(href)) {
                const menuItem = item.closest('.menu-item');
                if (menuItem) {
                    menuItem.classList.add('active');
                    foundActive = true;
                }
            }
        });

        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];

            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
    },

    // 左右两列菜单交互
    initMenuGroups: function() {
        const primaryMenuItems = document.querySelectorAll('.menu-primary .menu-item[data-submenu]');
        const submenuGroups = document.querySelectorAll('.submenu-group');

        primaryMenuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const submenuId = this.getAttribute('data-submenu');

                // 移除所有一级菜单的激活状态
                primaryMenuItems.forEach(function(menuItem) {
                    menuItem.classList.remove('active');
                });

                // 激活当前点击的一级菜单
                this.classList.add('active');

                // 隐藏所有二级菜单
                submenuGroups.forEach(function(group) {
                    group.classList.remove('active');
                });

                // 显示对应的二级菜单
                const targetSubmenu = document.getElementById('submenu-' + submenuId);
                if (targetSubmenu) {
                    targetSubmenu.classList.add('active');
                }

                // 保存当前激活的菜单
                localStorage.setItem('active_primary_menu', submenuId);
            });
        });

        // 恢复上次激活的菜单状态
        const savedMenu = localStorage.getItem('active_primary_menu');
        if (savedMenu) {
            const savedMenuItem = document.querySelector('.menu-primary .menu-item[data-submenu="' + savedMenu + '"]');
            const savedSubmenu = document.getElementById('submenu-' + savedMenu);

            if (savedMenuItem && savedSubmenu) {
                // 移除默认激活状态
                primaryMenuItems.forEach(function(item) {
                    item.classList.remove('active');
                });
                submenuGroups.forEach(function(group) {
                    group.classList.remove('active');
                });

                // 激活保存的菜单
                savedMenuItem.classList.add('active');
                savedSubmenu.classList.add('active');
            }
        }
    },

    // 确认按钮
    initConfirmButtons: function() {
        const self = this;
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-confirm') || e.target.closest('.btn-confirm')) {
                const button = e.target.classList.contains('btn-confirm') ? e.target : e.target.closest('.btn-confirm');
                const message = button.getAttribute('data-confirm') || self.config.confirmDeleteText;
                
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    },

    // 批量操作
    initBatchOperations: function() {
        const self = this;
        
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="ids[]"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                self.updateBatchButtons();
            });
        }
        
        // 单个复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.name === 'ids[]') {
                self.updateBatchButtons();
                
                // 更新全选状态
                const checkboxes = document.querySelectorAll('input[name="ids[]"]');
                const checkedBoxes = document.querySelectorAll('input[name="ids[]"]:checked');
                const selectAll = document.getElementById('select-all');
                
                if (selectAll) {
                    selectAll.checked = checkboxes.length === checkedBoxes.length;
                    selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
                }
            }
        });
        
        // 批量删除按钮
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-batch-delete')) {
                const checkedBoxes = document.querySelectorAll('input[name="ids[]"]:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要删除的项目');
                    e.preventDefault();
                    return false;
                }
                
                if (!confirm(self.config.confirmBatchDeleteText)) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    },

    // 更新批量操作按钮状态
    updateBatchButtons: function() {
        const checkedBoxes = document.querySelectorAll('input[name="ids[]"]:checked');
        const batchButtons = document.querySelectorAll('.batch-action-btn');
        
        batchButtons.forEach(function(button) {
            button.disabled = checkedBoxes.length === 0;
            if (checkedBoxes.length === 0) {
                button.classList.add('disabled');
            } else {
                button.classList.remove('disabled');
            }
        });
        
        // 更新选中数量显示
        const countElement = document.querySelector('.selected-count');
        if (countElement) {
            countElement.textContent = checkedBoxes.length;
        }
    },

    // 表单验证
    initFormValidation: function() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                if (!AdminCore.validateForm(form)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    },

    // 验证表单
    validateForm: function(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(function(field) {
            if (!field.value.trim()) {
                AdminCore.showFieldError(field, '此字段为必填项');
                isValid = false;
            } else {
                AdminCore.clearFieldError(field);
            }
        });
        
        return isValid;
    },

    // 显示字段错误
    showFieldError: function(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    },

    // 清除字段错误
    clearFieldError: function(field) {
        field.classList.remove('error');
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    },

    // 工具提示
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(function(element) {
            element.addEventListener('mouseenter', function() {
                AdminCore.showTooltip(this);
            });
            
            element.addEventListener('mouseleave', function() {
                AdminCore.hideTooltip();
            });
        });
    },

    // 显示工具提示
    showTooltip: function(element) {
        const text = element.getAttribute('data-tooltip');
        if (!text) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'admin-tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    },

    // 隐藏工具提示
    hideTooltip: function() {
        const tooltip = document.querySelector('.admin-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },

    // 模态框
    initModals: function() {
        document.addEventListener('click', function(e) {
            // 打开模态框
            if (e.target.hasAttribute('data-modal')) {
                const modalId = e.target.getAttribute('data-modal');
                AdminCore.openModal(modalId);
            }
            
            // 关闭模态框
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('modal')) {
                AdminCore.closeModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                AdminCore.closeModal();
            }
        });
    },

    // 打开模态框
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    },

    // 关闭模态框
    closeModal: function() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(function(modal) {
            modal.style.display = 'none';
        });
        document.body.style.overflow = '';
    },

    // 表格功能
    initTables: function() {
        // 表格排序
        const sortableHeaders = document.querySelectorAll('th[data-sort]');
        sortableHeaders.forEach(function(header) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                AdminCore.sortTable(this);
            });
        });
        
        // 表格筛选
        const filterInputs = document.querySelectorAll('.table-filter');
        filterInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                AdminCore.filterTable(this);
            });
        });
    },

    // 表格排序
    sortTable: function(header) {
        // 这里可以实现客户端排序或发送AJAX请求
        console.log('Sort table by:', header.getAttribute('data-sort'));
    },

    // 表格筛选
    filterTable: function(input) {
        // 这里可以实现客户端筛选或发送AJAX请求
        console.log('Filter table:', input.value);
    },

    // 显示加载状态
    showLoading: function(element) {
        if (element) {
            element.disabled = true;
            element.textContent = this.config.loadingText;
        }
    },

    // 隐藏加载状态
    hideLoading: function(element, originalText) {
        if (element) {
            element.disabled = false;
            element.textContent = originalText || '提交';
        }
    },

    // 显示消息
    showMessage: function(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} admin-message`;
        messageDiv.innerHTML = `
            <i class="fas fa-${this.getMessageIcon(type)}"></i>
            <span>${message}</span>
            <button type="button" class="message-close">&times;</button>
        `;
        
        document.body.appendChild(messageDiv);
        
        // 自动关闭
        setTimeout(function() {
            messageDiv.remove();
        }, 5000);
        
        // 手动关闭
        messageDiv.querySelector('.message-close').addEventListener('click', function() {
            messageDiv.remove();
        });
    },

    // 获取消息图标
    getMessageIcon: function(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    // 开关组件交互
    initSwitches: function() {
        const switches = document.querySelectorAll('.switch input[type="checkbox"]');

        switches.forEach(function(switchInput) {
            switchInput.addEventListener('change', function() {
                const label = this.closest('.switch').querySelector('.switch-label');
                if (label) {
                    console.log('开关状态改变:', label.textContent, this.checked ? '开启' : '关闭');

                    // 可以在这里添加AJAX请求保存状态
                    // AdminCore.showMessage(label.textContent + (this.checked ? ' 已开启' : ' 已关闭'), 'success');
                }
            });
        });
    },

    // 选项卡交互
    initTabs: function() {
        const tabs = document.querySelectorAll('.setting-tab');

        tabs.forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                // 检查是否是设置页面的选项卡
                if (this.href && this.href.includes('?group=')) {
                    // 允许正常的链接跳转，不阻止默认行为
                    return true;
                }

                // 对于其他选项卡，阻止默认行为并处理切换
                e.preventDefault();

                // 移除所有激活状态
                tabs.forEach(function(t) {
                    t.classList.remove('active');
                });

                // 激活当前选项卡
                this.classList.add('active');

                console.log('选项卡切换:', this.textContent);

                // 如果有data-tab属性，切换对应内容
                const tabId = this.getAttribute('data-tab');
                if (tabId) {
                    AdminCore.showTabContent(tabId);
                }
            });
        });
    },

    // 显示选项卡内容
    showTabContent: function(tabId) {
        // 隐藏所有选项卡内容
        const allContents = document.querySelectorAll('.tab-content');
        allContents.forEach(function(content) {
            content.style.display = 'none';
        });

        // 显示对应的选项卡内容
        const targetContent = document.getElementById(tabId);
        if (targetContent) {
            targetContent.style.display = 'block';
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    AdminCore.init();
});
