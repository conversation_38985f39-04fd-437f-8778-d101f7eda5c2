<?php
/**
 * 完整的发布信息问题修复和测试脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 启动会话
session_start();

// 定义常量
define('IN_BTMPS', true);
define('ROOT_PATH', dirname(__FILE__) . '/');
define('INCLUDE_PATH', ROOT_PATH . 'include/');
define('CONFIG_PATH', ROOT_PATH . 'config/');

// 加载必要文件
require_once(INCLUDE_PATH . 'global.fun.php');
require_once(INCLUDE_PATH . 'mysql.class.php');
require_once(CONFIG_PATH . 'config.db.php');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布信息问题完整修复</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .fix-box { background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0; }
        .test-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        input, textarea, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; font-weight: bold; margin-bottom: 5px; }
    </style>
</head>
<body>
    <h1>发布信息问题完整修复</h1>
    
    <?php
    // 处理修复操作
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['test_post'])) {
            // 测试发布功能
            echo "<div class='test-box'>";
            echo "<h3>发布功能测试结果</h3>";
            
            try {
                // 创建数据库连接
                $db = new MySQL();
                $connect_result = $db->connect(
                    $config['db_host'],
                    $config['db_user'], 
                    $config['db_pass'],
                    $config['db_name'],
                    $config['db_charset'],
                    $config['db_port']
                );
                
                if ($connect_result !== true) {
                    throw new Exception("数据库连接失败: " . $connect_result);
                }
                
                // 模拟发布数据
                $title = $_POST['title'] ?? '测试标题';
                $content = $_POST['content'] ?? '测试内容';
                $contact_name = $_POST['contact_name'] ?? '测试用户';
                $contact_mobile = $_POST['contact_mobile'] ?? '13800138000';
                $region_id = intval($_POST['region_id'] ?? 1);
                $category_id = intval($_POST['category_id'] ?? 1);
                $expire_days = intval($_POST['expire_days'] ?? 30);
                
                // 验证数据
                $errors = [];
                if (empty($title)) $errors[] = '标题不能为空';
                if (empty($content)) $errors[] = '内容不能为空';
                if (empty($contact_name)) $errors[] = '联系人不能为空';
                if (empty($contact_mobile)) $errors[] = '手机号不能为空';
                
                if (!empty($errors)) {
                    throw new Exception("数据验证失败: " . implode(', ', $errors));
                }
                
                // 开始发布流程
                $currentTime = time();
                $expireTime = $currentTime + ($expire_days * 86400);
                $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
                $password = md5('test123');
                
                $db->beginTransaction();
                
                // 插入posts表
                $sql = "INSERT INTO posts (category_id, title, region_id, expire_days, status, addtime, updatetime, expiretime) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $result = $db->query($sql, [$category_id, $title, $region_id, $expire_days, 1, $currentTime, $currentTime, $expireTime]);
                
                if ($result === false) {
                    throw new Exception("插入posts表失败: " . $db->error());
                }
                
                $postId = $db->lastInsertId();
                if ($postId <= 0) {
                    throw new Exception("获取插入ID失败");
                }
                
                // 插入post_contents表
                $contentSql = "INSERT INTO post_contents (post_id, content, fields_data, ip, password, contact_name, contact_mobile, contact_weixin, contact_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $contentResult = $db->query($contentSql, [$postId, $content, '', $ip, $password, $contact_name, $contact_mobile, '', '']);
                
                if ($contentResult === false) {
                    throw new Exception("插入post_contents表失败: " . $db->error());
                }
                
                $db->commit();
                
                echo "<p class='success'>✓ 发布测试成功！</p>";
                echo "<p>信息ID: {$postId}</p>";
                echo "<p>标题: {$title}</p>";
                echo "<p>联系人: {$contact_name}</p>";
                
            } catch (Exception $e) {
                if (isset($db)) {
                    $db->rollback();
                }
                echo "<p class='error'>✗ 发布测试失败: " . $e->getMessage() . "</p>";
            }
            
            echo "</div>";
        }
        
        if (isset($_POST['fix_csrf'])) {
            // 修复CSRF问题
            echo "<div class='fix-box'>";
            echo "<h3>CSRF修复结果</h3>";
            
            // 生成新的CSRF令牌
            $new_token = generate_csrf_token(true);
            echo "<p class='success'>✓ 已生成新的CSRF令牌</p>";
            echo "<p>令牌: " . substr($new_token, 0, 20) . "...</p>";
            
            echo "</div>";
        }
        
        if (isset($_POST['check_logs'])) {
            // 检查错误日志
            echo "<div class='info'>";
            echo "<h3>错误日志检查</h3>";
            
            $log_file = DATA_PATH . 'logs/' . date('Y-m-d') . '.log';
            if (file_exists($log_file)) {
                $logs = file_get_contents($log_file);
                $recent_logs = array_slice(explode("\n", $logs), -20);
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;'>";
                echo htmlspecialchars(implode("\n", $recent_logs));
                echo "</pre>";
            } else {
                echo "<p>今日暂无日志文件</p>";
            }
            
            // 检查PHP错误日志
            $php_log = ini_get('error_log');
            if ($php_log && file_exists($php_log)) {
                echo "<h4>PHP错误日志（最近10行）:</h4>";
                $php_logs = file($php_log);
                $recent_php_logs = array_slice($php_logs, -10);
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;'>";
                echo htmlspecialchars(implode("", $recent_php_logs));
                echo "</pre>";
            }
            
            echo "</div>";
        }
    }
    ?>
    
    <!-- 系统状态检查 -->
    <div class="info">
        <h2>系统状态检查</h2>
        <?php
        // 检查PHP扩展
        $extensions = ['mysqli', 'pdo', 'pdo_mysql', 'gd', 'json'];
        foreach ($extensions as $ext) {
            $status = extension_loaded($ext) ? '✓' : '✗';
            $class = extension_loaded($ext) ? 'success' : 'error';
            echo "<p class='{$class}'>{$status} {$ext}</p>";
        }
        
        // 检查数据库连接
        try {
            $db = new MySQL();
            $result = $db->connect($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name'], $config['db_charset'], $config['db_port']);
            if ($result === true) {
                echo "<p class='success'>✓ 数据库连接正常</p>";
            } else {
                echo "<p class='error'>✗ 数据库连接失败: {$result}</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>✗ 数据库连接异常: " . $e->getMessage() . "</p>";
        }
        
        // 检查会话
        if (session_status() === PHP_SESSION_ACTIVE) {
            echo "<p class='success'>✓ 会话正常</p>";
        } else {
            echo "<p class='error'>✗ 会话异常</p>";
        }
        
        // 检查CSRF令牌
        if (isset($_SESSION['csrf_token'])) {
            echo "<p class='success'>✓ CSRF令牌存在</p>";
        } else {
            echo "<p class='warning'>⚠ CSRF令牌不存在</p>";
        }
        ?>
    </div>
    
    <!-- 修复操作 -->
    <div class="fix-box">
        <h2>修复操作</h2>
        <form method="POST" style="display: inline;">
            <button type="submit" name="fix_csrf" class="btn-primary">修复CSRF令牌</button>
        </form>
        
        <form method="POST" style="display: inline;">
            <button type="submit" name="check_logs" class="btn-warning">检查错误日志</button>
        </form>
    </div>
    
    <!-- 发布功能测试 -->
    <div class="test-box">
        <h2>发布功能测试</h2>
        <form method="POST">
            <div class="form-group">
                <label>标题:</label>
                <input type="text" name="title" value="测试信息标题" required>
            </div>
            
            <div class="form-group">
                <label>内容:</label>
                <textarea name="content" rows="4" required>这是一个测试信息的详细内容，用于测试发布功能是否正常工作。</textarea>
            </div>
            
            <div class="form-group">
                <label>联系人:</label>
                <input type="text" name="contact_name" value="测试用户" required>
            </div>
            
            <div class="form-group">
                <label>手机号:</label>
                <input type="text" name="contact_mobile" value="13800138000" required>
            </div>
            
            <div class="form-group">
                <label>地区ID:</label>
                <input type="number" name="region_id" value="1" required>
            </div>
            
            <div class="form-group">
                <label>分类ID:</label>
                <input type="number" name="category_id" value="1" required>
            </div>
            
            <div class="form-group">
                <label>有效期(天):</label>
                <select name="expire_days">
                    <option value="7">7天</option>
                    <option value="15">15天</option>
                    <option value="30" selected>30天</option>
                    <option value="60">60天</option>
                    <option value="90">90天</option>
                </select>
            </div>
            
            <button type="submit" name="test_post" class="btn-success">测试发布功能</button>
        </form>
    </div>
    
    <!-- 快速链接 -->
    <div class="info">
        <h2>快速链接</h2>
        <p><a href="post.php?catid=1" target="_blank">PC端发布页面</a></p>
        <p><a href="post.php?catid=1" target="_blank">移动端发布页面</a></p>
        <p><a href="simple_post_test.php" target="_blank">简化发布测试</a></p>
    </div>
    
    <hr>
    <p><small>修复完成后请删除此文件以确保安全</small></p>
</body>
</html>
