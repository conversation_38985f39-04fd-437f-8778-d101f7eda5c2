<?php
if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

/**
 * 信息管理页面
 * 处理信息的修改、删除、刷新等操作
 */

// 清理输出缓冲区，确保没有额外输出
if (ob_get_level()) {
    ob_clean();
}

// 检测是否为AJAX请求
$is_ajax_request = (isset($_GET['ajax']) && $_GET['ajax'] == '1') ||
                   (isset($_POST['ajax']) && $_POST['ajax'] == '1') ||
                   (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');

// 如果是AJAX请求，设置JSON头信息
if ($is_ajax_request) {
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
}

// 注释掉清除编辑会话状态的代码，保持验证状态
// if (isset($_SESSION['edit_verified'])) {
//     $_SESSION['edit_verified'] = array();
// }

// 引入操作日志类（如果还未引入）
if (!class_exists('OperationLogger')) {
    require_once './include/OperationLogger.class.php';
}

// 获取操作类型和ID
$action = isset($_POST['action']) ? filter($_POST['action']) : (isset($_GET['action']) ? filter($_GET['action']) : '');
$id = isset($_POST['id']) ? filter($_POST['id'], 'int') : (isset($_GET['id']) ? filter($_GET['id'], 'int') : 0);



// 检查信息ID是否有效
if (!$id) {
    if ($is_ajax_request) {
        echo json_encode(['success' => false, 'message' => '参数错误：未指定信息ID']);
        exit;
    } else {
        show_error('参数错误：未指定信息ID');
    }
}

// 获取密码参数（如果存在）
$password = isset($_POST['password']) ? filter($_POST['password']) : (isset($_GET['password']) ? filter($_GET['password']) : '');

// 获取信息详情
$post = getPostDetail($id, false); // 第二个参数false表示不检查状态
if (!$post) {
    if ($is_ajax_request) {
        echo json_encode(['success' => false, 'message' => '信息不存在或已被删除']);
        exit;
    } else {
        show_error('信息不存在或已被删除');
    }
}

// 验证是否有操作权限
$verified = false;

// 检查会话中是否已验证
if (isset($_SESSION['edit_verified'][$id]) && $_SESSION['edit_verified'][$id] === true) {
    $verified = true;
} elseif (isset($_COOKIE['edit_verified_'.$id]) && $_COOKIE['edit_verified_'.$id] === 'true') {
    // 检查cookie是否验证通过
    $verified = true;
    // 同步到会话中
    if (!isset($_SESSION['edit_verified'])) {
        $_SESSION['edit_verified'] = array();
    }
    $_SESSION['edit_verified'][$id] = true;
} elseif (!empty($password)) {
    // 如果有密码参数，验证是否正确
    $verified = verifyPassword($id, md5($password));
    if ($verified) {
        // 记录验证状态到会话
        if (!isset($_SESSION['edit_verified'])) {
            $_SESSION['edit_verified'] = array();
        }
        $_SESSION['edit_verified'][$id] = true;
        
        // 同时设置cookie
        setcookie('edit_verified_'.$id, 'true', time() + 3600, '/');
    }
}

// 根据请求是否是JSON格式的来决定返回方式
$is_json_request = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// 如果是验证请求
if ($action === 'verify') {
    if ($is_json_request) {
        if ($verified) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => '管理密码不正确']);
        }
        exit;
    }
}

// 如果验证通过且没有特定操作，显示管理界面
if ($verified && empty($action)) {
    // 渲染管理界面
    assign('post', $post);
    assign('verified', $verified);
    assign('current_page', 'manage');
    $isMobile = (TEMPLATE_DIR === 'm');
    display('manage.htm', $isMobile);
    exit;
}

// 根据操作类型执行不同逻辑
switch ($action) {
    case '修改':
    case 'edit':
        // 已验证，直接重定向到编辑页面
        if ($verified) {
            if ($is_json_request) {
                echo json_encode(['success' => true, 'redirect' => "app.php?m=post&action=edit&id=$id"]);
                exit;
            } else {
                redirect("app.php?m=post&action=edit&id=$id");
            }
        }
        // 验证密码
        elseif (!empty($password)) {
            if (verifyPassword($id, md5($password))) {
                // 重定向到编辑页面并传递密码参数
                if ($is_json_request) {
                    echo json_encode(['success' => true, 'redirect' => "app.php?m=post&action=edit&id=$id&password=" . urlencode($password)]);
                    exit;
                } else {
                    redirect("app.php?m=post&action=edit&id=$id&password=" . urlencode($password));
                }
            } else {
                if ($is_json_request) {
                    echo json_encode(['success' => false, 'message' => '管理密码不正确']);
                    exit;
                } else {
                    // 显示错误信息并重定向到详情页
                    assign('error', '管理密码不正确');
                    assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                    $isMobile = (TEMPLATE_DIR === 'm');
                    display('error.htm', $isMobile);
                    exit;
                }
            }
        } else {
            if ($is_json_request) {
                echo json_encode(['success' => false, 'message' => '请输入管理密码']);
                exit;
            } else {
                show_error('请输入管理密码');
            }
        }
        break;
        
    case '删除':
    case 'delete':
        // 已验证，直接执行删除
        if ($verified) {
            // 只执行软删除，保留附件和数据库记录但将状态设为0
            if (deletePost($id, false)) {
                // 记录操作日志
                $logger = new OperationLogger($db);
                $logger->log([
                    'operation_type' => 'delete',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => '用户删除信息',
                    'target_title' => $post['title'],
                    'user_id' => 0, // 用户ID为0，表示前台用户
                    'username' => '前台用户',
                    'user_type' => 'user'
                ]);

                // 更新分类统计（减少计数）
                updateCategoryCount($post['category_id'], -1);

                if ($is_json_request) {
                    echo json_encode(['success' => true, 'message' => '信息删除成功', 'redirect' => '/']);
                    exit;
                } else {
                    show_success('信息删除成功', '/');
                }
            } else {
                if ($is_json_request) {
                    echo json_encode(['success' => false, 'message' => '信息删除失败，请稍后重试']);
                    exit;
                } else {
                    show_error('信息删除失败，请稍后重试');
                }
            }
        }
        // 验证管理密码
        elseif (!empty($password)) {
            if (verifyPassword($id, md5($password))) {
                // 执行软删除操作
                if (deletePost($id, false)) {
                    // 记录操作日志
                    $logger = new OperationLogger($db);
                    $logger->log([
                        'operation_type' => 'delete',
                        'target_type' => 'post',
                        'target_id' => $id,
                        'operation_desc' => '用户删除信息',
                        'target_title' => $post['title'],
                        'user_id' => 0, // 用户ID为0，表示前台用户
                        'username' => '前台用户',
                        'user_type' => 'user'
                    ]);

                    // 更新分类统计（减少计数）
                    updateCategoryCount($post['category_id'], -1);

                    // 清理相关缓存（删除信息，不需要清理首页缓存）
                    clearPostRelatedCaches($id, $post['category_id'], 'delete');

                    if ($is_json_request) {
                        echo json_encode(['success' => true, 'message' => '信息删除成功', 'redirect' => '/']);
                        exit;
                    } else {
                        show_success('信息删除成功', '/');
                    }
                } else {
                    if ($is_json_request) {
                        echo json_encode(['success' => false, 'message' => '信息删除失败，请稍后重试']);
                        exit;
                    } else {
                        show_error('信息删除失败，请稍后重试');
                    }
                }
            } else {
                if ($is_json_request) {
                    echo json_encode(['success' => false, 'message' => '管理密码不正确']);
                    exit;
                } else {
                    // 显示错误信息并重定向到详情页
                    assign('error', '管理密码不正确');
                    assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                    $isMobile = (TEMPLATE_DIR === 'm');
                    display('error.htm', $isMobile);
                    exit;
                }
            }
        } else {
            if ($is_json_request) {
                echo json_encode(['success' => false, 'message' => '请输入管理密码']);
                exit;
            } else {
                show_error('请输入管理密码');
            }
        }
        break;
        
    case '刷新':
    case 'refresh':
        // 处理刷新操作
        
        // 如果是已验证状态或有密码验证
        if ($verified) {
            // 获取当前信息的上次更新时间
            $sql = "SELECT updated_at FROM posts WHERE id = ? AND status = 1";
            $result = $db->query($sql, [$id]);
            $row = $db->fetch_array($result);
            
            // 检查距离上次刷新是否已经过了一小时
            $currentTime = time();
            $lastUpdateTime = $row['updated_at'];
            $timeDiff = $currentTime - $lastUpdateTime;
            
            if ($timeDiff < 3600) {
                // 未满一小时，计算还需等待的时间
                $waitMinutes = ceil((3600 - $timeDiff) / 60);
                
                if ($is_ajax_request) {
                    echo json_encode(['success' => false, 'message' => "操作过于频繁，距离上次刷新不足1小时，请等待{$waitMinutes}分钟后再试"]);
                    exit;
                } else {
                    // 显示错误信息并重定向到信息详情页
                    assign('error', "操作过于频繁，距离上次刷新不足1小时，请等待{$waitMinutes}分钟后再试");
                    assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                    $isMobile = (TEMPLATE_DIR === 'm');
                    display('error.htm', $isMobile);
                    exit;
                }
            }
            
            // 执行刷新操作
            if (refreshPost($id)) {
                // 记录操作日志
                $logger = new OperationLogger($db);
                $logger->log([
                    'operation_type' => 'update',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => '用户刷新信息',
                    'target_title' => $post['title'],
                    'user_id' => 0, // 用户ID为0，表示前台用户
                    'username' => '前台用户',
                    'user_type' => 'user'
                ]);

                // 清理相关缓存（刷新信息，需要清理首页缓存以更新排序）
                clearPostRelatedCaches($id, $post['category_id'], 'refresh');

                // 清除会话中的验证状态
                if (isset($_SESSION['edit_verified'][$id])) {
                    unset($_SESSION['edit_verified'][$id]);
                }
                if ($is_ajax_request) {
                    echo json_encode(['success' => true, 'message' => '信息刷新成功', 'redirect' => "/{$post['category_pinyin']}/{$id}.html"]);
                    exit;
                } else {
                    show_success('信息刷新成功', "/{$post['category_pinyin']}/{$id}.html");
                }
            } else {
                if ($is_ajax_request) {
                    echo json_encode(['success' => false, 'message' => '信息刷新失败，距离上次刷新不足1小时，请稍后再试']);
                    exit;
                } else {
                    // 显示错误信息并重定向到信息详情页
                    assign('error', '信息刷新失败，距离上次刷新不足1小时，请稍后再试');
                    assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                    $isMobile = (TEMPLATE_DIR === 'm');
                    display('error.htm', $isMobile);
                    exit;
                }
            }
        } else if (!empty($password)) {
            // 有密码但验证失败
            if ($is_json_request) {
                echo json_encode(['success' => false, 'message' => '管理密码不正确']);
                exit;
            } else {
                // 显示错误信息并重定向到详情页
                assign('error', '管理密码不正确');
                assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                $isMobile = (TEMPLATE_DIR === 'm');
                display('error.htm', $isMobile);
                exit;
            }
        } else {
            // 显示管理界面，需要输入密码
            if ($is_json_request) {
                echo json_encode(['success' => false, 'message' => '请输入管理密码']);
                exit;
            } else {
                assign('post', $post);
                assign('manage_type', 'post');
                $isMobile = (TEMPLATE_DIR === 'm');
                display('view.htm', $isMobile); // 改为重定向到信息查看页面
            }
        }
        break;
        
    default:
        // 只验证密码，不执行其他操作
        if (!empty($password)) {
            $is_verified = verifyPassword($id, md5($password));
            if ($is_verified) {
                // 验证成功，设置会话状态
                if (!isset($_SESSION['edit_verified'])) {
                    $_SESSION['edit_verified'] = array();
                }
                $_SESSION['edit_verified'][$id] = true;

                // 同时设置cookie
                setcookie('edit_verified_'.$id, 'true', time() + 3600, '/');
            }

            if ($is_ajax_request) {
                echo json_encode(['success' => $is_verified, 'message' => $is_verified ? '验证成功' : '管理密码不正确']);
                exit;
            } else {
                if ($is_verified) {
                    // 跳转到详情页面，带上密码参数
                    redirect("/{$post['category_pinyin']}/{$id}.html?password=" . urlencode($password));
                } else {
                    // 显示错误信息并重定向到详情页
                    assign('error', '管理密码不正确');
                    assign('redirect_url', "/{$post['category_pinyin']}/{$id}.html");
                    $isMobile = (TEMPLATE_DIR === 'm');
                    display('error.htm', $isMobile);
                    exit;
                }
            }
        } else {
            // 密码为空
            if ($is_json_request) {
                echo json_encode(['success' => false, 'message' => '请输入管理密码']);
                exit;
            } else {
                // 带上id跳回详情页
                redirect("/{$post['category_pinyin']}/{$id}.html");
            }
        }
}

