<?php
/**
 * 调试白屏问题
 */

define('IN_BTMPS', true);

// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>白屏问题调试</h2>";

// 测试基本PHP功能
echo "<h3>1. PHP基本功能测试</h3>";
echo "<p>✅ PHP正常工作</p>";

// 测试文件包含
echo "<h3>2. 文件包含测试</h3>";
try {
    if (file_exists('./include/common.inc.php')) {
        echo "<p>✅ common.inc.php 文件存在</p>";
        require_once('./include/common.inc.php');
        echo "<p>✅ common.inc.php 包含成功</p>";
    } else {
        echo "<p style='color: red;'>❌ common.inc.php 文件不存在</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 包含 common.inc.php 失败: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>❌ 包含 common.inc.php 错误: " . $e->getMessage() . "</p>";
}

// 测试数据库连接
echo "<h3>3. 数据库连接测试</h3>";
if (isset($db) && is_object($db)) {
    echo "<p>✅ 数据库连接正常</p>";
    echo "<p>数据库类型: " . get_class($db) . "</p>";
} else {
    echo "<p style='color: red;'>❌ 数据库连接异常</p>";
}

// 测试模板引擎
echo "<h3>4. 模板引擎测试</h3>";
if (isset($tpl) && is_object($tpl)) {
    echo "<p>✅ 模板引擎正常</p>";
    echo "<p>模板类型: " . get_class($tpl) . "</p>";
} else {
    echo "<p style='color: red;'>❌ 模板引擎异常</p>";
}

// 测试TEMPLATE_DIR常量
echo "<h3>5. 模板目录测试</h3>";
if (defined('TEMPLATE_DIR')) {
    echo "<p>✅ TEMPLATE_DIR 已定义: " . TEMPLATE_DIR . "</p>";
    $template_path = "template/" . TEMPLATE_DIR . "/edit.htm";
    if (file_exists($template_path)) {
        echo "<p>✅ 编辑模板文件存在: $template_path</p>";
    } else {
        echo "<p style='color: red;'>❌ 编辑模板文件不存在: $template_path</p>";
    }
} else {
    echo "<p style='color: red;'>❌ TEMPLATE_DIR 未定义</p>";
}

// 测试必要函数
echo "<h3>6. 必要函数测试</h3>";
$functions = ['getPostDetail', 'verifyPassword', 'display', 'assign', 'redirect'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p>✅ $func 函数存在</p>";
    } else {
        echo "<p style='color: red;'>❌ $func 函数不存在</p>";
    }
}

// 测试信息获取
echo "<h3>7. 信息获取测试</h3>";
$test_id = 110204;
if (function_exists('getPostDetail')) {
    try {
        $post = getPostDetail($test_id);
        if ($post) {
            echo "<p>✅ 信息获取成功</p>";
            echo "<p>标题: " . htmlspecialchars($post['title']) . "</p>";
            echo "<p>分类: " . htmlspecialchars($post['category_name']) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ 信息不存在 (ID: $test_id)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 获取信息失败: " . $e->getMessage() . "</p>";
    }
}

// 模拟编辑页面访问
echo "<h3>8. 模拟编辑页面访问</h3>";
$_GET['m'] = 'post';
$_GET['action'] = 'edit';
$_GET['id'] = $test_id;

// 设置验证状态
$_SESSION['edit_verified'][$test_id] = true;

echo "<p>设置参数:</p>";
echo "<ul>";
echo "<li>m = post</li>";
echo "<li>action = edit</li>";
echo "<li>id = $test_id</li>";
echo "<li>验证状态 = 已设置</li>";
echo "</ul>";

// 测试pages/post.php包含
echo "<h3>9. 测试 pages/post.php</h3>";
if (file_exists('pages/post.php')) {
    echo "<p>✅ pages/post.php 文件存在</p>";
    
    // 检查语法
    $syntax_check = shell_exec('php -l pages/post.php 2>&1');
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "<p>✅ pages/post.php 语法正确</p>";
    } else {
        echo "<p style='color: red;'>❌ pages/post.php 语法错误:</p>";
        echo "<pre style='color: red;'>$syntax_check</pre>";
    }
    
    // 尝试包含并捕获输出
    echo "<p>尝试包含 pages/post.php...</p>";
    try {
        ob_start();
        include 'pages/post.php';
        $output = ob_get_contents();
        ob_end_clean();
        
        if (empty($output)) {
            echo "<p style='color: red;'>❌ pages/post.php 没有输出</p>";
        } else {
            echo "<p>✅ pages/post.php 有输出 (" . strlen($output) . " 字符)</p>";
            echo "<h4>输出内容预览（前200字符）:</h4>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
            echo htmlspecialchars(substr($output, 0, 200));
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 包含 pages/post.php 异常: " . $e->getMessage() . "</p>";
    } catch (Error $e) {
        echo "<p style='color: red;'>❌ 包含 pages/post.php 错误: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ pages/post.php 文件不存在</p>";
}

// 测试直接访问链接
echo "<h3>10. 测试链接</h3>";
echo "<p><a href='/app.php?m=post&action=edit&id=$test_id' target='_blank'>直接访问编辑页面</a></p>";
echo "<p><a href='/pages/post.php?action=edit&id=$test_id' target='_blank'>直接访问 pages/post.php</a></p>";

// 检查错误日志
echo "<h3>11. 错误日志检查</h3>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<p>错误日志位置: $error_log</p>";
    $log_content = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $log_content), -10);
    echo "<h4>最近的错误日志:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
    foreach ($recent_errors as $error) {
        if (!empty(trim($error))) {
            echo htmlspecialchars($error) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>未找到错误日志文件</p>";
}

echo "<p style='margin-top: 30px;'><a href='/'>返回首页</a></p>";
?>
