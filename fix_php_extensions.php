<?php
/**
 * PHP扩展修复脚本
 * 用于修复mysqli、pdo_mysql、gd等扩展加载问题
 */

echo "<h1>PHP扩展修复脚本</h1>";

// 获取PHP配置信息
$php_ini_path = php_ini_loaded_file();
$php_version = PHP_VERSION;
$php_sapi = php_sapi_name();

echo "<h2>当前PHP配置信息</h2>";
echo "<p>PHP版本: {$php_version}</p>";
echo "<p>SAPI: {$php_sapi}</p>";
echo "<p>配置文件路径: {$php_ini_path}</p>";

// 检查扩展状态
echo "<h2>扩展加载状态</h2>";
$extensions = [
    'mysqli' => 'MySQL改进扩展',
    'pdo' => 'PDO数据库抽象层',
    'pdo_mysql' => 'PDO MySQL驱动',
    'gd' => 'GD图像处理库',
    'json' => 'JSON处理',
    'mbstring' => '多字节字符串',
    'openssl' => 'OpenSSL加密',
    'curl' => 'cURL网络库'
];

$loaded_extensions = [];
$missing_extensions = [];

foreach ($extensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        $loaded_extensions[] = $ext;
        echo "<p style='color: green'>✓ {$ext} ({$desc}) - 已加载</p>";
    } else {
        $missing_extensions[] = $ext;
        echo "<p style='color: red'>✗ {$ext} ({$desc}) - 未加载</p>";
    }
}

// 如果有缺失的扩展，提供解决方案
if (!empty($missing_extensions)) {
    echo "<h2>解决方案</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3>PHPStudy用户解决步骤：</h3>";
    echo "<ol>";
    echo "<li><strong>打开PHPStudy控制面板</strong></li>";
    echo "<li><strong>点击'软件管理' -> 'PHP扩展'</strong></li>";
    echo "<li><strong>启用以下扩展：</strong>";
    echo "<ul>";
    foreach ($missing_extensions as $ext) {
        echo "<li>{$ext}</li>";
    }
    echo "</ul></li>";
    echo "<li><strong>重启Apache/Nginx服务</strong></li>";
    echo "</ol>";
    
    echo "<h3>手动修复方法：</h3>";
    echo "<ol>";
    echo "<li><strong>找到php.ini文件位置：</strong> {$php_ini_path}</li>";
    echo "<li><strong>编辑php.ini文件，取消以下行的注释（删除前面的分号）：</strong>";
    echo "<ul>";
    foreach ($missing_extensions as $ext) {
        echo "<li>extension={$ext}</li>";
    }
    echo "</ul></li>";
    echo "<li><strong>保存文件并重启Web服务器</strong></li>";
    echo "</ol>";
    echo "</div>";
}

// 测试数据库连接
if (extension_loaded('mysqli')) {
    echo "<h2>数据库连接测试</h2>";
    
    // 尝试读取数据库配置
    $config_file = dirname(__FILE__) . '/config/config.db.php';
    if (file_exists($config_file)) {
        include $config_file;
        
        try {
            $mysqli = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name'], $config['db_port']);
            
            if ($mysqli->connect_error) {
                echo "<p style='color: red'>✗ 数据库连接失败: " . $mysqli->connect_error . "</p>";
                
                // 提供数据库连接问题的解决方案
                echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
                echo "<h3>数据库连接问题解决方案：</h3>";
                echo "<ol>";
                echo "<li><strong>检查MySQL服务是否启动</strong></li>";
                echo "<li><strong>验证数据库配置信息：</strong>";
                echo "<ul>";
                echo "<li>主机: {$config['db_host']}</li>";
                echo "<li>用户名: {$config['db_user']}</li>";
                echo "<li>数据库名: {$config['db_name']}</li>";
                echo "<li>端口: {$config['db_port']}</li>";
                echo "</ul></li>";
                echo "<li><strong>确保数据库用户有足够权限</strong></li>";
                echo "<li><strong>检查防火墙设置</strong></li>";
                echo "</ol>";
                echo "</div>";
            } else {
                echo "<p style='color: green'>✓ 数据库连接成功</p>";
                echo "<p>MySQL版本: " . $mysqli->server_info . "</p>";
                
                // 检查关键表
                $tables = ['posts', 'post_contents', 'categories', 'regions'];
                echo "<h3>数据表检查：</h3>";
                foreach ($tables as $table) {
                    $result = $mysqli->query("SHOW TABLES LIKE '{$table}'");
                    if ($result && $result->num_rows > 0) {
                        echo "<p style='color: green'>✓ 表 {$table} 存在</p>";
                    } else {
                        echo "<p style='color: red'>✗ 表 {$table} 不存在</p>";
                    }
                }
            }
            $mysqli->close();
        } catch (Exception $e) {
            echo "<p style='color: red'>✗ 数据库连接异常: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red'>✗ 数据库配置文件不存在: {$config_file}</p>";
    }
} else {
    echo "<h2>数据库连接测试</h2>";
    echo "<p style='color: red'>✗ 无法测试数据库连接，mysqli扩展未加载</p>";
}

// 生成修复报告
echo "<h2>修复状态总结</h2>";
if (empty($missing_extensions)) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: green'>✓ 所有必需的PHP扩展都已正确加载</h3>";
    echo "<p>您的PHP环境配置正常，发布功能应该可以正常工作。</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: red'>⚠ 发现问题需要修复</h3>";
    echo "<p>缺失的扩展: " . implode(', ', $missing_extensions) . "</p>";
    echo "<p>请按照上述解决方案修复这些问题。</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>修复完成后，请删除此测试文件以确保安全。</small></p>";
?>
