<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<!-- 页面标题 -->
    <div class="page-title">
        <h1><i class="fas fa-cube"></i> 内容块管理</h1>
        <?php if($action == 'list'): ?>
        <div class="page-actions">
            <button class="btn btn-primary" onclick="location.href='content_blocks.php?action=add'">
                <i class="fas fa-plus"></i> 添加内容块
            </button>
            <button class="btn btn-outline" onclick="if(confirm('确定要清除所有块缓存吗？')) location.href='content_blocks.php?action=clear_cache'">
                <i class="fas fa-trash"></i> 清除缓存
            </button>
            <button class="btn btn-outline" onclick="location.href='content_blocks.php?action=warmup_cache'">
                <i class="fas fa-sync"></i> 预热缓存
            </button>
        </div>
        <?php endif; ?>
    </div>
    <?php if($message): ?>
    <div class="section">
        <div class="alert alert-<?php if($message_type == 'success'): ?>success<?php elseif($message_type == 'error'): ?>danger<?php else: ?>info<?php endif; ?>">
            <i class="fas fa-<?php if($message_type == 'success'): ?>check-circle<?php elseif($message_type == 'error'): ?>exclamation-circle<?php else: ?>info-circle<?php endif; ?>"></i>
            <div><?php echo $message ?? ""; ?></div>
        </div>
    </div>
    <?php endif; ?>

    <?php if($action == 'list'): ?>
    <!-- 列表页面 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">内容块列表 <span class="record-count">(共 <?php if($blocks): ?><?php echo null !== ($blocks ?? null) ? count($blocks ?? null) : ""; ?><?php else: ?>0<?php endif; ?> 条记录)</span></h3>
                <div class="card-actions">
                    <?php if($cache_stats): ?>
                    <span class="tag tag-success">缓存文件: <?php echo (isset($cache_stats['total_files'])) ? $cache_stats['total_files'] : ""; ?></span>
                    <span class="tag tag-warning">缓存大小: <?php echo (isset($cache_stats['total_size_formatted'])) ? $cache_stats['total_size_formatted'] : ""; ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <?php if($blocks): ?>
            <div class="table-responsive">
                <table class="table table-enhanced">
                    <thead>
                        <tr>
                            <th width="60" class="text-center">ID</th>
                            <th>块名称</th>
                            <th width="150">调用标识符</th>
                            <th>描述</th>
                            <th width="80" class="text-center">状态</th>
                            <th width="80" class="text-center">排序</th>
                            <th width="140" class="text-center">创建时间</th>
                            <th width="200" class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(null !== ($blocks ?? null) && is_array($blocks)): foreach($blocks as $block): ?>
                        <tr class="table-row">
                            <td class="text-center table-id"><?php echo (isset($block['id'])) ? $block['id'] : ""; ?></td>
                            <td>
                                <div class="table-title">
                                    <span class="title-link"><?php echo (isset($block['name'])) ? $block['name'] : ""; ?></span>
                                </div>
                            </td>
                            <td>
                                <div class="copy-code-wrapper">
                                    <div class="code-display" onclick="copyToClipboard(this)" title="点击复制标签">
                                        <code class="copy-target">{literal}{block:<?php echo (isset($block['identifier'])) ? $block['identifier'] : ""; ?>}{/literal}</code>
                                        <i class="fas fa-copy copy-icon"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="table-subtitle"><?php if($block['description']): ?><?php echo (isset($block['description'])) ? $block['description'] : ""; ?><?php else: ?>-<?php endif; ?></div>
                            </td>
                            <td class="text-center">
                                <?php if($block['status']): ?>
                                <span class="status-tag success">启用</span>
                                <?php else: ?>
                                <span class="status-tag danger">禁用</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center table-number"><?php echo (isset($block['sort_order'])) ? $block['sort_order'] : ""; ?></td>
                            <td class="text-center table-time"><?php echo (isset($block['created_date'])) ? $block['created_date'] : ""; ?> <small><?php echo (isset($block['created_time'])) ? $block['created_time'] : ""; ?></small></td>
                            <td>
                                <div class="action-buttons-enhanced">
                                    <a href="content_blocks.php?action=edit&id=<?php echo (isset($block['id'])) ? $block['id'] : ""; ?>" class="btn-action btn-edit">
                                        <i class="fas fa-edit"></i>
                                        <span>编辑</span>
                                    </a>
                                    <a href="content_blocks.php?action=toggle_status&id=<?php echo (isset($block['id'])) ? $block['id'] : ""; ?>"
                                       class="btn-action btn-<?php if($block['status']): ?>warning<?php else: ?>approve<?php endif; ?>"
                                       onclick="return confirm('确定要<?php if($block['status']): ?>禁用<?php else: ?>启用<?php endif; ?>这个内容块吗？')">
                                        <i class="fas fa-<?php if($block['status']): ?>pause<?php else: ?>play<?php endif; ?>"></i>
                                        <span><?php if($block['status']): ?>禁用<?php else: ?>启用<?php endif; ?></span>
                                    </a>
                                    <a href="content_blocks.php?action=delete&id=<?php echo (isset($block['id'])) ? $block['id'] : ""; ?>"
                                       class="btn-action btn-delete btn-confirm"
                                       onclick="return confirm('确定要删除这个内容块吗？删除后无法恢复！')">
                                        <i class="fas fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; endif; ?>
                    </tbody>
                    </table>
            </div>
            <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-cube"></i>
                <h3>暂无内容块</h3>
                <p>还没有创建任何内容块，点击上方按钮开始添加。</p>
                <button class="btn btn-primary" onclick="location.href='content_blocks.php?action=add'">
                    <i class="fas fa-plus"></i> 添加第一个内容块
                </button>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if($blocks): ?>
    <!-- 使用说明 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">使用说明</h3>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <div class="quick-action-item">
                        <div class="quick-action-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4>模板中调用</h4>
                            <p>在模板文件中使用标签：<br><code>{block:标识符}</code></p>
                        </div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fas fa-terminal"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4>PHP中调用</h4>
                            <p>在PHP代码中使用函数：<br><code>echo get_block('标识符');</code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php elseif($action == 'add' || $action == 'edit'): ?>
    <!-- 添加/编辑页面 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title"><?php if($action == 'add'): ?>添加内容块<?php else: ?>编辑内容块<?php endif; ?></h3>
            </div>
            <div class="card-body">
                <form method="post" action="content_blocks.php" class="form-horizontal" data-validate>
                    <input type="hidden" name="action" value="<?php echo $action ?? ""; ?>">
                    <?php if($action == 'edit'): ?>
                    <input type="hidden" name="id" value="<?php echo $id ?? ""; ?>">
                    <?php endif; ?>

                    <div class="form-group">
                        <label class="form-label">块名称 *</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="name"
                                   value="<?php if($block_data && $block_data['name']): ?><?php echo (isset($block_data['name'])) ? $block_data['name'] : ""; ?><?php endif; ?>" required placeholder="请输入块名称">
                        </div>
                        <div class="form-help">
                            <span class="help-text">给内容块起一个容易识别的名称</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">调用标识符 *</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="identifier"
                                   value="<?php if($block_data && $block_data['identifier']): ?><?php echo (isset($block_data['identifier'])) ? $block_data['identifier'] : ""; ?><?php endif; ?>" required
                                   pattern="[a-zA-Z0-9_-]+" placeholder="例如：footer_nav">
                        </div>
                        <div class="form-help">
                            <span class="help-text">用于调用的唯一标识符，只能包含字母、数字、下划线和短横线</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">描述说明</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="description"
                                   value="<?php if($block_data && $block_data['description']): ?><?php echo (isset($block_data['description'])) ? $block_data['description'] : ""; ?><?php endif; ?>" placeholder="简单描述这个内容块的用途">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">HTML内容 *</label>
                        <div class="form-field">
                            <textarea class="form-textarea" name="content" rows="10" required placeholder="请输入HTML代码"><?php if($block_data && $block_data['content']): ?><?php echo (isset($block_data['content'])) ? $block_data['content'] : ""; ?><?php endif; ?></textarea>
                        </div>
                        <div class="form-help">
                            <span class="help-text">输入HTML代码，支持所有HTML标签</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <div class="form-field">
                            <label class="form-check">
                                <input type="radio" name="status" value="1" <?php if(!$block_data || !null !== ($block_data ?? null) && is_array($block_data) && array_key_exists('status', $block_data) || $block_data['status'] == 1): ?>checked<?php endif; ?>>
                                <span class="form-check-label">启用</span>
                            </label>
                            <label class="form-check">
                                <input type="radio" name="status" value="0" <?php if($block_data && null !== ($block_data ?? null) && is_array($block_data) && array_key_exists('status', $block_data) && $block_data['status'] == 0): ?>checked<?php endif; ?>>
                                <span class="form-check-label">禁用</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <div class="form-field">
                            <input type="number" class="form-control" name="sort_order"
                                   value="<?php if($block_data && null !== ($block_data ?? null) && is_array($block_data) && array_key_exists('sort_order', $block_data)): ?><?php echo (isset($block_data['sort_order'])) ? $block_data['sort_order'] : ""; ?><?php else: ?>0<?php endif; ?>" min="0" placeholder="0">
                        </div>
                        <div class="form-help">
                            <span class="help-text">数字越小排序越靠前</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> <?php if($action == 'add'): ?>添加<?php else: ?>更新<?php endif; ?>
                        </button>
                        <button type="button" class="btn btn-outline" onclick="location.href='content_blocks.php'">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

<style>
.copy-code-wrapper {
    width: 100%;
}

.code-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 180px;
    max-width: 220px;
}

.code-display:hover {
    background: #e9ecef;
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.code-display code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
    background: none;
    padding: 0;
    border: none;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.copy-icon {
    color: #6c757d;
    font-size: 12px;
    margin-left: 8px;
    transition: color 0.2s;
}

.code-display:hover .copy-icon {
    color: #007bff;
}

.code-display.copied {
    background: #d4edda;
    border-color: #28a745;
}

.code-display.copied .copy-icon {
    color: #28a745;
}

.code-display.copied .copy-icon:before {
    content: "\f00c"; /* fa-check */
}

/* 工具提示 */
.code-display[title]:hover:after {
    content: attr(title);
    position: absolute;
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-top: 25px;
    margin-left: -20px;
}
</style>

<script>
function copyToClipboard(element) {
    // 如果点击的是按钮，获取父元素；如果是代码区域，直接使用
    const codeDisplay = element.classList.contains('code-display') ? element : element.closest('.code-display');
    const codeElement = codeDisplay.querySelector('.copy-target');
    const text = codeElement.textContent.trim();

    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess(codeDisplay);
        }).catch(function(err) {
            // 如果失败，使用传统方法
            fallbackCopyTextToClipboard(text, codeDisplay);
        });
    } else {
        // 使用传统方法
        fallbackCopyTextToClipboard(text, codeDisplay);
    }
}

function fallbackCopyTextToClipboard(text, codeDisplay) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // 避免滚动到底部
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(codeDisplay);
        } else {
            showCopyError(codeDisplay);
        }
    } catch (err) {
        showCopyError(codeDisplay);
    }

    document.body.removeChild(textArea);
}

function showCopySuccess(codeDisplay) {
    const originalTitle = codeDisplay.title;
    codeDisplay.classList.add('copied');
    codeDisplay.title = '已复制到剪贴板!';

    // 显示复制成功的提示
    const toast = document.createElement('div');
    toast.textContent = '已复制: ' + codeDisplay.querySelector('.copy-target').textContent;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;
    document.body.appendChild(toast);

    setTimeout(function() {
        codeDisplay.classList.remove('copied');
        codeDisplay.title = originalTitle;
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 2000);
}

function showCopyError(codeDisplay) {
    const originalTitle = codeDisplay.title;
    codeDisplay.style.background = '#f8d7da';
    codeDisplay.style.borderColor = '#dc3545';
    codeDisplay.title = '复制失败，请手动复制';

    setTimeout(function() {
        codeDisplay.style.background = '';
        codeDisplay.style.borderColor = '';
        codeDisplay.title = originalTitle;
    }, 2000);
}
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
