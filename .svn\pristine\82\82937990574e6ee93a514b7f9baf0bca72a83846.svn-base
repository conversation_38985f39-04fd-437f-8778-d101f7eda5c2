{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>
        <i class="fas fa-cog"></i>
        系统设置
    </h1>
    <div class="page-actions">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回首页</span>
        </a>
    </div>
</div>

<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    {if !empty($message)}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <strong>操作成功</strong>
            <p>{$message}</p>
        </div>
    </div>
    {/if}


    <!-- 设置选项卡 -->
    <div class="card">
        <div class="setting-tabs">
            {foreach $groups as $group_key => $group_name}
            <a href="?group={$group_key}" class="setting-tab{if $current_group == $group_key} active{/if}">
                {$group_name}
            </a>
            {/foreach}
        </div>

        <!-- 设置表单 -->
        <form method="post" action="" class="form-horizontal">
            <input type="hidden" name="action" value="save_settings">
            <input type="hidden" name="group" value="{$current_group}">

            {if $settings}
            {foreach $settings as $setting}
            <div class="form-group">
                <label class="form-label" for="{$setting.setting_key}">
                    {$setting.setting_title}
                    {if !empty($setting.setting_description)}
                    <i class="fas fa-question-circle help-icon" title="{$setting.setting_description}"></i>
                    {/if}
                </label>

                <div class="form-field">
                    {if $setting.setting_type == 'text'}
                    <input type="text"
                           id="{$setting.setting_key}"
                           name="{$setting.setting_key}"
                           value="{$setting.setting_value|escape}"
                           class="form-control"
                           placeholder="请输入{$setting.setting_title}">

                    {elseif $setting.setting_type == 'number'}
                    <input type="number"
                           id="{$setting.setting_key}"
                           name="{$setting.setting_key}"
                           value="{$setting.setting_value}"
                           class="form-control"
                           min="0"
                           step="1"
                           placeholder="请输入数字">

                    {elseif $setting.setting_type == 'textarea'}
                    <textarea id="{$setting.setting_key}"
                              name="{$setting.setting_key}"
                              class="form-textarea"
                              rows="4"
                              placeholder="请输入{$setting.setting_title}">{$setting.setting_value|escape}</textarea>

                    {elseif $setting.setting_type == 'radio' || $setting.setting_type == 'switch'}
                    <div class="form-radio-group">
                        <div class="form-check">
                            <input type="radio"
                                   name="{$setting.setting_key}"
                                   id="{$setting.setting_key}_1"
                                   value="1"
                                   {if $setting.setting_value == '1'}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_1">
                                <i class="fas fa-check-circle"></i>
                                启用
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="radio"
                                   name="{$setting.setting_key}"
                                   id="{$setting.setting_key}_0"
                                   value="0"
                                   {if $setting.setting_value == '0'}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_0">
                                <i class="fas fa-times-circle"></i>
                                禁用
                            </label>
                        </div>
                    </div>

                    {elseif $setting.setting_type == 'select'}
                    <select id="{$setting.setting_key}" name="{$setting.setting_key}" class="form-select">
                        {if $setting.setting_key == 'mobile_pagination_mode'}
                        <option value="pagination" {if $setting.setting_value == 'pagination'}selected{/if}>传统分页模式（上一页/下一页）</option>
                        <option value="loadmore" {if $setting.setting_value == 'loadmore'}selected{/if}>点击加载更多模式</option>
                        <option value="infinite" {if $setting.setting_value == 'infinite'}selected{/if}>滚动无限加载模式</option>
                        {else}
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {if $options}
                        {foreach $options as $option_key => $option_value}
                        <option value="{$option_key}" {if $setting.setting_value == $option_key}selected{/if}>{$option_value}</option>
                        {/foreach}
                        {/if}
                        {/if}
                    </select>

                    {elseif $setting.setting_type == 'checkbox'}
                    <div class="form-checkbox-group">
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {assign var=selected_values value=explode(',', $setting.setting_value)}
                        {if $options}
                        {foreach $options as $option_key => $option_value}
                        <div class="form-check">
                            <input type="checkbox"
                                   name="{$setting.setting_key}[]"
                                   id="{$setting.setting_key}_{$option_key}"
                                   value="{$option_key}"
                                   {if in_array($option_key, $selected_values)}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_{$option_key}">
                                {$option_value}
                            </label>
                        </div>
                        {/foreach}
                        {/if}
                    </div>
                    {/if}
                </div>

                {if !empty($setting.setting_description)}
                <div class="form-help">
                    <i class="fas fa-info-circle"></i>
                    <span class="help-text">{$setting.setting_description}</span>
                </div>
                {/if}
            </div>
            {/foreach}
            {else}
            <div class="empty-state">
                <i class="fas fa-cog"></i>
                <h3>暂无设置项</h3>
                <p>当前分组下没有可配置的设置项</p>
            </div>
            {/if}

            <!-- 提交按钮 -->
            {if $settings}
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    <span>保存设置</span>
                </button>
                <button type="reset" class="btn btn-outline">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
                <a href="?group={$current_group}" class="btn btn-outline">
                    <i class="fas fa-refresh"></i>
                    <span>刷新</span>
                </a>
            </div>
            {/if}
        </form>
    </div>
</div>

<!-- 页面脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const helpIcons = document.querySelectorAll('.help-icon');
    helpIcons.forEach(function(icon) {
        icon.addEventListener('mouseenter', function() {
            // 可以在这里添加更详细的工具提示逻辑
        });
    });

    // 表单验证
    const form = document.querySelector('.form-horizontal');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('请填写所有必填字段');
            }
        });
    }

    // 数字输入验证
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });
});
</script>

{include file="footer.htm"}