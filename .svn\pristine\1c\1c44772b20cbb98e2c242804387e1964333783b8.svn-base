<!DOCTYPE html>
<?php 
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
 ?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>信息管理 - <?php echo $site_name ?? ""; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <style>
        /* 确保简约主题头部为白色背景 */
        .theme-simple header {
            background-color: #ffffff !important;
        }
        
        body {
            background-color: #f5f5f5;
            padding-bottom: 20px;
            margin: 0;
        }
        .post-info {
            background-color: white;
            padding: 15px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .post-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .post-meta {
            display: flex;
            flex-direction: column;
            font-size: 13px;
            color: #666;
            gap: 8px;
        }
        .post-meta-item {
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .post-meta-item:last-child {
            border-bottom: none;
        }
        .meta-label {
            font-weight: 500;
            display: inline-block;
            width: 80px;
            color: #333;
        }
        
        .form-section {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
        }
        .form-control {
            display: block;
            width: 100%;
            height: 40px;
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            margin-bottom: 15px;
        }
        
        .hint-box {
            background-color: #fff8e1;
            border-left: 3px solid #ffb300;
            padding: 10px;
            margin: 10px 15px;
            font-size: 13px;
            color: #666;
            border-radius: 4px;
        }
        .hint-box p {
            margin: 0 0 5px 0;
        }
        
        /* 扁平化操作按钮 */
        .action-btns {
            display: flex;
            justify-content: space-between;
            padding: 0 15px;
            margin-top: 15px;
            gap: 10px;
        }
        .action-btn {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
        }
        .btn-edit {
            background-color: var(--primary-color);
        }
        .btn-refresh {
            background-color: #3498db;
        }
        .btn-delete {
            background-color: #e74c3c;
        }
        
        /* 底部导航 */
        .footer-nav {
            background: white;
            padding: 15px;
            margin-top: 10px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .back-link {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">信息管理</div>
            <div class="header-right"></div>
        </div>
    </header>

    <div class="post-info">
        <div class="post-title"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></div>
        <div class="post-meta">
            <span class="post-meta-item"><span class="meta-label">分类：</span><?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?></span>
            <span class="post-meta-item"><span class="meta-label">发布时间：</span><?php echo friendlyTime($post['created_at']); ?></span>
            <span class="post-meta-item"><span class="meta-label">联系人：</span><?php echo (isset($post['contact_name'])) ? $post['contact_name'] : ""; ?></span>
            <span class="post-meta-item"><span class="meta-label">联系电话：</span><?php echo (isset($post['contact_mobile'])) ? $post['contact_mobile'] : ""; ?></span>
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_weixin', $post) && !empty($post['contact_weixin'])): ?>
            <span class="post-meta-item"><span class="meta-label">微信号：</span><?php echo (isset($post['contact_weixin'])) ? $post['contact_weixin'] : ""; ?></span>
            <?php endif; ?>
        </div>
    </div>

    <?php if(null !== ($verified ?? null) && $verified === true): ?>
    <div class="hint-box" style="background-color: #e8f5e9; border-left-color: #4caf50;">
        <p>身份已验证，您可以管理此信息</p>
    </div>
    
    <!-- 已验证，显示操作按钮 -->
    <div class="action-btns">
        <a href="/app.php?m=post&action=edit&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" class="action-btn btn-edit">
            修改
        </a>
        <a href="javascript:void(0)" onclick="refreshPost(<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>)" class="action-btn btn-refresh">
            刷新
        </a>
        <a href="javascript:void(0)" onclick="deletePost(<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>)" class="action-btn btn-delete">
            删除
        </a>
    </div>
    <?php else: ?>
    <div class="hint-box">
        <p>请输入管理密码完成身份验证</p>
    </div>
    
    <!-- 未验证，显示密码输入框 -->
    <form action="/app.php?m=manage" method="post">
        <input type="hidden" name="id" value="<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>">
        
        <div class="form-section">
            <input type="password" name="password" id="password" class="form-control" placeholder="请输入管理密码" required>
        </div>
        
        <div class="action-btns">
            <button type="submit" name="action" value="修改" class="action-btn btn-edit">
                修改
            </button>
            <button type="submit" name="action" value="刷新" class="action-btn btn-refresh">
                刷新
            </button>
            <button type="submit" name="action" value="删除" class="action-btn btn-delete" onclick="return confirm('确定要删除此信息吗？删除后无法恢复！')">
                删除
            </button>
        </div>
    </form>
    <?php endif; ?>
    
    <div class="footer-nav">
        <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="back-link">
            <i class="fas fa-arrow-left"></i> 返回信息详情
        </a>
    </div>

<script>
// 刷新信息
function refreshPost(postId) {
    if (!confirm('确定要刷新此信息吗？刷新后信息将重新排序到最前面。')) {
        return;
    }

    // 显示加载状态
    showLoading('正在刷新信息...');

    fetch(`/app.php?m=manage&id=${postId}&action=refresh&ajax=1`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('信息刷新成功！', 'success');
            // 3秒后跳转到信息详情页
            setTimeout(() => {
                if (data.redirect) {
                    window.location.href = data.redirect;
                } else {
                    window.location.reload();
                }
            }, 3000);
        } else {
            showMessage(data.message || '刷新失败，请稍后重试', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('刷新请求失败:', error);
        showMessage('网络请求失败，请稍后重试', 'error');
    });
}

// 删除信息
function deletePost(postId) {
    if (!confirm('确定要删除此信息吗？删除后无法恢复！')) {
        return;
    }

    // 显示加载状态
    showLoading('正在删除信息...');

    fetch(`/app.php?m=manage&id=${postId}&action=delete&ajax=1`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showMessage('信息删除成功！', 'success');
            // 3秒后跳转到首页
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        } else {
            showMessage(data.message || '删除失败，请稍后重试', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('删除请求失败:', error);
        showMessage('网络请求失败，请稍后重试', 'error');
    });
}

// 显示加载状态
function showLoading(message) {
    const loading = document.createElement('div');
    loading.id = 'loading-overlay';
    loading.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        ">
            <div style="
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            ">
                <div style="
                    width: 20px;
                    height: 20px;
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #007bff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                "></div>
                <div style="color: #333; font-size: 14px;">${message}</div>
            </div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
    document.body.appendChild(loading);
}

// 隐藏加载状态
function hideLoading() {
    const loading = document.getElementById('loading-overlay');
    if (loading) {
        loading.remove();
    }
}

// 显示消息
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            max-width: 90%;
            text-align: center;
        ">
            ${message}
        </div>
    `;
    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>

</body>
</html>