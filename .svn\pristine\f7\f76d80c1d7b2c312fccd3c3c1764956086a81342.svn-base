<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1755595844,
  'data' => 
  array (
    'post' => 
    array (
      'id' => 110653,
      'user_id' => 0,
      'category_id' => 6,
      'region_id' => 43,
      'expire_days' => 7,
      'title' => '二手洗衣机配送73元',
      'is_top_home' => 0,
      'is_top_category' => 0,
      'is_top_subcategory' => 0,
      'top_home_expire' => NULL,
      'top_category_expire' => NULL,
      'top_subcategory_expire' => NULL,
      'view_count' => 1,
      'image_count' => 3,
      'status' => 1,
      'created_at' => 1746494562,
      'updated_at' => 1746494562,
      'expired_at' => 1747099362,
      'is_expired' => 0,
      'category_name' => '合租',
      'category_pinyin' => 'hezu',
      'parent_category_name' => '房产信息',
      'parent_category_pinyin' => 'fangchan',
      'parent_category_id' => 1,
      'region_name' => NULL,
      'content' => '保证正品，假一赔十，支持验货后付款，让您买得放心。

本人急需用钱，所以价格比市场便宜很多，先到先得。

这是一个测试数据，自动生成的内容，仅用于测试显示效果。

有任何问题可以电话或微信联系，随时恭候您的来电咨询。',
      'fields_data' => '',
      'ip' => '***************',
      'password' => 'e10adc3949ba59abbe56e057f20f883e',
      'contact_name' => '孙磊',
      'contact_mobile' => '18124020571',
      'contact_weixin' => '18124020571',
      'contact_address' => '深圳市中心区长江路590号',
      'fields' => 
      array (
      ),
      'mobile' => '18124020571',
      'wechat' => '18124020571',
    ),
    'images' => 
    array (
      0 => 
      array (
        'id' => 166014,
        'post_id' => 110653,
        'type' => 'post',
        'file_path' => 'uploads/images/202505/6819646367ef0.jpg',
        'thumb_path' => 'uploads/thumbs/202505/6819646367ef0.jpg',
        'file_size' => 60,
        'file_type' => 'image/jpeg',
        'sort_order' => 0,
        'created_at' => 1746494562,
      ),
    ),
    'related_posts' => 
    array (
      0 => 
      array (
        'id' => 110498,
        'title' => '正品沙发出售14元',
        'created_at' => 1746494562,
        'updated_at' => 1755593998,
        'detail_url' => 'http://localhost/hezu/110498.html',
      ),
      1 => 
      array (
        'id' => 110374,
        'title' => '高档车辆处理35元',
        'created_at' => 1746494562,
        'updated_at' => 1755593644,
        'detail_url' => 'http://localhost/hezu/110374.html',
      ),
      2 => 
      array (
        'id' => 110061,
        'title' => '全新餐桌配送69元',
        'created_at' => 1746494562,
        'updated_at' => 1753579712,
        'detail_url' => 'http://localhost/hezu/110061.html',
      ),
      3 => 
      array (
        'id' => 110212,
        'title' => '正品电视置换23元',
        'created_at' => 1746494562,
        'updated_at' => 1753579637,
        'detail_url' => 'http://localhost/hezu/110212.html',
      ),
      4 => 
      array (
        'id' => 110969,
        'title' => '二手床垫出售37元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110969.html',
      ),
      5 => 
      array (
        'id' => 110963,
        'title' => '优质床垫维修94元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110963.html',
      ),
      6 => 
      array (
        'id' => 110952,
        'title' => '全新电视转让81元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110952.html',
      ),
      7 => 
      array (
        'id' => 110902,
        'title' => '急售沙发出租13元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110902.html',
      ),
      8 => 
      array (
        'id' => 110846,
        'title' => '实惠沙发转让92元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110846.html',
      ),
      9 => 
      array (
        'id' => 110801,
        'title' => '急售家具求购40元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://localhost/hezu/110801.html',
      ),
    ),
    'category_info' => 
    array (
      'id' => '6',
      'parent_id' => '1',
      'name' => '合租',
      'icon' => 'share.png',
      'sort_order' => '2',
      'status' => '1',
      'pinyin' => 'hezu',
      'seo_title' => '',
      'seo_keywords' => '',
      'seo_description' => '',
      'template' => 'category_fang.htm',
      'detail_template' => 'view_fang.htm',
      'subcategory_ids' => '',
    ),
    'cached_at' => 1755594044,
  ),
);
