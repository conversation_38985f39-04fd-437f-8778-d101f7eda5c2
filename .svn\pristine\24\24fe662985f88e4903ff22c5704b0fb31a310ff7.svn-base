<?php
if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

/**
 * 信息发布页面
 * 使用case结构处理不同功能：
 * 1. 栏目选择
 * 2. 信息发布
 * 3. 信息编辑
 */

// 清理输出缓冲区，确保没有额外输出
if (ob_get_level()) {
    ob_clean();
}

// 检测是否为AJAX请求
$is_ajax_request = (isset($_GET['ajax']) && $_GET['ajax'] == '1') ||
                   (isset($_POST['ajax']) && $_POST['ajax'] == '1') ||
                   (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');

// 如果是AJAX请求，禁用错误显示并设置JSON头信息
if ($is_ajax_request) {
    ini_set('display_errors', 0);
    error_reporting(E_ERROR | E_PARSE); // 只报告致命错误
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
}

// 引入操作日志类（如果还未引入）
if (!class_exists('OperationLogger')) {
    require_once './include/OperationLogger.class.php';
}

// 从命令行参数中解析参数 (命令行模式下 $_GET 不会自动填充)
if (PHP_SAPI === 'cli' && isset($argv) && count($argv) > 1) {
    // 优化命令行参数解析
    foreach (array_slice($argv, 1) as $arg) {
        if (strpos($arg, '=') !== false) {
            list($key, $value) = explode('=', $arg, 2);
            $_GET[$key] = $value;
        }
    }
    
    // 处理合并参数
    foreach ($_GET as $key => $value) {
        if (strpos($value, '&') !== false) {
            parse_str($value, $parsed);
            $_GET = array_merge($_GET, $parsed);
        }
    }
}

/**
 * 检查手机号每日发布限制（安全版本 - 防止时间篡改）
 * @param string $mobile 手机号码
 * @return array 返回检查结果 ['allowed' => bool, 'current_count' => int, 'limit' => int, 'message' => string]
 */
function checkMobileDailyLimit($mobile) {
    global $db, $config;

    // 获取配置的每日限制数量
    $dailyLimit = isset($config['mobile_daily_post_limit']) ? intval($config['mobile_daily_post_limit']) : 10;

    // 使用数据库服务器时间，防止客户端时间篡改
    $sql = "SELECT NOW() as server_time, UNIX_TIMESTAMP(NOW()) as server_timestamp";
    $result = $db->query($sql);
    $timeRow = $db->fetch_array($result);
    $serverTimestamp = intval($timeRow['server_timestamp']);
    $serverDate = date('Y-m-d', $serverTimestamp);

    // 方案1：24小时滑动窗口限制（更安全）
    $sql = "SELECT COUNT(*) as count FROM mobile_daily_stats
            WHERE mobile = ? AND created_at > ?";
    $result = $db->query($sql, [$mobile, $serverTimestamp - 86400]); // 86400秒 = 24小时
    $row = $db->fetch_array($result);
    $slidingWindowCount = $row ? intval($row['count']) : 0;

    // 方案2：当日发布数量（基于服务器日期）
    $sql = "SELECT post_count FROM mobile_daily_stats WHERE mobile = ? AND date = ?";
    $result = $db->query($sql, [$mobile, $serverDate]);
    $row = $db->fetch_array($result);
    $dailyCount = $row ? intval($row['post_count']) : 0;

    // 使用更严格的限制（两种方案取最大值）
    $currentCount = max($slidingWindowCount, $dailyCount);

    // 额外安全检查：检测时间异常
    $sql = "SELECT MAX(created_at) as last_post_time FROM mobile_daily_stats WHERE mobile = ?";
    $result = $db->query($sql, [$mobile]);
    $row = $db->fetch_array($result);
    $lastPostTime = $row ? intval($row['last_post_time']) : 0;

    // 如果检测到时间倒退（可能的时间篡改），使用更严格的限制
    if ($lastPostTime > $serverTimestamp + 300) { // 允许5分钟的时间误差
        return [
            'allowed' => false,
            'current_count' => $currentCount,
            'limit' => $dailyLimit,
            'message' => "系统检测到时间异常，请稍后再试或联系管理员"
        ];
    }

    if ($currentCount >= $dailyLimit) {
        return [
            'allowed' => false,
            'current_count' => $currentCount,
            'limit' => $dailyLimit,
            'message' => "您在24小时内已发布{$currentCount}条信息，已达到每日限制{$dailyLimit}条，请稍后再试"
        ];
    }

    return [
        'allowed' => true,
        'current_count' => $currentCount,
        'limit' => $dailyLimit,
        'message' => ''
    ];
}

/**
 * 更新手机号每日发布统计（安全版本 - 防止时间篡改）
 * @param string $mobile 手机号码
 * @return bool 更新是否成功
 */
function updateMobileDailyStats($mobile) {
    global $db;

    // 使用数据库服务器时间，确保时间准确性
    $sql = "SELECT NOW() as server_time, UNIX_TIMESTAMP(NOW()) as server_timestamp";
    $result = $db->query($sql);
    $timeRow = $db->fetch_array($result);
    $serverTimestamp = intval($timeRow['server_timestamp']);
    $serverDate = date('Y-m-d', $serverTimestamp);

    // 开始事务
    $db->beginTransaction();

    try {
        // 1. 更新每日统计表（基于服务器日期）
        $sql = "INSERT INTO mobile_daily_stats (mobile, date, post_count, created_at, updated_at, server_timestamp)
                VALUES (?, ?, 1, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                post_count = post_count + 1,
                updated_at = ?,
                server_timestamp = ?";

        $result1 = $db->query($sql, [$mobile, $serverDate, $serverTimestamp, $serverTimestamp, $serverTimestamp, $serverTimestamp, $serverTimestamp]);

        // 2. 插入详细记录表（用于滑动窗口检查）
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $sql = "INSERT INTO mobile_post_records (mobile, post_timestamp, server_timestamp, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?)";

        $result2 = $db->query($sql, [$mobile, $serverTimestamp, $serverTimestamp, $ipAddress, $userAgent, $serverTimestamp]);

        if ($result1 === false || $result2 === false) {
            throw new Exception("更新统计失败");
        }

        // 3. 清理过期记录（保留30天的记录用于分析）
        $sql = "DELETE FROM mobile_post_records WHERE created_at < ?";
        $db->query($sql, [$serverTimestamp - (30 * 86400)]);

        // 提交事务
        $db->commit();
        return true;

    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("更新手机号发布统计失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 更严格判断是否为Ajax请求
 * @return boolean 是否为Ajax请求
 */
function is_ajax_request() {
    // 检查请求头
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        return true;
    }
    
    // 如果有明确的AJAX标记并且同时有其他验证通过，则可以视为Ajax请求
    if ((isset($_POST['ajax']) || isset($_GET['ajax'])) && 
        (verify_csrf_token(isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '') && 
         validate_referer())) {
        return true;
    }
    
    return false;
}

// 获取分类ID和操作类型
$catId = isset($_GET['category_id']) ? filter($_GET['category_id'], 'int') : (isset($_GET['cid']) ? filter($_GET['cid'], 'int') : 0);

// 确定当前操作类型
$action = isset($_GET['action']) ? $_GET['action'] : (($catId > 0) ? 'post' : 'select');

// 根据操作类型执行不同功能
switch ($action) {
    // 删除图片API
    case 'delete_image':
        // 检查是否为Ajax请求
        if (!is_ajax_request()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '非法请求，必须使用Ajax方式提交'
            ]);
            exit;
        }
        
        // 检查权限、CSRF令牌和Referer
        $imageId = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $postId = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
        
        // 验证CSRF令牌
        if (!verify_csrf_token($token)) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '安全验证失败，请刷新页面重试'
            ]);
            exit;
        }
        
        // 验证Referer
        if (!validate_referer()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '请求来源验证失败，禁止外部提交'
            ]);
            exit;
        }
        
        if (!$imageId || !$postId) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '参数错误：缺少图片ID或信息ID'
            ]);
            exit;
        }
        
        // 删除图片
        if (deleteImage($imageId)) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => '图片删除成功'
            ]);
        } else {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '删除图片失败，请稍后重试'
            ]);
        }
        exit;
        
    // 栏目选择
    case 'select':
        // 获取缓存的分类数据
        $categories = $GLOBALS['cached_categories'];
        
        // 整理分类数据
        $parentCategories = array();
        $subCategories = array();
        
        // 按父子关系整理分类
        foreach ($categories as $cat) {
            // 确保parent_id字段存在
            $parentId = isset($cat['parent_id']) ? $cat['parent_id'] : 0;
            
            if ($parentId == 0) {
                $cat['children'] = array();
                $parentCategories[] = $cat;
            } else {
                if (!isset($subCategories[$parentId])) {
                    $subCategories[$parentId] = array();
                }
                $subCategories[$parentId][] = $cat;
            }
        }
        
        // 将子分类添加到对应的父分类中
        foreach ($parentCategories as &$parent) {
            // 确保id字段存在
            $parentId = isset($parent['id']) ? $parent['id'] : 0;
            
            if ($parentId > 0 && isset($subCategories[$parentId])) {
                $parent['children'] = $subCategories[$parentId];
            }
        }
        
        // 分配变量到模板
        assign('categories', $parentCategories);
        
        // 显示栏目选择模板
        display('select_category.htm');
        break;
        
    // 信息发布
    case 'post':
        // 获取分类信息
        $category = getCategoryInfo($catId);

        // 检查分类是否存在
        if (!$category) {
            show_error('分类不存在或已被删除');
        }

        // 检查是否为一级分类（一级分类不能直接发布信息）
        if ($category['parent_id'] == 0) {
            // 获取该一级分类下的子分类
            $subCategories = getSubCategories($catId);

            if (empty($subCategories)) {
                // 如果没有子分类，则该一级分类可以发布信息
                assign('category', $category);
            } else {
                // 如果有子分类，则使用栏目选择页面，只显示该一级分类及其子分类

                // 构建只包含当前一级分类的数据结构
                $selectedCategories = array();
                $subCategoriesMap = array();

                // 添加当前一级分类
                $selectedCategories[] = $category;

                // 添加其子分类
                $subCategoriesMap[$catId] = $subCategories;

                // 清除可能存在的全局categories变量影响
                unset($GLOBALS['cached_categories']);

                // 分配变量到模板，使用不同的变量名避免冲突
                assign('selected_categories', $selectedCategories);
                assign('selected_parent_id', $catId);
                assign('sub_categories_map', $subCategoriesMap);



                // 显示栏目选择模板
                display('select_category.htm');
                exit;
            }
        } else {
            // 二级分类可以直接发布
            assign('category', $category);
        }
        
        // 处理表单提交
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && (isset($_POST['submit']) || isset($_POST['ajax']))) {
            // 设置响应头为JSON
            header('Content-Type: application/json');
            
            $errors = array(); // 初始化错误数组
            
            // 验证CSRF令牌
            $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
            if (!verify_csrf_token($token)) {
                echo json_encode([
                    'success' => false,
                    'message' => '安全验证失败，请刷新页面重试'
                ]);
                exit;
            } 
            // 验证Referer
            else if (!validate_referer()) {
                echo json_encode([
                    'success' => false,
                    'message' => '请求来源验证失败，禁止外部提交'
                ]);
                exit;
            }
            else {
                // 验证表单数据
                $errors = validateForm();
            }
            
            if (empty($errors)) {
                // 保存信息
                $postId = savePost();
                
                if ($postId) {
                    // 上传并保存图片
                    if (!empty($_FILES['images']['name'][0])) {
                        saveImages($postId);
                    }

                    // 记录操作日志
                    $logger = new OperationLogger($db);
                    $logger->log([
                        'operation_type' => 'create',
                        'target_type' => 'post',
                        'target_id' => $postId,
                        'operation_desc' => '用户发布信息',
                        'target_title' => isset($_POST['title']) ? $_POST['title'] : '',
                        'user_id' => 0, // 用户ID为0，表示前台用户
                        'username' => '前台用户',
                        'user_type' => 'user'
                    ]);

                    // 清理相关缓存（新建信息，需要清理首页缓存）
                    clearPostRelatedCaches($postId, $category['id'], 'create');

                    // 获取分类拼音
                    $categoryPinyin = $category['pinyin'];

                    // 设置信息详情页URL
                    $detailUrl = "/{$categoryPinyin}/{$postId}.html";

                    echo json_encode([
                        'success' => true,
                        'message' => '您的信息已成功发布！',
                        'post_id' => $postId,
                        'detail_url' => $detailUrl
                    ]);
                    exit;
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => '发布失败，请稍后重试'
                    ]);
                    exit;
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => implode('<br>', $errors),
                    'errors' => $errors
                ]);
                exit;
            }
        }

        // 获取分类列表 (使用缓存)
        $categories = $GLOBALS['cached_categories'];

        // 整理分类数据
        $parentCategories = array();
        $subCategories = array();
        
        // 按父子关系整理分类
        foreach ($categories as $cat) {
            // 确保parent_id字段存在
            $parentId = isset($cat['parent_id']) ? $cat['parent_id'] : 0;
            
            if ($parentId == 0) {
                $cat['children'] = array();
                $parentCategories[] = $cat;
            } else {
                if (!isset($subCategories[$parentId])) {
                    $subCategories[$parentId] = array();
                }
                $subCategories[$parentId][] = $cat;
            }
        }
        
        // 将子分类添加到对应的父分类中
        foreach ($parentCategories as &$parent) {
            // 确保id字段存在
            $parentId = isset($parent['id']) ? $parent['id'] : 0;
            
            if ($parentId > 0 && isset($subCategories[$parentId])) {
                $parent['children'] = $subCategories[$parentId];
            }
        }

        // 获取区域列表 (使用缓存)
        $regions = $GLOBALS['cached_regions'];

        // 处理区域数据，为没有有效子区域的省份添加省份本身作为选项
        $regions = processRegionsForPost($regions);
        
        // 获取分类字段
        $fields = getCategoryFields($catId);

        // 分配变量到模板
        assign('categories', $parentCategories);
        assign('regions', $regions);
        assign('fields', $fields);
        assign('catId', $catId);
        assign('current_page', 'post');

        // 分配上传配置到模板
        assign('upload_config', [
            'max_size' => isset($config['upload_image_size']) ? intval($config['upload_image_size']) : 2,
            'max_count' => isset($config['upload_image_count']) ? intval($config['upload_image_count']) : 10,
            'allowed_extensions' => isset($config['allowed_extensions']) ? $config['allowed_extensions'] : 'jpg,jpeg,png,gif'
        ]);

        // 显示发布表单模板
        display('post.htm');
        break;
        
    // 信息编辑
    case 'edit':
        // 获取信息ID
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        // 检查信息ID是否有效
        if (!$id) {
            show_error('参数错误：未指定信息ID');
        }
        
        // 获取信息详情
        $post = getPostDetail($id);
        if (!$post) {
            show_error('信息不存在或已被删除');
        }
        
        // 检查密码验证
        $password = isset($_GET['password']) ? filter($_GET['password']) : '';
        $verified = false;
        
        // 检查会话中是否已验证
        if (isset($_SESSION['edit_verified'][$id]) && $_SESSION['edit_verified'][$id] === true) {
            $verified = true;
        } elseif (!empty($password)) {
            // 如果有密码参数，验证是否正确
            $verified = verifyPassword($id, md5($password));
            if ($verified) {
                // 记录验证状态到会话
                if (!isset($_SESSION['edit_verified'])) {
                    $_SESSION['edit_verified'] = array();
                }
                $_SESSION['edit_verified'][$id] = true;
            } else {
                show_error('管理密码不正确');
            }
        } elseif (!isset($_POST['submit'])) {
            // 如果没有密码参数且不是表单提交，跳转到管理页面
            redirect("manage.php?id=$id");
        }
        
        // 如果是提交表单
        if (isset($_POST['submit'])) {
            // 使用全局的AJAX检测结果
            $isAjax = $is_ajax_request;

            // 验证CSRF令牌
            $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
            if (!verify_csrf_token($token)) {
                if ($isAjax) {
                    echo json_encode(['success' => false, 'message' => '安全验证失败，请刷新页面重试']);
                    exit;
                } else {
                    show_error('安全验证失败，请刷新页面重试');
                }
            }

            // 验证Referer
            if (!validate_referer()) {
                if ($isAjax) {
                    echo json_encode(['success' => false, 'message' => '请求来源验证失败，禁止外部提交']);
                    exit;
                } else {
                    show_error('请求来源验证失败，禁止外部提交');
                }
            }
            
            // 获取表单数据
            $title = isset($_POST['title']) ? filter($_POST['title']) : '';
            $content = isset($_POST['content']) ? filter($_POST['content']) : '';
            $contactName = isset($_POST['contact_name']) ? filter($_POST['contact_name']) : '';
            $contactMobile = isset($_POST['contact_mobile']) ? filter($_POST['contact_mobile']) : '';
            $contactWeixin = isset($_POST['contact_weixin']) ? filter($_POST['contact_weixin']) : '';
            $contactAddress = isset($_POST['contact_address']) ? filter($_POST['contact_address']) : '';
            $regionId = isset($_POST['region_id']) ? intval($_POST['region_id']) : 0;
            $expireDays = isset($_POST['expire_days']) ? intval($_POST['expire_days']) : 30;
            
            // 验证数据
            $errors = array();
            
            // 验证标题
            if (empty($title)) {
                $errors[] = '标题不能为空';
            } elseif (mb_strlen($title, 'UTF-8') > 50) {
                $errors[] = '标题长度不能超过50个字符';
            }
            
            // 验证内容
            if (empty($content)) {
                $errors[] = '详情内容不能为空';
            } elseif (mb_strlen($content, 'UTF-8') < 10) {
                $errors[] = '详情内容太短，请至少输入10个字符';
            }
            
            // 验证联系人和手机号
            if (empty($contactName)) {
                $errors[] = '称呼不能为空';
            }
            
            if (empty($contactMobile)) {
                $errors[] = '手机号码不能为空';
            } else {
                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $contactMobile)) {
                    $errors[] = '手机号格式不正确';
                } else {
                    // 检查手机号每日发布限制（仅在新发布时检查，编辑时不检查）
                    if (!isset($id) || $id <= 0) {
                        $limitCheck = checkMobileDailyLimit($contactMobile);
                        if (!$limitCheck['allowed']) {
                            $errors[] = $limitCheck['message'];
                        }
                    }
                }
            }
            
            // 验证地区
            if ($regionId <= 0) {
                $errors[] = '请选择地区';
            }
            
            // 验证有效期
            if ($expireDays <= 0) {
                $errors[] = '请选择有效期';
            } elseif (!in_array($expireDays, [7, 15, 30, 60, 90])) {
                $errors[] = '无效的有效期选项';
            }
            

            
            if (empty($errors)) {
                try {
                    // 更新信息
                    if (updatePost($id, $title, $content, $contactName, $contactMobile, $contactWeixin, $contactAddress, $regionId)) {
                    // 处理图片上传
                    if (!empty($_FILES['images']['name'][0])) {
                        saveImages($id);
                    }
                    
                    // 处理图片删除
                    if (!empty($_POST['deleted_images']) && is_array($_POST['deleted_images'])) {
                        foreach ($_POST['deleted_images'] as $imageId) {
                            deleteImage(intval($imageId));
                        }
                    }
                    
                    // 处理现有图片
                    if (isset($_POST['existing_images'])) {
                        // 这里处理现有图片的排序或其他操作
                        error_log("处理现有图片: " . $_POST['existing_images']);
                    }
                    
                    // 记录操作日志
                    $logger = new OperationLogger($db);
                    $logger->log([
                        'operation_type' => 'update',
                        'target_type' => 'post',
                        'target_id' => $id,
                        'operation_desc' => '用户修改信息',
                        'target_title' => $title,
                        'user_id' => 0, // 用户ID为0，表示前台用户
                        'username' => '前台用户',
                        'user_type' => 'user'
                    ]);

                    // 清理相关缓存（修改信息，不需要清理首页缓存）
                    clearPostRelatedCaches($id, $post['category_id'], 'update');

                    // 清除会话中的验证状态
                    if (isset($_SESSION['edit_verified'][$id])) {
                        unset($_SESSION['edit_verified'][$id]);
                    }

                    // 根据请求类型返回不同的响应
                    if ($isAjax) {
                        echo json_encode([
                            'success' => true,
                            'message' => '信息修改成功',
                            'post_id' => $id,
                            'detail_url' => "/{$post['category_pinyin']}/{$id}.html"
                        ]);
                        exit;
                    } else {
                        show_success('信息修改成功', "/{$post['category_pinyin']}/{$id}.html");
                    }
                    } else {
                        if ($isAjax) {
                            echo json_encode([
                                'success' => false,
                                'message' => '信息修改失败，请稍后重试'
                            ]);
                            exit;
                        } else {
                            show_error('信息修改失败，请稍后重试');
                        }
                    }
                } catch (Exception $e) {
                    // 捕获异常并返回错误信息
                    if ($isAjax) {
                        echo json_encode([
                            'success' => false,
                            'message' => '操作失败：' . $e->getMessage()
                        ]);
                        exit;
                    } else {
                        show_error('操作失败：' . $e->getMessage());
                    }
                }
            } else {
                // 有错误
                if ($isAjax) {
                    echo json_encode([
                        'success' => false,
                        'message' => implode('<br>', $errors),
                        'errors' => $errors
                    ]);
                    exit;
                } else {
                    assign('error_message', implode('<br>', $errors));
                }
            }
        } else {
            // 获取当前图片
            $images = getPostImage($id, true);
            assign('images', $images);
            
            // 获取区域列表 (使用缓存)
            $regions = $GLOBALS['cached_regions'];

            // 处理区域数据，为没有有效子区域的省份添加省份本身作为选项
            $regions = processRegionsForPost($regions);

            assign('regions', $regions);
            
            // 将图片数据赋值给 post_images 变量，保持与模板一致
            assign('post_images', $images);
            
            // 分配信息数据到模板变量
            assign('post', $post);
            assign('formData', $post);

            // 分配上传配置到模板
            assign('upload_config', [
                'max_size' => isset($config['upload_image_size']) ? intval($config['upload_image_size']) : 2,
                'max_count' => isset($config['upload_image_count']) ? intval($config['upload_image_count']) : 10,
                'allowed_extensions' => isset($config['allowed_extensions']) ? $config['allowed_extensions'] : 'jpg,jpeg,png,gif'
            ]);

            // 显示编辑表单
            display('edit.htm');
        }
        break;
        
    default:
        // 默认显示分类选择
        redirect('post.php?action=select');
        break;
}

/**
 * 验证表单数据
 */
function validateForm() {
    $errors = array();
    
    // 必填字段验证
    $title = isset($_POST['title']) ? filter($_POST['title']) : '';
    $content = isset($_POST['content']) ? filter($_POST['content']) : '';
    $contactName = isset($_POST['contact_name']) ? filter($_POST['contact_name']) : '';
    $contactMobile = isset($_POST['contact_mobile']) ? filter($_POST['contact_mobile']) : '';
    $regionId = isset($_POST['region_id']) ? intval($_POST['region_id']) : 0;
    $expireDays = isset($_POST['expire_days']) ? intval($_POST['expire_days']) : 0;
    $password = isset($_POST['password']) ? filter($_POST['password']) : '';
    
    // 验证标题
    if (empty($title)) {
        $errors[] = '标题不能为空';
    } elseif (mb_strlen($title, 'UTF-8') > 50) {
        $errors[] = '标题长度不能超过50个字符';
    }
    
    // 验证内容
    if (empty($content)) {
        $errors[] = '详情内容不能为空';
    } elseif (mb_strlen($content, 'UTF-8') < 10) {
        $errors[] = '详情内容太短，请至少输入10个字符';
    }
    
    // 验证联系人
    if (empty($contactName)) {
        $errors[] = '称呼不能为空';
    }
    
    // 验证手机号
    if (empty($contactMobile)) {
        $errors[] = '手机号码不能为空';
    } else {
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $contactMobile)) {
            $errors[] = '手机号格式不正确';
        } else {
            // 检查手机号每日发布限制
            $limitCheck = checkMobileDailyLimit($contactMobile);
            if (!$limitCheck['allowed']) {
                $errors[] = $limitCheck['message'];
            }
        }
    }
    
    // 验证地区
    if ($regionId <= 0) {
        $errors[] = '请选择地区';
    }
    
    // 验证有效期
    if ($expireDays <= 0) {
        $errors[] = '请选择有效期';
    } elseif (!in_array($expireDays, [7, 15, 30, 60, 90])) {
        $errors[] = '无效的有效期选项';
    }
    
    // 验证管理密码
    if (empty($password)) {
        $errors[] = '请设置管理密码';
    } elseif (strlen($password) < 6) {
        $errors[] = '管理密码长度不能少于6位';
    }
    
    return $errors;
}

/**
 * 保存信息
 */
function savePost() {
    global $db, $catId;
    
    // 获取信息字段
    $title = isset($_POST['title']) ? filter($_POST['title']) : '';
    $content = isset($_POST['content']) ? filter($_POST['content']) : '';
    $contactName = isset($_POST['contact_name']) ? filter($_POST['contact_name']) : '';
    $contactMobile = isset($_POST['contact_mobile']) ? filter($_POST['contact_mobile']) : '';
    $contactWeixin = isset($_POST['contact_weixin']) ? filter($_POST['contact_weixin']) : '';
    $contactAddress = isset($_POST['contact_address']) ? filter($_POST['contact_address']) : '';
    $regionId = isset($_POST['region_id']) ? intval($_POST['region_id']) : 0;
    $expireDays = isset($_POST['expire_days']) ? intval($_POST['expire_days']) : 30;
    $password = md5(filter($_POST['password']));
  
    // 计算时间戳
    $currentTime = time();
    $expireTime = $currentTime + ($expireDays * 86400); // 一天有86400秒
   
    // 获取IP地址
    $ip = get_client_ip();
    
    // 初始化自定义字段数据
    $fieldsData = '';
    if (isset($_POST['fields']) && is_array($_POST['fields'])) {
        $fieldsData = json_encode($_POST['fields']);
    }
    
    // 获取上传图片数量
    $imageCount = 0;
    if (!empty($_FILES['images']['name'][0])) {
        $imageCount = count(array_filter($_FILES['images']['name']));
    }
    
    try {
        // 开始事务
        $db->beginTransaction();
        
        // 1. 保存信息基本数据到posts表
        $sql = "INSERT INTO posts (
                category_id, title, region_id,
                expire_days, status, created_at, updated_at, expired_at, image_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
        $result = $db->query($sql, [
            $catId, $title, $regionId,
            $expireDays, 1, $currentTime, $currentTime, $expireTime, $imageCount
        ]);
        
        if ($result === false) {
            throw new Exception("Database error: " . $db->error());
        }
        
        $postId = $db->lastInsertId();
        
        if ($postId <= 0) {
            throw new Exception("Failed to get insert ID");
        }
        
        // 2. 在同一事务中保存内容、联系人信息、密码和IP到post_contents表
        $contentSql = "INSERT INTO post_contents (
                      post_id, content, fields_data, ip,
                      password, contact_name, contact_mobile, 
                      contact_weixin, contact_address
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $contentResult = $db->query($contentSql, [
            $postId, $content, $fieldsData, $ip,
            $password, $contactName, $contactMobile, 
            $contactWeixin, $contactAddress
        ]);
        
        if ($contentResult === false) {
            throw new Exception("Failed to save content: " . $db->error());
        }
        
        // 提交事务
        $db->commit();

        // 更新分类计数
        if (function_exists('updateCategoryCount')) {
            updateCategoryCount($catId, 1);
        }

        // 更新手机号每日发布统计
        updateMobileDailyStats($contactMobile);

        // 显示密码给用户
        $_SESSION['last_password'] = $password;
        assign('password', $password);

        return $postId;
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        return false;
    }
}

