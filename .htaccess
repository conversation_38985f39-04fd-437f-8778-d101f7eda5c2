RewriteEngine On

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# 简化架构 - 所有PHP文件现在都在根目录，可以直接访问






















RewriteRule ^news/$ news.php [L,QSA]

# ������Ŀҳ: /news/��Ŀƴ��/
RewriteRule ^news/([a-zA-Z0-9_-]+)/$ news.php?catpinyin=$1 [L,QSA]

# ������Ŀ��ҳ: /news/��Ŀƴ��/p2/
RewriteRule ^news/([a-zA-Z0-9_-]+)/p([0-9]+)/$ news.php?catpinyin=$1&page=$2 [L,QSA]

# ��������ҳ: /news/id.html
RewriteRule ^news/([0-9]+)\.html$ news.php?id=$1 [L,QSA]

# ��ҳϵͳα��̬����
# ��ҳ��ʾ: /page/�Զ���·��.html
# 单页系统伪静态规则
# 优先匹配ID访问: /about/123.html (纯数字)
RewriteRule ^about/([0-9]+)\.html$ about.php?id=$1 [L,QSA]
# 然后匹配路径访问: /about/jianjie.html (包含字母)
RewriteRule ^about/([a-zA-Z0-9_/-]+)\.html$ about.php?path=about/$1 [L,QSA]

# ������Ϣϵͳα��̬���򣨱���ԭ�й���
RewriteRule ^([a-zA-Z0-9_-]+)/$ category.php?pinyin=$1 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/([0-9]+)\.html$ view.php?id=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/p([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)/$ category.php?pinyin=$1&area=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)p([0-9]+)/$ category.php?pinyin=$1&area=$2&page=$3 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/page/([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteCond %{REQUEST_URI} ^/admin/
RewriteRule ^ - [L]