RewriteEngine On

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# 简化架构 - 所有PHP文件现在都在根目录，可以直接访问






















RewriteRule ^news/$ news.php [L,QSA]


RewriteRule ^news/([a-zA-Z0-9_-]+)/$ news.php?catpinyin=$1 [L,QSA]


RewriteRule ^news/([a-zA-Z0-9_-]+)/p([0-9]+)/$ news.php?catpinyin=$1&page=$2 [L,QSA]


RewriteRule ^news/([0-9]+)\.html$ news.php?id=$1 [L,QSA]


RewriteRule ^about/([0-9]+)\.html$ about.php?id=$1 [L,QSA]

RewriteRule ^about/([a-zA-Z0-9_/-]+)\.html$ about.php?path=about/$1 [L,QSA]


RewriteRule ^([a-zA-Z0-9_-]+)/$ category.php?pinyin=$1 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/([0-9]+)\.html$ view.php?id=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/p([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)/$ category.php?pinyin=$1&area=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)p([0-9]+)/$ category.php?pinyin=$1&area=$2&page=$3 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/page/([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteCond %{REQUEST_URI} ^/admin/
RewriteRule ^ - [L]