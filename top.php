<?php
define('IN_BTMPS', true);
require_once(__DIR__ . '/include/common.inc.php');

/**
 * 信息置顶页面
 * 处理信息的置顶操作
 */

// 引入操作日志类
require_once './include/OperationLogger.class.php';

// 获取信息ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 检查信息ID是否有效
if (!$id) {
    show_error('参数错误：未指定信息ID');
}

// 获取信息详情
$post = getPostDetail($id);
if (!$post) {
    show_error('信息不存在或已被删除');
}

// 处理置顶表单提交
if (isset($_POST['submit'])) {
    $password = isset($_POST['password']) ? filter($_POST['password']) : '';
    $top_days = isset($_POST['top_days']) ? intval($_POST['top_days']) : 0;
    $top_type = isset($_POST['top_type']) ? filter($_POST['top_type']) : '';
    
    // 验证
    if (empty($password)) {
        show_error('请输入管理密码');
    }
    
    if (!in_array($top_type, ['0', '1', '2'])) {
        show_error('请选择有效的置顶类型');
    }
    
    if ($top_type != '0' && $top_days <= 0) {
        show_error('请选择置顶天数');
    }
    
    // 验证管理密码
    if (verifyPassword($id, md5($password))) {
        // 执行置顶操作
        if ($top_type == '0') {
            // 取消置顶
            if (cancelPostTop($id)) {
                // 记录操作日志
                $logger = new OperationLogger($db);
                $logger->log([
                    'operation_type' => 'update',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => '用户取消信息置顶',
                    'target_title' => $post['title'],
                    'user_id' => 0, // 用户ID为0，表示前台用户
                    'username' => '前台用户',
                    'user_type' => 'user'
                ]);

                show_success('取消置顶成功', "/{$post['category_pinyin']}/{$id}.html");
            } else {
                show_error('取消置顶失败，请稍后重试');
            }
        } else {
            // 执行置顶
            $top_type_name = ($top_type == '1') ? 'category' : 'home';
            if (setPostTop($id, $top_type_name, $top_days)) {
                // 记录操作日志
                $logger = new OperationLogger($db);
                $top_desc = ($top_type == '1') ? '分类置顶' : '首页置顶';
                $logger->log([
                    'operation_type' => 'update',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => "用户设置信息{$top_desc}（{$top_days}天）",
                    'target_title' => $post['title'],
                    'user_id' => 0, // 用户ID为0，表示前台用户
                    'username' => '前台用户',
                    'user_type' => 'user'
                ]);

                show_success('信息置顶成功', "/{$post['category_pinyin']}/{$id}.html");
            } else {
                show_error('信息置顶失败，请稍后重试');
            }
        }
    } else {
        show_error('管理密码不正确');
    }
} else {
    // 显示置顶表单
    assign('post', $post);
    
    // 置顶天数选项
    $top_days_options = array(
        1 => '1天',
        7 => '7天',
        15 => '15天',
        30 => '30天',
        60 => '60天'
    );
    assign('top_days_options', $top_days_options);
    
    // 根据客户端类型选择模板
    display('top.htm');
}

/**
 * 取消信息置顶
 */
function cancelPostTop($id) {
    global $db, $cache;
    
    // 获取信息的分类ID
    $sql = "SELECT category_id FROM posts WHERE id = ?";
    $result = $db->query($sql, [$id]);
    $post = $db->fetch_array($result);
    $categoryId = $post ? $post['category_id'] : 0;
    
    // 更新置顶状态，取消所有置顶
    $sql = "UPDATE posts SET is_top_category = 0, is_top_subcategory = 0, is_top_home = 0, 
            top_category_expire = 0, top_subcategory_expire = 0, top_home_expire = 0 
            WHERE id = ?";
    $result = $db->query($sql, [$id]);
    
    if ($result) {
        // 清除相关缓存 - 使用改进的缓存清理机制
        $cache->clearTopPostCache($id, $categoryId, true);
        return true;
    }
    
    return false;
}

/**
 * 设置信息置顶
 */
function setPostTop($id, $top_type, $days) {
    global $db, $cache;
    
    // 参数校验
    if (!$id || !in_array($top_type, ['category', 'home']) || $days <= 0) {
        return false;
    }
    
    // 获取信息的分类ID
    $sql = "SELECT category_id FROM posts WHERE id = ?";
    $result = $db->query($sql, [$id]);
    $post = $db->fetch_array($result);
    $categoryId = $post ? $post['category_id'] : 0;
    
    $expire_timestamp = time() + ($days * 86400); // 使用时间戳代替日期字符串
    $update_fields = '';
    
    // 根据置顶类型设置不同的置顶字段
    if ($top_type == 'category') {
        $update_fields = "is_top_category = 1, top_category_expire = $expire_timestamp";
    } elseif ($top_type == 'home') {
        $update_fields = "is_top_home = 1, top_home_expire = $expire_timestamp";
    } else {
        return false;
    }
    
    // 更新置顶状态
    $sql = "UPDATE posts SET $update_fields WHERE id = ?";
    $result = $db->query($sql, [$id]);
    
    if ($result) {
        // 清除相关缓存 - 使用改进的缓存清理机制
        $cache->clearTopPostCache($id, $categoryId, $top_type == 'home');
        return true;
    }
    
    return false;
} 