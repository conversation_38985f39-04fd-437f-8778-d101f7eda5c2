<?php
/**
 * 模板引擎类
 * 实现简单的标签替换和变量赋值功能
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class Template {
    // 模板变量
    private $vars = array();
    
    // 模板目录
    private $template_dir = '';
    
    // 缓存目录
    private $cache_dir = '';
    
    // 是否开启缓存
    private $cache_on = false;
    
    // 缓存有效期(秒)
    private $cache_expire = 3600;
    
    // 跟踪处理过的包含文件，防止循环包含
    private $included_files = array();

    // 预编译的正则表达式模式，提高性能
    private static $regex_patterns = array(
        'php_tags' => '/{php}(.*?){\\/php}/s',
        'include_tags' => '/{include\\s+file=["\']([^\'"]+)["\']}/i',
        'simple_vars' => '/{\\$([a-zA-Z0-9_]+)}/i',
        'complex_vars' => '/{\\$([a-zA-Z0-9_\.]+)}/i',
        'modified_vars' => '/{\\$([a-zA-Z0-9_\.]+)\\|([a-zA-Z0-9_]+)(?:\\:([^}]*))?}/i',
        'if_tags' => '/{if\\s+(.+?)}/i',
        'elseif_tags' => '/{elseif\\s+(.+?)}/i',
        'loop_with_key' => '/{loop\\s+\\$([a-zA-Z0-9_\.]+)\\s+\\$([a-zA-Z0-9_]+)\\s+\\$([a-zA-Z0-9_]+)}/i',
        'loop_simple' => '/{loop\\s+\\$([a-zA-Z0-9_\.]+)\\s+\\$([a-zA-Z0-9_]+)}/i',
        'foreach_with_key' => '/{foreach\\s+\\$([a-zA-Z0-9_\.]+)\\s+as\\s+\\$([a-zA-Z0-9_]+)\\s*=>\\s*\\$([a-zA-Z0-9_]+)}/i',
        'foreach_simple' => '/{foreach\\s+\\$([a-zA-Z0-9_\.]+)\\s+as\\s+\\$([a-zA-Z0-9_]+)}/i',
        'foreach_smarty' => '/{foreach\\s+from=\\$([a-zA-Z0-9_\.]+)\\s+item=(?:\\$)?([a-zA-Z0-9_]+)}/i',
        'foreach_smarty_key' => '/{foreach\\s+from=\\$([a-zA-Z0-9_\.]+)\\s+item=(?:\\$)?([a-zA-Z0-9_]+)\\s+key=(?:\\$)?([a-zA-Z0-9_]+)}/i',
        'for_tags' => '/{for\\s+(.*?)}/i',
        'date_tags' => '/{date:([a-zA-Z])}/i',
        'function_calls' => '/{([a-zA-Z0-9_]+)}/i'
    );

    // 变量解析缓存，避免重复解析相同的变量表达式
    private static $var_parse_cache = array();

    // 性能监控数据
    private $performance_data = array(
        'compile_time' => 0,
        'render_time' => 0,
        'memory_usage' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0
    );

    // 是否启用性能监控
    private $enable_profiling = false;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->template_dir = ROOT_PATH . 'template/';
        $this->cache_dir = DATA_PATH . 'compiled/';

        // 确保缓存目录存在
        if (!is_dir($this->cache_dir)) {
            mkdir($this->cache_dir, 0777, true);
        }

        // 从配置中加载模板引擎设置
        $this->loadTemplateSettings();
    }
    
    /**
     * 从配置中加载模板引擎设置
     */
    private function loadTemplateSettings() {
        // 如果全局配置存在，使用配置中的设置
        if (isset($GLOBALS['config'])) {
            $config = $GLOBALS['config'];

            // 设置缓存开关
            if (isset($config['template_cache_enable'])) {
                $this->cache_on = (bool)$config['template_cache_enable'];
            }

            // 设置缓存过期时间
            if (isset($config['template_cache_expire'])) {
                $this->cache_expire = (int)$config['template_cache_expire'];
            }

            // 设置性能监控
            if (isset($config['template_profiling_enable'])) {
                $this->enable_profiling = (bool)$config['template_profiling_enable'];
            }
        }
    }

    /**
     * 设置模板目录
     */
    public function setTemplateDir($dir) {
        $this->template_dir = $dir;
    }

    /**
     * 获取模板目录
     */
    public function getTemplateDir() {
        return $this->template_dir;
    }
    
    /**
     * 设置缓存目录
     */
    public function setCacheDir($dir) {
        $this->cache_dir = $dir;
    }
    
    /**
     * 开启或关闭缓存
     */
    public function setCaching($on = true) {
        $this->cache_on = $on;
    }
    
    /**
     * 设置缓存有效期
     */
    public function setCacheExpire($expire) {
        $this->cache_expire = $expire;
    }

    /**
     * 启用或禁用性能监控
     */
    public function enableProfiling($enable = true) {
        $this->enable_profiling = $enable;
        if ($enable) {
            $this->resetPerformanceData();
        }
    }

    /**
     * 重置性能数据
     */
    private function resetPerformanceData() {
        $this->performance_data = array(
            'compile_time' => 0,
            'render_time' => 0,
            'memory_usage' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0
        );
    }

    /**
     * 获取性能数据
     */
    public function getPerformanceData() {
        return $this->performance_data;
    }
    
    /**
     * 分配模板变量
     */
    public function assign($key, $value = null) {
        if (is_array($key)) {
            foreach ($key as $k => $v) {
                $this->vars[$k] = $v;
            }
        } else {
            $this->vars[$key] = $value;
        }
    }
    
    /**
     * 获取模板变量
     */
    public function getVar($key) {
        return isset($this->vars[$key]) ? $this->vars[$key] : null;
    }
    
    /**
     * 获取所有模板变量
     */
    public function getVars() {
        return $this->vars;
    }
    
    /**
     * 清除模板变量
     */
    public function clearVars() {
        $this->vars = array();
    }
    
    /**
     * 获取模板文件的绝对路径
     */
    private function getTemplatePath($template, $current_dir = '') {
        // 如果模板路径不包含目录分隔符，并且当前目录不为空，则使用当前目录
        if (strpos($template, '/') === false && !empty($current_dir)) {
            $template = $current_dir . '/' . $template;
        }
        
        return $this->template_dir . $template;
    }
    
    /**
     * 解析模板标签
     */
    private function parseTemplate($content, $template_dir = '', $template_file = '') {
        // 标记当前文件为已处理，防止循环包含
        if (!empty($template_file)) {
            $this->included_files[$template_file] = true;
        }
        
        // 替换PHP代码标签 {php} ... {/php}
        $content = preg_replace(self::$regex_patterns['php_tags'], '<?php $1 ?>', $content);

        // 替换include标签 {include file="path/to/file"}
        $content = preg_replace_callback(self::$regex_patterns['include_tags'], function($matches) use ($template_dir, $template_file) {
            $include_file = $matches[1];
            
            // 获取当前模板文件的目录
            $current_template_dir = !empty($template_dir) ? $template_dir : 
                                   (!empty($template_file) ? dirname($template_file) : '');
            
            // 如果include文件是相对路径
            if (strpos($include_file, '/') === false) {
                // 没有目录分隔符，视为当前目录下的文件
                $include_file = $current_template_dir . '/' . $include_file;
            } else if (strpos($include_file, '../') === 0) {
                // 处理上级目录的相对路径
                $include_file = dirname($current_template_dir) . '/' . substr($include_file, 3);
            }
            
            // 获取include文件的完整路径
            if (strpos($include_file, $this->template_dir) === 0) {
                // 如果路径已经包含模板目录前缀，不再添加
                $include_path = $include_file;
            } else {
                // 否则添加模板目录前缀
                $include_path = $this->template_dir . $include_file;
            }
            
            // 检查是否已经处理过该文件，防止循环包含
            if (isset($this->included_files[$include_path])) {
                return "<?php echo '<!-- 检测到循环包含: {$include_path} -->'; ?>";
            }
            
            // 获取include文件的内容
            if (file_exists($include_path)) {
                $include_content = file_get_contents($include_path);
                
                // 避免在parseTemplate方法中标记该文件，而是在当前位置标记
                $this->included_files[$include_path] = true;
                
                // 递归解析include文件中的模板标签
                $include_content = $this->parseTemplate($include_content, dirname($include_file), '');
                return $include_content;
            } else {
                return "<?php echo '<!-- 包含的文件不存在: {$include_path} -->'; ?>";
            }
        }, $content);
        
        // 替换复杂数组变量标签 {$user.name} {$data.items.0.title}
        $content = preg_replace_callback(self::$regex_patterns['complex_vars'], function($matches) {
            return $this->parseVariable($matches[1]);
        }, $content);
        
        // 替换带修饰符的变量标签 {$var|func} 或 {$var|func:param}
        $content = preg_replace_callback(self::$regex_patterns['modified_vars'], function($matches) {
            $var = $matches[1];
            $modifier = $matches[2];
            $params = isset($matches[3]) ? $matches[3] : '';
            
            // 构建变量访问和检查代码
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                // 完整的变量访问可能为空
                $safeCode = '(null !== ($' . $varName . ' ?? null)) ? (' . $code . ') : null';
            } else {
                $code = '$' . $var;
                $safeCode = '$' . $var . ' ?? null';
            }
            
            // 处理default修饰符，因为default是PHP关键字，需要特殊处理
            if ($modifier === 'default') {
                return '<?php echo null !== (' . $safeCode . ') && (' . $safeCode . ') !== "" ? ' . $safeCode . ' : ' . $params . '; ?>';
            }
            
            // 调用修饰符函数
            $staticMethod = 'Template::' . $modifier;
            
            // 优先使用Template类的静态方法
            if (method_exists('Template', $modifier)) {
                if (!empty($params)) {
                    return '<?php echo null !== (' . $safeCode . ') ? ' . $staticMethod . '(' . $safeCode . ', ' . $params . ') : ""; ?>';
                } else {
                    return '<?php echo null !== (' . $safeCode . ') ? ' . $staticMethod . '(' . $safeCode . ') : ""; ?>';
                }
            } elseif (function_exists($modifier)) {
                // 如果没有Template类的静态方法，尝试使用同名的PHP函数
                if (!empty($params)) {
                    // 注意：某些PHP函数可能需要不同的参数格式
                    return '<?php echo null !== (' . $safeCode . ') ? ' . $modifier . '(' . $safeCode . ', ' . $params . ') : ""; ?>';
                } else {
                    return '<?php echo null !== (' . $safeCode . ') ? ' . $modifier . '(' . $safeCode . ') : ""; ?>';
                }
            } else {
                // 修饰符函数不存在，原样输出
                return '<?php echo ' . $safeCode . ' ?? ""; ?>';
            }
        }, $content);
        
        // 修复HTML中的特殊字符，防止被当作PHP语法错误
        $content = str_replace(array('&lt;', '&gt;'), array('&amp;lt;', '&amp;gt;'), $content);
        
        // 替换if标签 {if condition} ... {/if}
        $content = preg_replace_callback(self::$regex_patterns['if_tags'], function($matches) {
            $condition = $this->parseCondition($matches[1]);
            return '<?php if(' . $condition . '): ?>';
        }, $content);
        $content = preg_replace('/{\\/if}/i', '<?php endif; ?>', $content);

        // 替换else标签 {else}
        $content = preg_replace('/{else}/i', '<?php else: ?>', $content);

        // 替换elseif标签 {elseif condition}
        $content = preg_replace_callback(self::$regex_patterns['elseif_tags'], function($matches) {
            $condition = $this->parseCondition($matches[1]);
            return '<?php elseif(' . $condition . '): ?>';
        }, $content);
        
        // 替换foreach标签为loop标签
        // 处理带键值对的loop标签 {loop $array $key $value} ... {/loop}
        $content = preg_replace_callback(self::$regex_patterns['loop_with_key'], function($matches) {
            $var = $matches[1];
            $key = $matches[2];
            $value = $matches[3];
            
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $key . ' => $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $key . ' => $' . $value . '): ?>';
            }
        }, $content);
        
        // 处理简单loop标签 {loop $array $value} ... {/loop}
        $content = preg_replace_callback(self::$regex_patterns['loop_simple'], function($matches) {
            $var = $matches[1];
            $value = $matches[2];
            
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $value . '): ?>';
            }
        }, $content);
        
        // 处理loop结束标签 {/loop}
        $content = preg_replace('/{\\/loop}/i', '<?php endforeach; endif; ?>', $content);
        
        // 处理for循环 {for $i=1; $i<=5; $i++} ... {/for}
        $content = preg_replace_callback('/{for\\s+(.*?)}/i', function($matches) {
            return '<?php for(' . $matches[1] . '): ?>';
        }, $content);
        
        // 处理for结束标签 {/for}
        $content = preg_replace('/{\\/for}/i', '<?php endfor; ?>', $content);
        
        // 为了向后兼容，保留foreach标签处理，但建议使用loop标签
        // 处理带键值对的foreach标签 {foreach $array as $key => $value} ... {/foreach}
        $content = preg_replace_callback('/{foreach\\s+\\$([a-zA-Z0-9_\.]+)\\s+as\\s+\\$([a-zA-Z0-9_]+)\\s*=>\\s*\\$([a-zA-Z0-9_]+)}/i', function($matches) {
            $var = $matches[1];
            $key = $matches[2];
            $value = $matches[3];
            
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $key . ' => $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $key . ' => $' . $value . '): ?>';
            }
        }, $content);
        
        // 处理简单foreach标签 {foreach $array as $value} ... {/foreach}
        $content = preg_replace_callback('/{foreach\\s+\\$([a-zA-Z0-9_\.]+)\\s+as\\s+\\$([a-zA-Z0-9_]+)}/i', function($matches) {
            $var = $matches[1];
            $value = $matches[2];
            
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $value . '): ?>';
            }
        }, $content);
        
        // 处理foreach结束标签 {/foreach}
        $content = preg_replace('/{\\/foreach}/i', '<?php endforeach; endif; ?>', $content);
        
        // 处理Smarty风格的foreach标签 {foreach from=$array item=value}
        $content = preg_replace_callback('/{foreach\\s+from=\\$([a-zA-Z0-9_\.]+)\\s+item=(?:\\$)?([a-zA-Z0-9_]+)}/i', function($matches) {
            $var = $matches[1];
            $value = $matches[2];
            
            // 处理复杂变量例如$data.items
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $value . '): ?>';
            }
        }, $content);
        
        // 处理Smarty风格的foreach带键名标签 {foreach from=$array item=value key=key}
        $content = preg_replace_callback('/{foreach\\s+from=\\$([a-zA-Z0-9_\.]+)\\s+item=(?:\\$)?([a-zA-Z0-9_]+)\\s+key=(?:\\$)?([a-zA-Z0-9_]+)}/i', function($matches) {
            $var = $matches[1];
            $value = $matches[2];
            $key = $matches[3];
            
            // 处理复杂变量例如$data.items
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }
                
                return '<?php if(null !== ($' . $varName . ' ?? null) && is_array(' . $code . ')): foreach(' . $code . ' as $' . $key . ' => $' . $value . '): ?>';
            } else {
                return '<?php if(null !== ($' . $var . ' ?? null) && is_array($' . $var . ')): foreach($' . $var . ' as $' . $key . ' => $' . $value . '): ?>';
            }
        }, $content);
        
        // 最后，还原为正确的HTML符号
        $content = str_replace(array('&amp;lt;', '&amp;gt;'), array('&lt;', '&gt;'), $content);
        
        // 替换年份标签 {date:Y}
        $content = preg_replace('/{date:([a-zA-Z])}/i', '<?php echo date("\\1"); ?>', $content);
        
        // 添加年份函数 {year}
        $content = preg_replace('/{year}/i', '<?php echo date("Y"); ?>', $content);
        
        // 替换函数调用 {function_name}
        $content = preg_replace_callback('/{([a-zA-Z0-9_]+)}/i', function($matches) {
            $function = $matches[1];
            $templateFunction = 'template_function_' . $function;

            if (function_exists($templateFunction)) {
                return '<?php echo ' . $templateFunction . '(); ?>';
            } else {
                // 如果没有对应的模板函数，保持原样
                return '{' . $function . '}';
            }
        }, $content);

        // 替换块标签 {block:identifier} - 放在最后处理，避免被其他规则干扰
        $content = preg_replace_callback('/{block:([a-zA-Z0-9_-]+)}/i', function($matches) {
            $identifier = $matches[1];
            return '<?php echo get_block("' . $identifier . '"); ?>';
        }, $content);

        return $content;
    }
    
    /**
     * 优化的变量解析方法，支持缓存
     */
    private function parseVariable($var) {
        // 检查缓存
        if (isset(self::$var_parse_cache[$var])) {
            return self::$var_parse_cache[$var];
        }

        $result = '';

        if (strpos($var, '.') !== false) {
            // 复杂变量解析
            $parts = explode('.', $var);
            $varName = array_shift($parts);

            if (!empty($parts)) {
                // 构建安全的数组访问代码
                $code = '$' . $varName;
                foreach ($parts as $part) {
                    $code .= '[\'' . $part . '\']';
                }

                // 生成安全的访问代码，避免未定义索引错误
                $result = '<?php echo (isset(' . $code . ')) ? ' . $code . ' : ""; ?>';
            } else {
                $result = '<?php echo $' . $varName . ' ?? ""; ?>';
            }
        } else {
            // 简单变量
            $result = '<?php echo $' . $var . ' ?? ""; ?>';
        }

        // 缓存结果
        self::$var_parse_cache[$var] = $result;

        return $result;
    }

    /**
     * 解析条件表达式，支持数组变量
     */
    private function parseCondition($condition) {
        // 完全重写此方法，确保在PHP 7.4+中不使用isset()嵌套表达式
        
        // 检测并替换 isset($var) 模式
        $condition = preg_replace_callback('/isset\\s*\\(\\s*\\$([a-zA-Z0-9_]+)\\s*\\)/', function($matches) {
            return 'null !== ($' . $matches[1] . ' ?? null)';
        }, $condition);
        
        // 检测并替换 isset($var.field) 或 isset($var['field']) 模式
        $condition = preg_replace_callback('/isset\\s*\\(\\s*\\$([a-zA-Z0-9_]+)\\.([a-zA-Z0-9_\\.]+)\\s*\\)/', function($matches) {
            $var = $matches[1];
            $field = $matches[2];
            $parts = explode('.', $field);
            $code = '$' . $var;
            $checkCode = 'null !== ($' . $var . ' ?? null)';
            
            foreach ($parts as $part) {
                $checkCode .= ' && is_array(' . $code . ') && array_key_exists(\'' . $part . '\', ' . $code . ')';
                $code .= '[\'' . $part . '\']';
            }
            
            return $checkCode;
        }, $condition);
        
        // 检测并替换 !empty($var) 模式
        $condition = preg_replace_callback('/!empty\\s*\\(\\s*\\$([a-zA-Z0-9_\\.]+)\\s*\\)/', function($matches) {
            $var = $matches[1];
            if (strpos($var, '.') !== false) {
                $parts = explode('.', $var);
                $varName = array_shift($parts);
                $code = '$' . $varName;
                $checkCode = 'null !== ($' . $varName . ' ?? null)';
                
                foreach ($parts as $part) {
                    $checkCode .= ' && is_array(' . $code . ') && array_key_exists(\'' . $part . '\', ' . $code . ')';
                    $code .= '[\'' . $part . '\']';
                }
                
                return $checkCode . ' && !empty(' . $code . ')';
            } else {
                return 'null !== ($' . $var . ' ?? null) && !empty($' . $var . ')';
            }
        }, $condition);
        
        // 替换数组变量 $user.name -> $user['name']
        $condition = preg_replace_callback('/\\$([a-zA-Z0-9_]+)\\.([a-zA-Z0-9_\\.]+)/', function($matches) {
            $var = $matches[1];
            $field = $matches[2];
            $parts = explode('.', $field);
            $code = '$' . $var;
            
            foreach ($parts as $part) {
                $code .= '[\'' . $part . '\']';
            }
            
            return $code;
        }, $condition);
        
        return $condition;
    }
    
    /**
     * 编译模板
     */
    private function compile($template_file, $cache_file) {
        // 读取模板内容
        $content = file_get_contents($template_file);
        
        // 检测并处理UTF-16 LE编码(FF FE)
        if (substr($content, 0, 2) === "\xFF\xFE") {
            // 转换UTF-16LE到UTF-8
            $content = mb_convert_encoding($content, 'UTF-8', 'UTF-16LE');
        }
        // 检测并处理UTF-16 BE编码(FE FF)
        else if (substr($content, 0, 2) === "\xFE\xFF") {
            // 转换UTF-16BE到UTF-8
            $content = mb_convert_encoding($content, 'UTF-8', 'UTF-16BE');
        }
        // 检测并移除UTF-8 BOM
        else if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
            $content = substr($content, 3);
        }
        
        // 确保内容是UTF-8编码
        if (!mb_check_encoding($content, 'UTF-8')) {
            $detect_encoding = mb_detect_encoding($content, array('UTF-8', 'GBK', 'GB2312', 'BIG5'));
            if ($detect_encoding) {
                $content = mb_convert_encoding($content, 'UTF-8', $detect_encoding);
            } else {
                $content = mb_convert_encoding($content, 'UTF-8');
            }
        }
        
        // 重置包含文件跟踪
        $this->included_files = array();
        
        // 解析模板内容
        $content = $this->parseTemplate($content, dirname($template_file), $template_file);
        
        // 确保缓存目录存在
        $cache_dir = dirname($cache_file);
        if (!is_dir($cache_dir)) {
            mkdir($cache_dir, 0777, true);
        }
        
        // 将编译后的内容写入缓存文件，强制UTF-8编码
        file_put_contents($cache_file, $content);
        
        return true;
    }
    
    /**
     * 检查是否需要重新编译模板
     */
    private function needRecompile($template_file, $cache_file) {
        // 如果缓存文件不存在，需要编译
        if (!file_exists($cache_file)) {
            return true;
        }

        // 如果模板文件比缓存文件新，需要重新编译
        if (filemtime($template_file) > filemtime($cache_file)) {
            return true;
        }

        // 检查包含的文件是否有更新
        if ($this->hasIncludeFileChanged($template_file, $cache_file)) {
            return true;
        }

        // 如果开启了缓存但设置了过期时间，检查是否过期
        if ($this->cache_on && $this->cache_expire > 0) {
            $cache_age = time() - filemtime($cache_file);
            if ($cache_age > $this->cache_expire) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查包含文件是否有变化
     */
    private function hasIncludeFileChanged($template_file, $cache_file) {
        $cache_time = filemtime($cache_file);

        // 读取模板内容查找include标签
        $content = file_get_contents($template_file);
        if (preg_match_all('/{include\\s+file=["\']([^\'"]+)["\']}/i', $content, $matches)) {
            foreach ($matches[1] as $include_file) {
                // 获取include文件的完整路径
                $include_path = $this->getTemplatePath($include_file, dirname($template_file));

                if (file_exists($include_path) && filemtime($include_path) > $cache_time) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 强制删除缓存文件
     */
    public function clearCache($template = null) {
        if ($template === null) {
            // 清除所有缓存
            $this->clearCacheDir($this->cache_dir);
        } else {
            // 清除指定模板的缓存
            // 获取模板目录
            $template_dir = '';
            if (strpos($template, '/') !== false) {
                $template_dir = dirname($template);
                $template_name = basename($template);
                $cache_file = $this->cache_dir . $template_dir . '/' . md5($template_name) . '.php';
            } else {
                $cache_file = $this->cache_dir . md5($template) . '.php';
            }
            
            if (file_exists($cache_file)) {
                @unlink($cache_file);
            }
        }
    }
    
    /**
     * 递归清除缓存目录
     */
    private function clearCacheDir($dir) {
        if (is_dir($dir)) {
            $files = scandir($dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $path = $dir . '/' . $file;
                    if (is_dir($path)) {
                        $this->clearCacheDir($path);
                    } else {
                        @unlink($path);
                    }
                }
            }
        }
    }
    
    /**
     * 显示模板
     */
    public function display($template) {
        $start_time = $this->enable_profiling ? microtime(true) : 0;
        $start_memory = $this->enable_profiling ? memory_get_usage() : 0;

        // 获取模板文件路径
        $template_file = $this->getTemplatePath($template);

        // 检查模板文件是否存在
        if (!file_exists($template_file)) {
            die('Template file not found: ' . $template_file);
        }

        // 确定缓存文件名
        $cache_file = $this->cache_dir . md5($template_file) . '.php';

        // 添加错误处理
        try {
            // 智能缓存机制：只有在需要时才重新编译
            $compile_start = $this->enable_profiling ? microtime(true) : 0;

            if ($this->needRecompile($template_file, $cache_file)) {
                $this->compile($template_file, $cache_file);
                if ($this->enable_profiling) {
                    $this->performance_data['cache_misses']++;
                }
            } else {
                if ($this->enable_profiling) {
                    $this->performance_data['cache_hits']++;
                }
            }

            if ($this->enable_profiling) {
                $this->performance_data['compile_time'] += (microtime(true) - $compile_start) * 1000;
            }

            // 将模板变量抽取到局部变量，以便模板使用
            extract($this->vars);

            // 引入缓存文件
            include $cache_file;
        } catch (Exception $e) {
            // 如果出现错误，尝试清除缓存并重新编译
            $this->clearCache();
            $this->compile($template_file, $cache_file);

            // 将模板变量抽取到局部变量，以便模板使用
            extract($this->vars);

            // 引入缓存文件
            include $cache_file;
        }

        // 记录性能数据
        if ($this->enable_profiling) {
            $this->performance_data['render_time'] = (microtime(true) - $start_time) * 1000;
            $this->performance_data['memory_usage'] = (memory_get_usage() - $start_memory) / 1024;
        }
    }
    
    /**
     * 获取模板内容
     */
    public function fetch($template) {
        ob_start();
        $this->display($template);
        $content = ob_get_contents();
        ob_end_clean();
        return $content;
    }

    /**
     * 预编译模板目录中的所有模板文件
     */
    public function precompileTemplates($template_dir = null) {
        $template_dir = $template_dir ?: $this->template_dir;
        $compiled_count = 0;
        $errors = array();

        if (!is_dir($template_dir)) {
            return array('count' => 0, 'errors' => array('模板目录不存在: ' . $template_dir));
        }

        // 递归查找所有模板文件
        $template_files = $this->findTemplateFiles($template_dir);

        foreach ($template_files as $template_file) {
            try {
                // 获取相对路径作为模板名
                $template_name = str_replace($template_dir, '', $template_file);
                $template_name = ltrim($template_name, '/\\');

                // 生成缓存文件路径
                $cache_file = $this->cache_dir . md5($template_file) . '.php';

                // 编译模板
                if ($this->compile($template_file, $cache_file)) {
                    $compiled_count++;
                } else {
                    $errors[] = '编译失败: ' . $template_name;
                }
            } catch (Exception $e) {
                $errors[] = '编译错误: ' . $template_name . ' - ' . $e->getMessage();
            }
        }

        return array(
            'count' => $compiled_count,
            'total' => count($template_files),
            'errors' => $errors
        );
    }

    /**
     * 递归查找模板文件
     */
    private function findTemplateFiles($dir, $extensions = array('htm', 'html', 'tpl')) {
        $files = array();

        if (!is_dir($dir)) {
            return $files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());
                if (in_array($extension, $extensions)) {
                    $files[] = $file->getPathname();
                }
            }
        }

        return $files;
    }

    /**
     * 清理过期的编译缓存
     */
    public function cleanExpiredCache() {
        $cleaned_count = 0;

        if (!is_dir($this->cache_dir)) {
            return $cleaned_count;
        }

        $cache_files = glob($this->cache_dir . '*.php');

        foreach ($cache_files as $cache_file) {
            // 检查缓存文件是否过期
            if ($this->cache_expire > 0) {
                $cache_age = time() - filemtime($cache_file);
                if ($cache_age > $this->cache_expire) {
                    if (@unlink($cache_file)) {
                        $cleaned_count++;
                    }
                }
            }
        }

        return $cleaned_count;
    }
    
    /**
     * 日期格式化修饰符
     * 将时间戳格式化为指定格式的日期字符串
     * 
     * 用法: {$timestamp|date_format:'Y-m-d H:i:s'}
     * 
     * @param int $timestamp 时间戳
     * @param string $format 日期格式
     * @return string 格式化后的日期字符串
     */
    public static function date_format($timestamp, $format = 'Y-m-d H:i:s') {
        return date($format, (int)$timestamp);
    }

    /**
     * 友好时间格式化修饰符
     * 将时间戳转换为友好的时间表示，如"刚刚"、"5分钟前"、"2小时前"等
     * 
     * 用法: {$timestamp|format_time}
     * 
     * @param int $timestamp 时间戳
     * @return string 友好的时间表示
     */
    public static function format_time($timestamp) {
        $diff = time() - (int)$timestamp;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) { // 30天内
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', (int)$timestamp);
        }
    }

    /**
     * 数字格式化修饰符
     * 将数字格式化为指定小数位数的字符串
     * 
     * 用法: {$number|number_format:2}
     * 
     * @param float $number 要格式化的数字
     * @param int $decimals 小数位数
     * @return string 格式化后的数字字符串
     */
    public static function number_format($number, $decimals = 0) {
        return number_format($number, $decimals, '.', ',');
    }

    /**
     * 字符串截取修饰符
     * 将字符串截取为指定长度，超出部分用省略号表示
     * 
     * 用法: {$string|truncate:20}
     * 
     * @param string $string 要截取的字符串
     * @param int $length 截取长度
     * @param string $etc 省略符号
     * @return string 截取后的字符串
     */
    public static function truncate($string, $length = 80, $etc = '...') {
        if (mb_strlen($string, 'UTF-8') <= $length) {
            return $string;
        }
        
        return mb_substr($string, 0, $length, 'UTF-8') . $etc;
    }

    /**
     * HTML转义修饰符
     * 将字符串中的HTML特殊字符转换为HTML实体
     * 
     * 用法: {$string|escape}
     * 
     * @param string $string 要转义的字符串
     * @return string 转义后的字符串
     */
    public static function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * 金额格式化修饰符
     * 将数字格式化为金额形式，默认保留2位小数
     * 
     * 用法: {$price|money}
     * 
     * @param float $price 要格式化的金额
     * @param int $decimals 小数位数
     * @param string $prefix 金额前缀（如¥、$等）
     * @return string 格式化后的金额字符串
     */
    public static function money($price, $decimals = 2, $prefix = '¥') {
        return $prefix . number_format($price, $decimals, '.', ',');
    }

    /**
     * 友好文件大小修饰符
     * 将字节数转换为友好的文件大小表示，如KB、MB等
     * 
     * 用法: {$bytes|filesize}
     * 
     * @param int $bytes 字节数
     * @return string 友好的文件大小表示
     */
    public static function filesize($bytes) {
        $size = $bytes;
        $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');
        
        for ($i = 0; $size >= 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, 2) . ' ' . $units[$i];
    }
}