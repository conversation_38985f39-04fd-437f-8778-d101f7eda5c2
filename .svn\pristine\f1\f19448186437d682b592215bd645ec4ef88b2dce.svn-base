/* 面包屑导航样式 - 扁平化设计 */
.breadcrumb-container {
    margin: 0px 0 10px 0;
    padding: 10px;
    border: none;
    background-color: #fff;
 
    width: 100%;
    box-sizing: border-box;
    margin-top: 10px;
}

.breadcrumb {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #EE3131;
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc;
}

/* 新闻页面布局 - 两栏布局 */
.news-container {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

/* 左侧分类导航 - 大气现代化设计 */
.news-sidebar {
    width: 220px;
    flex-shrink: 0;
}

/* 中间新闻列表 - 大气现代化设计 */
.news-main {
    flex: 1;
    background: #fff;
    padding: 0;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.04);
    overflow: hidden;
}

/* 栏目标题样式 - 融合在顶部 */
.category-header {
    margin: 0;
    padding: 20px 30px 0 30px;
    background: #fff;
    border-radius: 16px 16px 0 0;
}

.category-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.3;
    position: relative;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #EE3131, #ff6b6b);
    border-radius: 2px;
}

/* 左侧分类导航容器 - 现代化卡片设计 */
.category-nav {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.06);
    overflow: hidden;
}

.category-list {
    list-style: none;
    margin: 0;
    padding: 8px 0;
}

.category-item {
    position: relative;
    margin: 0 8px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 2px;
}

.category-item:last-child {
    margin-bottom: 0;
}

.category-item a {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    color: #4a5568;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    line-height: 1.5;
    position: relative;
    border-radius: 8px;
    background: transparent;
}

.category-item a:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    text-decoration: none;
}

.category-item a .icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 16px;
    line-height: 20px;
    flex-shrink: 0;
    border-radius: 6px;
    background: rgba(102, 126, 234, 0.1);
}

.category-item a:hover .icon {
    background: rgba(255, 255, 255, 0.2);
}

/* 使用现代化图标字体 */
.category-item a .icon:before {
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    font-family: 'Font Awesome 5 Free', 'Font Awesome 5 Pro', sans-serif;
    font-weight: 900;
    font-size: 14px;
    line-height: 16px;
    color: #667eea;
}

.category-item a:hover .icon:before {
    color: #fff;
}

/* 各个导航项的现代化图标 */
.category-item:nth-child(1) .icon:before { content: '\f013'; } /* 设置图标 */
.category-item:nth-child(2) .icon:before { content: '\f525'; } /* 火焰图标 */
.category-item:nth-child(3) .icon:before { content: '\f0ca'; } /* 列表图标 */
.category-item:nth-child(4) .icon:before { content: '\f086'; } /* 对话图标 */
.category-item:nth-child(5) .icon:before { content: '\f15c'; } /* 文档图标 */
.category-item:nth-child(6) .icon:before { content: '\f03e'; } /* 图片图标 */
.category-item:nth-child(7) .icon:before { content: '\f53f'; } /* 设计图标 */
.category-item:nth-child(8) .icon:before { content: '\f1ea'; } /* 新闻图标 */
.category-item:nth-child(9) .icon:before { content: '\f466'; } /* 盒子图标 */
.category-item:nth-child(10) .icon:before { content: '\f3cd'; } /* 手机图标 */
.category-item:nth-child(11) .icon:before { content: '\f109'; } /* 电脑图标 */
.category-item:nth-child(12) .icon:before { content: '\f059'; } /* 问号图标 */

/* 当前激活状态 - 现代化渐变设计 */
.category-item.active a,
.category-item.current a {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.category-item.active a .icon,
.category-item.current a .icon {
    background: rgba(255, 255, 255, 0.2);
}

.category-item.active a .icon:before,
.category-item.current a .icon:before {
    color: #fff;
}

.category-item.active a:hover,
.category-item.current a:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #fff !important;
    
}

/* NEW标签样式 - 现代化设计 */
.category-item .new-tag {
    position: absolute;
    right: 16px;
    top: 50%;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #fff;
    font-size: 11px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* 响应式设计 - 确保在不同屏幕尺寸下的良好显示 */
@media (max-width: 1024px) {
    .news-sidebar {
        width: 200px;
    }

    .category-item a {
        padding: 14px 16px;
        font-size: 14px;
    }

    .category-item a .icon {
        width: 18px;
        height: 18px;
        margin-right: 10px;
    }
}

@media (max-width: 768px) {
    .news-container {
        flex-direction: column;
        gap: 15px;
    }

    .news-sidebar {
        width: 100%;
        order: 2;
    }

    .news-main {
        order: 1;
        padding: 0;
    }

    .category-header {
        padding: 15px 20px 0 20px;
    }

    .news-list {
        padding: 15px 20px 20px 20px;
    }

    .category-nav {
        margin-bottom: 0;
    }

    .category-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px;
    }

    .category-item {
        margin: 0;
        flex: 1;
        min-width: calc(50% - 4px);
    }

    .category-item a {
        padding: 12px 16px;
        justify-content: center;
        text-align: center;
        flex-direction: column;
        gap: 6px;
    }

    .category-item a .icon {
        margin-right: 0;
        margin-bottom: 4px;
    }

    .category-item .new-tag {
        position: static;
        margin-top: 4px;
        align-self: center;
    }

    /* 移动端新闻列表优化 */
    .news-item {
        padding: 20px 0;
        flex-direction: column;
        gap: 12px;
    }

    .news-item:hover {
        margin: 0 -4px;
        padding-left: 4px;
        padding-right: 4px;
    }

    .news-thumb {
        flex: none;
        width: 100%;
        height: 160px;
        align-self: stretch;
    }

    .news-item.has-thumb {
        min-height: auto;
    }

    .news-item h3 {
        font-size: 18px;
    }

    .news-item .meta {
        flex-wrap: wrap;
        gap: 12px;
    }

    .news-filter {
        margin-bottom: 20px;
    }

    .filter-buttons {
        justify-content: center;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .filter-btn {
        padding: 14px 24px;
        font-size: 14px;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .pagination {
        padding: 16px;
    }

    .pagination a, .pagination .current {
        padding: 10px 14px;
        margin: 0 3px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .category-item {
        min-width: 100%;
    }

    .category-item a {
        flex-direction: row;
        justify-content: flex-start;
        text-align: left;
        gap: 0;
    }

    .category-item a .icon {
        margin-right: 12px;
        margin-bottom: 0;
    }

    .category-item .new-tag {
        position: absolute;
        right: 16px;
        top: 50%;
        margin-top: 0;
    }
}

/* 空状态样式 */
.empty-list {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-list p {
    font-size: 16px;
    margin: 0;
}

/* 加载更多按钮 */
.load-more {
    text-align: center;
    margin: 30px 0;
}

.load-more-btn {
    padding: 10px 30px;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    text-decoration: none;
    display: inline-block;
}

.load-more-btn:hover {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

/* 筛选选项卡样式 - 选项卡形式 */
.news-filter {
    margin-bottom: 30px;
    background: #fff;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.filter-buttons {
    display: flex;
    gap: 0;
    border-bottom: 1px solid #e9ecef;
}

.filter-btn {
    padding: 16px 32px;
    background: transparent;
    color: #666;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-bottom: 3px solid transparent;
    position: relative;
    cursor: pointer;
}

.filter-btn:hover {
    color: #333;
    background: rgba(102, 126, 234, 0.05);
}

.filter-btn.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    border-bottom-color: #667eea;
    font-weight: 700;
}

.filter-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 20px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

/* 新闻列表容器 */
.news-list {
    padding: 20px 30px 30px 30px;
}

/* 新闻列表样式 - 统一列表设计 */
.news-item {
    display: flex;
    padding: 24px 0;
    border-bottom: 1px solid rgba(0,0,0,0.06);
    position: relative;
    gap: 20px;
    align-items: flex-start;
    transition: all 0.2s ease-out;
}

.news-item:first-child {
    padding-top: 0;
}

.news-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.news-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    border-radius: 12px;
    margin: 0 -4px;
    padding-left: 4px;
    padding-right: 4px;
}

/* 左侧缩略图样式 - 与内容高度对齐 */
.news-thumb {
    flex: 0 0 120px;
    width: 120px;
    height: 110px;
    overflow: hidden;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.news-thumb::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    z-index: 1;
}

.news-thumb:hover::before {
    opacity: 1;
}

.news-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.news-thumb:hover img {
}

/* 新闻内容区域 */
.news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* 有缩略图的新闻项 */
.news-item.has-thumb {
    min-height: 110px;
}

/* 无缩略图的新闻项 */
.news-item.no-thumb {
    padding-left: 0;
}

/* 移除左侧边框效果 */

/* 通用新闻元素样式 - 醒目大黑标题 */
.news-item h3 {
    font-size: 22px;
    margin-bottom: 12px;
    font-weight: 900;
    line-height: 1.4;
    letter-spacing: -0.01em;
}

.news-item h3 a {
    color: #000;
    text-decoration: none;
    position: relative;
    display: inline-block;
    font-weight: 900;
    transition: color 0.2s ease-out;
}

.news-item h3 a:hover {
    color: #333;
}

.news-item .meta {
    color: #718096;
    font-size: 14px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 20px;
    font-weight: 500;
}

.news-item .meta span {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 16px;
}

.news-item .meta span::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.6;
}

.news-item .meta span:first-child::before {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.news-item .meta span:nth-child(2)::before {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.news-item .meta span:nth-child(3)::before {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.news-item .desc {
    color: #9ca3af;
    font-size: 15px;
    line-height: 1.6;
    margin-top: 0;
    font-weight: 400;
    letter-spacing: 0.01em;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 分页样式 - 简洁不变形设计 */
.pagination {
    margin-top: 30px;
    text-align: center;
    padding: 20px 0;
    background: #fff;
    border-top: 1px solid #e9ecef;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 3px;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    background: #fff;
    min-width: 32px;
    text-align: center;
    vertical-align: middle;
}

.pagination a:hover {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
    
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination .current {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
    font-weight: 600;
}

.pagination .prev, .pagination .next {
    font-weight: 600;
    padding: 6px 16px;
}

.pagination .prev:hover, .pagination .next:hover {
    background: #5a6fd8;
    border-color: #5a6fd8;
}
/* 右侧热门信息样式 */
.hot-news {
    background: #fff;
    padding: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    border-radius: 4px;
}

.hot-news h2 {
    font-size: 18px;
    color: #333;
    padding-bottom: 15px;
    border-bottom: 2px solid #EE3131;
    margin-bottom: 10px;
    font-weight: 600;
}

.hot-news-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.hot-news-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.hot-news-item img {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 12px;
}

.hot-news-item .info {
    flex: 1;
    overflow: hidden;
}

.hot-news-item .info h4 {
    font-size: 15px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.hot-news-item .info h4 a {
    color: #333;
    text-decoration: none;
}

.hot-news-item .info h4 a:hover {
    color: #EE3131;
}

.hot-news-item .info p {
    color: #999;
    font-size: 12px;
}

/* 热门新闻列表样式 */
.hot-news-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.hot-news-list li {
    margin-bottom: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

.hot-news-list li:last-child {
    margin-bottom: 0;
}

.hot-news-list li a {
    color: #333;
    text-decoration: none;
    font-size: 14px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hot-news-list li a:hover {
    color: #EE3131;
}

.hot-news-list .num {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background: #ddd;
    color: #fff;
    font-size: 12px;
    margin-right: 10px;
    border-radius: 3px;
    float: left;
}

.hot-news-list li:nth-child(1) .num,
.hot-news-list li:nth-child(2) .num,
.hot-news-list li:nth-child(3) .num {
    background: #EE3131;
}

/* 右侧模块样式 */
.bbs-hot {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px 10px 10px;

}

.bbs-hot img {
    width: 100%;
    height: auto;
    margin: 5px 0;
    box-sizing: border-box;
    display: block;
}

/* 新闻详情页样式 */
/* 新闻详情头部 */
.news-detail-header {
    margin-bottom: 20px;

}

/* 旧的新闻详情样式已移除，使用.news-detail-main下的新样式 */

/* 新闻正文 */
.news-detail-content {
    line-height: 1.8; 
    color: #333; 
    font-size: 16px;
    white-space: pre-wrap; /* 保留空白符和换行符 */
}

.news-detail-content p {
    margin-bottom: 15px; 
}

.news-detail-content p:last-child {
    margin-bottom: 0;
}

.news-detail-img {
    margin: 20px 0;
    text-align: center;
}

.news-detail-img img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.img-caption {
    text-align: center;
    color: #999;
    font-size: 13px;
    margin-top: 10px;
}



/* 上一篇下一篇导航 */
.news-detail-nav {
    margin: 30px 0;
    border-top: 1px dashed #eee;
    border-bottom: 1px dashed #eee;
    padding: 20px 0;
}

.prev-next-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 30px;
}

.prev-article, .next-article {
    flex: 1;
    max-width: 48%;
}

.prev-article {
    text-align: left;
}

.next-article {
    text-align: right;
}

.nav-label {
    color: #666;
    font-weight: 600;
    font-size: 14px;
    display: block;
    margin-bottom: 8px;
}

.nav-title {
    color: #333;
    text-decoration: none;
    font-size: 15px;
    line-height: 1.5;
    display: block;
    word-break: break-all;
    transition: color 0.3s ease;
}

.nav-title:hover {
    color: #EE3131;
}

.nav-empty {
    color: #999;
    font-size: 14px;
}

/* 相关推荐 */
.news-detail-related {
    margin-top: 30px;
}

.related-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    position: relative;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.related-title span {
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 2px solid #EE3131;
    position: relative;
    bottom: -1px;
}

.related-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.related-list li {
    position: relative;
    padding-left: 15px;
    margin-bottom: 12px;
    line-height: 1.6;
}

.related-list li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 8px;
    width: 6px;
    height: 6px;
    background: #EE3131;
    border-radius: 50%;
}

.related-list li a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
}

.related-list li a:hover {
    color: #EE3131;
}

/* 右侧广告 */
.side-ad {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.side-ad img {
    display: block;
    width: 100%;
    height: auto;
}

/* 旧的响应式样式已移除，使用.news-detail-main下的新样式 */

/* 新闻焦点区域样式 */
.news-focus {
    background: #fff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.focus-slide {
    display: flex;
    margin-top: 15px;
    gap: 20px;
}

.focus-img {
    flex: 1.5;
    position: relative;
    height: 280px;
    overflow: hidden;
    border-radius: 4px;
}

.focus-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.focus-img img:hover {
    
}

.focus-img-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 15px;
    background: rgba(0,0,0,0.6);
    color: #fff;
}

.focus-img-title h3 {
    margin: 0;
    font-size: 16px;
    font-weight: normal;
}

.focus-list {
    flex: 1;
}

.focus-list-item {
    padding: 10px 0;
    border-bottom: 1px dashed #eee;
}

.focus-list-item:last-child {
    border-bottom: none;
}

.focus-list-item h3 {
    font-size: 15px;
    margin: 0 0 5px 0;
    font-weight: normal;
}

.focus-list-item h3 a {
    color: #333;
    text-decoration: none;
}

.focus-list-item h3 a:hover {
    color: #EE3131;
}

.focus-list-item .meta {
    color: #999;
    font-size: 12px;
}

.focus-list-item .meta span {
    margin-right: 10px;
}

/* 栏目新闻样式 */
.cat-news {
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.cat-news-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 15px;
}

.cat-news-item {
    flex: 0 0 calc(50% - 10px);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #eee;
}

.cat-news-item:nth-child(odd) {
    margin-right: 20px;
}

.cat-news-item h3 {
    font-size: 15px;
    margin: 0 0 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: normal;
}

.cat-news-item h3 a {
    color: #333;
    text-decoration: none;
}

.cat-news-item h3 a:hover {
    color: #EE3131;
}

.cat-news-item .meta {
    color: #999;
    font-size: 12px;
}

.cat-news-item .meta span {
    margin-right: 10px;
}

/* 通用标题样式 */
.news-title {
    font-size: 20px;
    margin-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.news-title h3 {
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    padding-bottom: 10px;
    color: #333;
    border-bottom: 2px solid #EE3131;
    margin-bottom: -2px;
}

.news-title .more {
    position: absolute;
    right: 0;
    bottom: 10px;
    font-size: 14px;
    color: #999;
    text-decoration: none;
}

.news-title .more:hover {
    color: #EE3131;
}

/* 新闻焦点区域 */
.news-focus {
    margin-bottom: 30px;
    background: none;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
}

.focus-content {
    display: flex;
    gap: 30px;
}

.focus-main {
    flex: 2;
    position: relative;
    height: 360px;
    overflow: hidden;
}

.focus-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.focus-main-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px 20px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
}

.focus-main-title h3 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

.focus-list {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.focus-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.focus-item:last-child {
    border-bottom: none;
}

.focus-item a {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    margin-bottom: 8px;
    line-height: 1.4;
}

.focus-item a:hover {
    color: #EE3131;
}

.item-meta {
    color: #999;
    font-size: 13px;
}

.item-meta span {
    margin-right: 15px;
}

/* 新闻区块样式 */
.news-block {
    margin-bottom: 30px;
    padding-bottom: 20px;
}

.news-list {
    border-top: none;
}

/* 头条新闻 */
.headline-news {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.headline-img {
    width: 240px;
    height: 160px;
    margin-right: 20px;
    overflow: hidden;
}

.headline-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.headline-info {
    flex: 1;
}

.headline-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: bold;
}

.headline-info h3 a {
    color: #333;
    text-decoration: none;
}

.headline-info h3 a:hover {
    color: #EE3131;
}

.headline-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-top: 10px;
}

/* 列表新闻 */
.news-item {
    padding: 10px 0;
    border-bottom: 1px solid #f7f7f7;
}

.news-item:last-child {
    border-bottom: none;
}

.news-item h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: normal;
}

.news-item h3 a {
    color: #333;
    text-decoration: none;
}

.news-item h3 a:hover {
    color: #EE3131;
}

/* 右侧模块通用样式 */
.bbs-hot {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px 10px 10px;
}

.yui-h-title {
    position: relative;
    font-size: 18px;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

/* 面包屑导航样式 - 扁平化设计 */

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .focus-content {
        flex-direction: column;
    }
    
    .focus-main {
        height: 320px;
    }
    
    .headline-img {
        width: 200px;
        height: 140px;
    }
}

@media screen and (max-width: 768px) {
    .news-container {
        flex-direction: column;
    }
    
    .news-left,
    .news-right {
        width: 100%;
    }
    
    .headline-news {
        flex-direction: column;
    }
    
    .headline-img {
        width: 100%;
        height: auto;
        margin-right: 0;
        margin-bottom: 15px;
    }
}

/* 置顶和推荐标签样式 - 现代化设计 */
.top-tag, .rec-tag {
    display: inline-block;
    padding: 6px 12px;
    font-size: 12px;
    color: #fff;
    margin-left: 12px;
    border-radius: 20px;
    vertical-align: middle;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.top-tag::before, .rec-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.top-tag:hover::before, .rec-tag:hover::before {
    left: 100%;
}

.top-tag {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.rec-tag {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.top-tag:hover {
    
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.rec-tag:hover {
    
    box-shadow: 0 4px 15px rgba(254, 202, 87, 0.4);
}

/* 分页样式 */
.pagebar {
    margin: 20px 0;
    text-align: center;
    font-size: 0;
}

.pagebar a, .pagebar span {
    display: inline-block;
    padding: 4px 8px; /* 更小的内边距 */
    font-size: 12px;  /* 更小的字体 */
    margin: 0 3px;
    border: 1px solid #e5e5e5;
    background: #fff;
    color: #333;
    text-decoration: none;
    vertical-align: middle;
}

.pagebar a:hover {
    background: #f5f5f5;
    color: #EE3131;
    border-color: #EE3131;
}

.pagebar span.current {
    background: #EE3131;
    color: #fff;
    border-color: #EE3131;
}

.pagebar .disabled {
    color: #999;
    cursor: not-allowed;
}

/* 旧版兼容 - 对应传统简单分页 */
.pagination {
    margin: 20px 0;
    text-align: center;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 4px 8px; /* 更小的内边距 */
    font-size: 12px;  /* 更小的字体 */
    margin: 0 3px;
    border: 1px solid #e5e5e5;
    background: #fff;
    color: #333;
    text-decoration: none;
}

.pagination a:hover {
    background: #f5f5f5;
    color: #EE3131;
    border-color: #EE3131;
}

.pagination span.current {
    background: #EE3131;
    color: #fff;
    border-color: #EE3131;
}

/* 新闻详情页样式 - 大气简洁设计 */
.news-detail-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0;
}

.news-detail-main {
    background: #fff;
    padding: 20px 60px 40px 60px;
    margin-bottom: 20px;
}

/* 新闻详情标题 - 大气简洁 */
.news-detail-main .news-detail-title {
    font-size: 28px;
    color: #000;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 15px;
    text-align: center;
    padding-bottom: 0;
}

/* 新闻详情元信息 - 简洁设计 */
.news-detail-main .news-detail-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin-bottom: 20px;
    padding: 10px 0 15px 0;
    border-bottom: 1px solid #f0f0f0;
    flex-wrap: wrap;
}

.news-detail-main .news-detail-meta span {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.news-detail-main .news-detail-meta i {
    margin-right: 6px;
    color: #999;
    font-size: 14px;
}



/* 新闻详情正文 - 简洁排版 */
.news-detail-main .news-detail-content {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
}

.news-detail-main .news-detail-content p {
    margin-bottom: 18px;
    text-indent: 2em;
}

.news-detail-main .news-detail-content p:first-child {
    text-indent: 0;
    font-size: 17px;
    font-weight: 600;
    color: #222;
}

/* 新闻详情图片 - 简洁展示 */
.news-detail-main .news-detail-img {
    margin: 30px 0;
    text-align: center;
}

.news-detail-main .news-detail-img img {
    max-width: 100%;
    height: auto;
    border: 1px solid #e9ecef;
}

.news-detail-main .img-caption {
    margin-top: 10px;
    color: #999;
    font-size: 13px;
    font-style: italic;
}



/* 响应式设计 */
@media (max-width: 1240px) {
    .news-detail-container {
        width: 100%;
        padding: 0 20px;
    }
}

@media (max-width: 1024px) {
    .news-detail-main {
        padding: 15px 40px 30px 40px;
    }

    .news-detail-main .news-detail-title {
        font-size: 24px;
        margin-bottom: 12px;
    }

    .news-detail-main .news-detail-meta {
        margin-bottom: 18px;
    }

    .news-detail-main .news-detail-content {
        font-size: 15px;
    }
}

@media (max-width: 768px) {
    .news-detail-container {
        padding: 0 15px;
    }

    .news-detail-main {
        padding: 15px 20px 25px 20px;
    }

    .news-detail-main .news-detail-title {
        font-size: 22px;
        text-align: left;
        margin-bottom: 10px;
    }

    .news-detail-main .news-detail-meta {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
        padding: 8px 0 12px 0;
    }

    .news-detail-main .news-detail-content {
        font-size: 15px;
    }
}