<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<!-- 页面标题 -->
<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-link"></i> 友情链接管理</h1>
        <div class="page-subtitle">管理网站友情链接</div>
    </div>
    <div class="page-actions">
        <button type="button" class="btn btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus"></i> 添加友情链接
        </button>
        <button type="button" class="btn btn-danger" onclick="batchDelete()" style="display: none;" id="batch-delete-btn">
            <i class="fas fa-trash"></i> 批量删除
        </button>
    </div>
</div>

<!-- 友情链接列表 -->
<div class="card">
    <div class="table-responsive">
        <table class="table table-enhanced">
            <thead>
                <tr>
                    <th width="40" class="text-center">
                        <input type="checkbox" id="select-all">
                    </th>
                    <th width="60" class="text-center">ID</th>
                    <th>链接名称</th>
                    <th>链接地址</th>
                    <th>描述</th>
                    <th width="80" class="text-center">排序</th>
                    <th width="80" class="text-center">状态</th>
                    <th width="100" class="text-center">打开方式</th>
                    <th width="120" class="text-center">创建时间</th>
                    <th width="180" class="text-center">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(!$links): ?>
                <tr>
                    <td colspan="10" class="text-center" style="padding: 60px 20px; color: #999;">
                        <i class="fas fa-link" style="font-size: 48px; margin-bottom: 16px; color: #ddd;"></i>
                        <div style="font-size: 18px; margin-bottom: 8px;">暂无友情链接</div>
                        <div style="font-size: 14px;">点击上方按钮添加第一个友情链接</div>
                    </td>
                </tr>
                <?php else: ?>
                <?php if(null !== ($links ?? null) && is_array($links)): foreach($links as $item): ?>
                <tr class="table-row">
                    <td class="text-center">
                        <input type="checkbox" name="ids[]" value="<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>">
                    </td>
                    <td class="text-center table-id"><?php echo (isset($item['id'])) ? $item['id'] : ""; ?></td>
                    <td>
                        <div class="table-title">
                            <a href="<?php echo (isset($item['url'])) ? $item['url'] : ""; ?>" target="<?php echo (isset($item['target'])) ? $item['target'] : ""; ?>" class="title-link"><?php echo (isset($item['name'])) ? $item['name'] : ""; ?></a>
                        </div>
                    </td>
                    <td>
                        <a href="<?php echo (isset($item['url'])) ? $item['url'] : ""; ?>" target="<?php echo (isset($item['target'])) ? $item['target'] : ""; ?>" class="link-url" title="<?php echo (isset($item['url'])) ? $item['url'] : ""; ?>">
                            <?php echo mb_strlen($item['url']) > 40 ? mb_substr($item['url'], 0, 40) . '...' : $item['url']; ?>
                        </a>
                    </td>
                    <td>
                        <?php if($item['description']): ?>
                            <?php echo mb_strlen($item['description']) > 30 ? mb_substr($item['description'], 0, 30) . '...' : $item['description']; ?>
                        <?php else: ?>
                            <span class="text-muted">--</span>
                        <?php endif; ?>
                    </td>
                    <td class="text-center table-number"><?php echo (isset($item['sort_order'])) ? $item['sort_order'] : ""; ?></td>
                    <td class="text-center">
                        <?php if($item['status'] == 1): ?>
                        <span class="status-tag success">启用</span>
                        <?php else: ?>
                        <span class="status-tag danger">禁用</span>
                        <?php endif; ?>
                    </td>
                    <td class="text-center">
                        <?php if($item['target'] == '_blank'): ?>
                        <span class="badge bg-info">新窗口</span>
                        <?php else: ?>
                        <span class="badge bg-secondary">当前窗口</span>
                        <?php endif; ?>
                    </td>
                    <td class="text-center table-time">
                        <?php echo date('m-d H:i', $item['created_at']); ?>
                    </td>
                    <td>
                        <div class="action-buttons-enhanced">
                            <a href="javascript:void(0)" onclick="showEditModal(<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>)" class="btn-action btn-edit">
                                <i class="fas fa-edit"></i>
                                <span>编辑</span>
                            </a>
                            <a href="links.php?ajax=1&action=toggle_status&id=<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>" class="btn-action btn-<?php if($item['status'] == 1): ?>warning<?php else: ?>success<?php endif; ?>" onclick="return confirm('确定要<?php if($item['status'] == 1): ?>禁用<?php else: ?>启用<?php endif; ?>这个友情链接吗？')">
                                <i class="fas fa-<?php if($item['status'] == 1): ?>eye-slash<?php else: ?>eye<?php endif; ?>"></i>
                                <span><?php if($item['status'] == 1): ?>禁用<?php else: ?>启用<?php endif; ?></span>
                            </a>
                            <a href="links.php?ajax=1&action=delete&id=<?php echo (isset($item['id'])) ? $item['id'] : ""; ?>" class="btn-action btn-delete btn-confirm" onclick="return confirm('确定要删除这个友情链接吗？')">
                                <i class="fas fa-trash"></i>
                                <span>删除</span>
                            </a>
                        </div>
                    </td>
                </tr>
                <?php endforeach; endif; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <?php if($links && $pagination['total_pages'] > 1): ?>
    <div class="card-footer">
        <div class="pagination-wrapper">
            <div class="pagination-info">
                共 <?php echo (isset($pagination['total_items'])) ? $pagination['total_items'] : ""; ?> 条记录
            </div>
            <div class="pagination">
                <?php if($pagination['has_prev']): ?>
                <a href="links.php?page=<?php echo (isset($pagination['prev_page'])) ? $pagination['prev_page'] : ""; ?>" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
                
                <?php 
                for ($i = 1; $i <= $pagination['total_pages']; $i++) {
                    if ($i == $pagination['current_page']) {
                        echo '<span class="pagination-btn active">' . $i . '</span>';
                    } else {
                        echo '<a href="links.php?page=' . $i . '" class="pagination-btn">' . $i . '</a>';
                    }
                }
                 ?>
                
                <?php if($pagination['has_next']): ?>
                <a href="links.php?page=<?php echo (isset($pagination['next_page'])) ? $pagination['next_page'] : ""; ?>" class="pagination-btn">
                    <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 添加友情链接模态框 -->
<div class="modal" id="addModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4><i class="fas fa-plus"></i> 添加友情链接</h4>
            <button type="button" class="modal-close" onclick="closeModal('addModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="addForm">
                <div class="form-group">
                    <label class="form-label">链接名称 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="text" name="name" class="form-control" placeholder="请输入链接名称" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接地址 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="url" name="url" class="form-control" placeholder="https://www.example.com" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接描述</label>
                    <div class="form-field">
                        <textarea name="description" class="form-control form-textarea" rows="3" placeholder="请输入链接描述（可选）"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">排序</label>
                    <div class="form-field">
                        <input type="number" name="sort_order" class="form-control" value="0" min="0" placeholder="数字越小排序越靠前">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">打开方式</label>
                    <div class="form-field">
                        <select name="target" class="form-control form-select">
                            <option value="_blank">新窗口打开</option>
                            <option value="_self">当前窗口打开</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div class="form-field">
                        <select name="status" class="form-control form-select">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeModal('addModal')">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitAddForm()">保存</button>
        </div>
    </div>
</div>

<!-- 编辑友情链接模态框 -->
<div class="modal" id="editModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4><i class="fas fa-edit"></i> 编辑友情链接</h4>
            <button type="button" class="modal-close" onclick="closeModal('editModal')">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editForm">
                <input type="hidden" name="id" id="edit_id">
                
                <div class="form-group">
                    <label class="form-label">链接名称 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接地址 <span class="required">*</span></label>
                    <div class="form-field">
                        <input type="url" name="url" id="edit_url" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接描述</label>
                    <div class="form-field">
                        <textarea name="description" id="edit_description" class="form-control form-textarea" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">排序</label>
                    <div class="form-field">
                        <input type="number" name="sort_order" id="edit_sort_order" class="form-control" min="0" placeholder="数字越小排序越靠前">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">打开方式</label>
                    <div class="form-field">
                        <select name="target" id="edit_target" class="form-control form-select">
                            <option value="_blank">新窗口打开</option>
                            <option value="_self">当前窗口打开</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <div class="form-field">
                        <select name="status" id="edit_status" class="form-control form-select">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeModal('editModal')">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitEditForm()">保存</button>
        </div>
    </div>
</div>

<script>
// 显示添加模态框
function showAddModal() {
    document.getElementById('addModal').style.display = 'flex';
}

// 显示编辑模态框
function showEditModal(id) {
    fetch('links.php?ajax=1&action=get&id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 填充表单数据
                document.getElementById('edit_id').value = data.data.id;
                document.getElementById('edit_name').value = data.data.name;
                document.getElementById('edit_url').value = data.data.url;
                document.getElementById('edit_description').value = data.data.description || '';
                document.getElementById('edit_sort_order').value = data.data.sort_order || 0;
                document.getElementById('edit_target').value = data.data.target || '_blank';
                document.getElementById('edit_status').value = data.data.status || 1;

                // 显示模态框
                document.getElementById('editModal').style.display = 'flex';
            } else {
                alert('获取数据失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            alert('请求失败：' + error.message);
        });
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 提交添加表单
function submitAddForm() {
    const form = document.getElementById('addForm');
    const formData = new FormData(form);

    fetch('links.php?ajax=1&action=add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('添加成功！');
            location.reload();
        } else {
            alert('添加失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 提交编辑表单
function submitEditForm() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);

    fetch('links.php?ajax=1&action=edit', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 批量删除
function batchDelete() {
    const checkboxes = document.querySelectorAll('input[name="ids[]"]:checked');
    if (checkboxes.length === 0) {
        alert('请选择要删除的项目');
        return;
    }

    if (!confirm('确定要删除选中的 ' + checkboxes.length + ' 个友情链接吗？此操作不可恢复！')) {
        return;
    }

    const ids = Array.from(checkboxes).map(cb => cb.value);

    fetch('links.php?ajax=1&action=batch_delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ids: ids})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('删除成功！');
            location.reload();
        } else {
            alert('删除失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('请求失败：' + error.message);
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('input[name="ids[]"]');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');

    // 全选/取消全选
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBatchDeleteBtn();
        });
    }

    // 单个复选框变化
    checkboxes.forEach(cb => {
        cb.addEventListener('change', function() {
            updateBatchDeleteBtn();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('input[name="ids[]"]:checked').length;
            if (selectAll) {
                selectAll.checked = checkedCount === checkboxes.length;
                selectAll.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            }
        });
    });

    // 更新批量删除按钮显示状态
    function updateBatchDeleteBtn() {
        const checkedCount = document.querySelectorAll('input[name="ids[]"]:checked').length;
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = checkedCount > 0 ? 'inline-flex' : 'none';
        }
    }
});

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
