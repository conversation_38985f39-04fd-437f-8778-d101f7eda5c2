<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>泊头生活网 - 发布信息</title>
    <meta name="keywords" content="泊头生活网,泊头信息网,泊头信息港,泊头生活信息网站" />
    <meta name="description" content="泊头生活网(泊头信息网)，河北泊头生活信息网站。" />
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/post.css?v=20250801">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/image-compress.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <script src="/template/pc/js/jquery.min.js"></script>

</head>
<body>
    <!-- 顶部 -->
    <div class="yui-top yui-1200">
    <div class="yui-top-center">
        <div class="yui-top-left yui-left">
            <a href="/">网站首页</a>
            <a href="#">移动版</a>
            <a href="#">微信公众号</a>
            <a href="#">快速发布</a>
        </div>

        <div class="yui-top-right yui-right yui-text-right">
            <a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">会员中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">我的信息</a></li>
                    <li><a href="#">我的收藏</a></li>
                    <li><a href="#">账号设置</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">商家中心</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">商家入驻</a></li>
                    <li><a href="#">商家管理</a></li>
                    <li><a href="#">营销推广</a></li>
                </ul>
            </div><div class="yui-top-dropdown">
                <span class="yui-top-dropdown-btn">网站导航</span>
                <ul class="yui-top-dropdown-menu">
                    <li><a href="#">关于我们</a></li>
                    <li><a href="#">联系我们</a></li>
                    <li><a href="#">使用帮助</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 简洁头部 -->
<div class="simple-header">
    <div class="simple-header-inner">
        <div class="logo-title-group">
            <div class="simple-logo">
                <a href="/"><img src="/template/pc/images/logo.png" alt="泊头生活网"></a>
            </div>
            <span class="title-separator"></span>
            <h1 class="simple-title">选择栏目</h1>
        </div>
        <a href="/" class="simple-back">返回网站首页</a>
    </div>
</div>

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="content-wrap" style="width: 100%;">
            <!-- 左侧表单区域 -->
            <div class="left-column" style="width: 100%;">
                <div class="pd10">
                    <div class="yui-h-title">
                        <h3>发布信息</h3><span></span>
                    </div>
                </div>
                <div class="posting-notice">
                    <h4 class="notice-title">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>发布须知</span>
                    </h4>
                    <div class="notice-content">
                        <p>每天限发1条信息,三天内不允许发重复信息</p>
                        <p>采用信息审核制,须经审核才会显示,应遵守法律法规</p>
                        <p>后台审核信息时可能会对部分字词句进行调整</p>
                        <p class="highlight">如有图片，请上传自己拍摄的图片，盗图或使用字体侵权风险自担</p>
                        <p>招聘不得限定男女性别,涉嫌违法一律不予通过</p>
                        <p>如继续发布提交信息,视为您已知晓并同意该协议</p>
                        <p>请认真阅读本页"发布须知"，以及<a href="/help/statement.html" target="_blank" class="agreement-link">网站声明</a>、<a href="/help/review.html" target="_blank" class="agreement-link">审核条例</a>文档内容</p>
                        <p class="highlight">点击"提交发布"即代表你已阅读并同意我们的<a href="/help/service.html" target="_blank" class="agreement-link">服务条款</a>!</p>
                    </div>
                </div>
                <form id="post-form" action="/post.php<?php if(null !== ($category ?? null) && null !== ($category ?? null) && !empty($category)): ?>?category_id=<?php echo (isset($category['id'])) ? $category['id'] : ""; ?><?php endif; ?>" method="post" enctype="multipart/form-data" novalidate>
                    <?php if(null !== ($category ?? null) && null !== ($category ?? null) && !empty($category)): ?>
                    <input type="hidden" name="category_id" value="<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>">
                    <?php endif; ?>
                    
                    <!-- CSRF令牌 -->
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                     
                    <?php if(null !== ($error_message ?? null) && !empty($error_message)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i><?php echo $error_message ?? ""; ?>
                    </div>
                    <?php endif; ?>

                    <div class="form-panel">
                        <!-- 当前栏目 -->
                        <div class="category-row">
                            <label class="form-label">栏目</label>
                            <div class="category-content">
                                <?php if(null !== ($category ?? null) && is_array($category) && array_key_exists('parent_name', $category)): ?>[<?php echo (isset($category['parent_name'])) ? $category['parent_name'] : ""; ?>] <?php endif; ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?>
                                <a href="post.php" class="change-link">[重选栏目]</a>
                            </div>
                        </div>

                        <!-- 选择地区 -->
                        <div class="form-group required">
                            <label for="region_id" class="form-label">地区</label>
                            <div class="form-right">
                                <div class="custom-select">
                                    <input type="hidden" name="region_id" id="region_id" required>
                                    <div class="select-trigger">请选择地区</div>
                                    <div class="select-dropdown">
                                        <?php if(null !== ($regions ?? null) && is_array($regions)): foreach($regions as $province): ?>
                                        <div class="select-group">
                                            <div class="group-label"><?php echo (isset($province['name'])) ? $province['name'] : ""; ?></div>
                                            <div class="group-options">
                                                <?php if(null !== ($province ?? null) && is_array($province['children'])): foreach($province['children'] as $city): ?>
                                                <div class="select-option" data-value="<?php echo (isset($city['id'])) ? $city['id'] : ""; ?>"><?php echo (isset($city['name'])) ? $city['name'] : ""; ?></div>
                                                <?php endforeach; endif; ?>
                                            </div>
                                        </div>
                                        <?php endforeach; endif; ?>
                                    </div>
                                </div>
                                <span class="inputTip" data-default="请选择您所在的地区">请选择您所在的地区</span>
                            </div>
                        </div>
                        
                        <!-- 信息有效期 -->
                        <div class="form-group required">
                            <label for="expire_days" class="form-label">有效期</label>
                            <div class="form-right">
                                <div class="custom-select">
                                    <input type="hidden" name="expire_days" id="expire_days" value="30" required>
                                    <div class="select-trigger">30天</div>
                                    <div class="select-dropdown">
                                        <div class="select-option" data-value="7">7天</div>
                                        <div class="select-option" data-value="15">15天</div>
                                        <div class="select-option" data-value="30">30天</div>
                                        <div class="select-option" data-value="60">60天</div>
                                        <div class="select-option" data-value="90">90天</div>
                                    </div>
                                </div>
                                <span class="inputTip" data-default="请选择信息的有效期限">请选择信息的有效期限</span>
                            </div>
                        </div>
                        
                        <!-- 标题 -->
                        <div class="form-group required">
                            <label for="title" class="form-label">标题</label>
                            <div class="form-right">
                                <input type="text" name="title" id="title" class="form-input" placeholder="请输入标题" value="<?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('title', $post)): ?><?php echo (isset($post['title'])) ? $post['title'] : ""; ?><?php endif; ?>" required>
                                <span class="inputTip" data-default="请输入简洁明了的标题">请输入简洁明了的标题</span>
                            </div>
                        </div>
                        
                        <!-- 内容 -->
                        <div class="form-group required">
                            <label for="content" class="form-label">详情</label>
                            <div class="form-right">
                                <textarea name="content" id="content" class="form-textarea" placeholder="请输入详细内容" required><?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('content', $post)): ?><?php echo (isset($post['content'])) ? $post['content'] : ""; ?><?php endif; ?></textarea>
                                <span class="inputTip" data-default="请详细描述信息内容，越详细越好"></span>
                            </div>
                        </div>

                        <!-- 图片上传 -->
                        <div class="form-group">
                            <label class="form-label">上传图片</label>
                            <div class="form-right">
                                <div class="image-upload">
                                    <div class="upload-area">
                                        <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                                        <label for="image_uploads" class="upload-btn">
                                            <i class="fas fa-image"></i>
                                            <span>添加图片</span>
                                        </label>
                                        <div class="image-hint">最多可上传<?php echo (isset($upload_config['max_count'])) ? $upload_config['max_count'] : ""; ?>张（选填）<br><span class="warning">请上传非侵权图片和字体图，否则后果自负</span></div>
                                    </div>
                                    <div class="image-previews" id="image-previews"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系人 -->
                        <div class="form-group required">
                            <label for="contact_name" class="form-label">称呼</label>
                            <div class="form-right">
                                <input type="text" name="contact_name" id="contact_name" class="form-input" placeholder="输入称呼" value="<?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_name', $post)): ?><?php echo (isset($post['contact_name'])) ? $post['contact_name'] : ""; ?><?php endif; ?>" required>
                                <span class="inputTip" data-default="请填写能够联系到您的真实姓名">请填写能够联系到您的真实姓名</span>
                            </div>
                        </div>
                        
                        <!-- 联系电话 -->
                        <div class="form-group required">
                            <label for="contact_mobile" class="form-label">手机</label>
                            <div class="form-right">
                                <input type="tel" name="contact_mobile" id="contact_mobile" class="form-input" placeholder="请输入手机号码" value="<?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_mobile', $post)): ?><?php echo (isset($post['contact_mobile'])) ? $post['contact_mobile'] : ""; ?><?php endif; ?>" required>
                                <span class="inputTip" data-default="请填写您的11位手机号码">请填写您的11位手机号码</span>
                            </div>
                        </div>

                        <!-- 详细地址 -->
                        <div class="form-group">
                            <label for="contact_address" class="form-label">地址</label>
                            <div class="form-right">
                                <input type="text" name="contact_address" id="contact_address" class="form-input" placeholder="请输入详细地址（选填）" value="<?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_address', $post)): ?><?php echo (isset($post['contact_address'])) ? $post['contact_address'] : ""; ?><?php endif; ?>">
                                <span class="inputTip" data-default="请填写详细的地址信息（选填）">请填写详细的地址信息（选填）</span>
                            </div>
                        </div>

                        <!-- 微信号 -->
                        <div class="form-group">
                            <label for="contact_weixin" class="form-label">微信</label>
                            <div class="form-right weixin-group">
                                <input type="text" name="contact_weixin" id="contact_weixin" class="form-input" placeholder="微信号（选填）" value="<?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('contact_weixin', $post)): ?><?php echo (isset($post['contact_weixin'])) ? $post['contact_weixin'] : ""; ?><?php endif; ?>">
                                <span class="inputTip" data-default="请填写您的微信号（选填）">请填写您的微信号（选填）</span>
                                <label class="weixin-checkbox">
                                    <input type="checkbox" id="weixin_same"> 与手机相同
                                </label>
                            </div>
                        </div>
                        
                        <!-- 管理密码 -->
                        <div class="form-group required">
                            <label for="password" class="form-label">管理密码</label>
                            <div class="form-right">
                                <input type="password" name="password" id="password" class="form-input" placeholder="请设置管理密码" required>
                                <span class="inputTip" data-default="请设置6位以上的密码，用于后续管理和删除信息">请设置6位以上的密码，用于后续管理和删除信息</span>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-group">
                            <button type="submit" class="submit-button" id="submit-btn">提交发布</button>
                        </div>
                       
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>
    
    <!-- 提交成功提示 -->
    <div class="success-overlay" id="success-overlay">
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <div class="success-text">提交成功！</div>
            <div class="success-subtext">您的信息已提交，正在等待审核</div>
            <button class="success-btn" id="success-btn">确定</button>
        </div>
    </div>

    <!-- 错误提示弹窗 -->
    <div class="error-overlay" id="error-overlay">
        <div class="error-message-popup">
            <i class="fas fa-exclamation-circle"></i>
            <div class="error-text">提交失败</div>
            <div class="error-subtext" id="error-subtext">请检查输入信息后重试</div>
            <button class="error-btn" id="error-btn">确定</button>
        </div>
    </div>

    <!-- 底部 -->
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
<?php echo get_block('footer_nav'); ?>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="/static/js/image-compress.js"></script>
    <script>
    // 简化的图片压缩功能（内嵌版本）
    function SimpleImageCompressor() {
        this.compressFile = function(file) {
            return new Promise((resolve, reject) => {
                if (!file.type.match('image.*')) {
                    reject(new Error('不支持的文件类型'));
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 计算新尺寸
                            let { width, height } = img;
                            const maxWidth = 1920;
                            const maxHeight = 1080;

                            if (width > maxWidth || height > maxHeight) {
                                const ratio = Math.min(maxWidth / width, maxHeight / height);
                                width *= ratio;
                                height *= ratio;
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                const compressedFile = new File([blob], file.name, {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                });

                                resolve({
                                    file: compressedFile,
                                    originalSize: file.size,
                                    compressedSize: blob.size,
                                    compressionRatio: (blob.size / file.size).toFixed(2),
                                    quality: '0.80',
                                    dimensions: { width: Math.round(width), height: Math.round(height) }
                                });
                            }, 'image/jpeg', 0.8);

                        } catch (error) {
                            reject(error);
                        }
                    };
                    img.onerror = () => reject(new Error('图片加载失败'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        };
    }
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        const loadingOverlay = document.getElementById('loading-overlay');
        const successOverlay = document.getElementById('success-overlay');
        const successBtn = document.getElementById('success-btn');
        const errorOverlay = document.getElementById('error-overlay');
        const errorBtn = document.getElementById('error-btn');
        const errorSubtext = document.getElementById('error-subtext');

        // 成功提示框确定按钮事件处理
        successBtn.addEventListener('click', function() {
            window.location.href = '/';
        });

        // 错误提示框确定按钮事件处理
        errorBtn.addEventListener('click', function() {
            errorOverlay.style.display = 'none';
            errorOverlay.classList.remove('show');
        });

        // 显示错误弹窗的函数（全局可用）
        window.showErrorPopup = function(message) {
            errorSubtext.innerHTML = message;
            errorOverlay.style.display = 'flex';
            errorOverlay.classList.add('show');
            // 确保弹窗在最顶层
            errorOverlay.style.zIndex = '10000';
        };
        
        // 发布须知折叠功能
        const noticeTitle = document.querySelector('.notice-title');
        const noticeContent = document.querySelector('.notice-content');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        noticeTitle.addEventListener('click', function() {
            noticeContent.classList.toggle('collapsed');
            toggleIcon.classList.toggle('rotate');
        });
        
        // jQuery表单验证
        $(document).ready(function() {
            // 自定义验证方法 - 手机号
            $.validator.addMethod("isMobile", function(value, element) {
                var mobile = /^1\d{10}$/;
                return this.optional(element) || (mobile.test(value));
            }, "请输入正确的手机号码");
            
            // 验证配置
            $("#post-form").validate({
                // 错误信息处理
                errorPlacement: function(error, element) {
                    // 获取对应的inputTip
                    var $tip = element.siblings(".inputTip");
                    
                    // 保存原始提示文本（如果还没保存）
                    if (!$tip.data('originalText')) {
                        $tip.data('originalText', $tip.attr('data-default'));
                    }
                    
                    // 显示错误信息
                    $tip.removeClass('success focus').addClass('error').html('<i class="fas fa-exclamation-circle"></i> <span class="tip-text">' + error.text() + '</span>');
                },
                // 高亮显示错误字段
                highlight: function(element) {
                    var $element = $(element);
                    if ($element.parent().hasClass('custom-select')) {
                        $element.parent().addClass('error').removeClass('valid');
                    } else {
                        $element.addClass('error').removeClass('valid');
                    }
                },
                // 移除错误高亮
                unhighlight: function(element) {
                    var $element = $(element);
                    var $tip = $element.siblings(".inputTip");
                    
                    // 恢复原始提示文本
                    var originalText = $tip.data('originalText') || $tip.attr('data-default');
                    
                    if ($element.parent().hasClass('custom-select')) {
                        $element.parent().removeClass('error');
                        if ($element.val()) {
                            $element.parent().addClass('valid');
                            // 成功状态只显示图标，不显示文本
                            $tip.removeClass('error').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                        } else {
                            // 默认状态显示图标和文本
                            $tip.removeClass('error success').html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                        }
                    } else {
                        $element.removeClass('error');
                        if ($element.val()) {
                            $element.addClass('valid');
                            // 成功状态只显示图标，不显示文本
                            $tip.removeClass('error').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                        } else {
                            // 默认状态显示图标和文本
                            $tip.removeClass('error success').html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                        }
                    }
                },
                // 验证规则
                rules: {
                    region_id: "required",
                    expire_days: "required",
                    title: {
                        required: true,
                        minlength: 5,
                        maxlength: 50
                    },
                    content: {
                        required: true,
                        minlength: 10
                    },
                    contact_name: "required",
                    contact_mobile: {
                        required: true,
                        isMobile: true
                    },
                    password: {
                        required: true,
                        minlength: 6
                    }
                },
                // 错误信息
                messages: {
                    region_id: "请选择所在地区",
                    expire_days: "请选择有效期",
                    title: {
                        required: "请输入标题",
                        minlength: "标题至少5个字符",
                        maxlength: "标题最多50个字符"
                    },
                    content: {
                        required: "请输入详细内容",
                        minlength: "详情内容太短，请至少输入10个字符"
                    },
                    contact_name: "请输入联系人姓名",
                    contact_mobile: {
                        required: "请输入手机号码",
                        isMobile: "请输入正确的手机号码"
                    },
                    password: {
                        required: "请设置管理密码",
                        minlength: "管理密码长度不能少于6位"
                    }
                }
            });
            
            // 添加焦点状态处理
            $("#post-form input, #post-form select, #post-form textarea").on("focus", function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                
                // 如果字段没有错误状态且不是成功状态
                if (!$this.hasClass("error") && !$tip.hasClass("success")) {
                    var originalText = $tip.data('originalText') || $tip.attr('data-default');
                    // 焦点状态显示蓝色图标和提示文字
                    $tip.removeClass('success error').addClass('focus').html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                }
            }).on("blur", function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.data('originalText') || $tip.attr('data-default');
                
                // 移除焦点状态
                $tip.removeClass('focus');
                
                // 如果字段有值且验证通过，显示成功图标
                if ($this.val() && !$this.hasClass("error")) {
                    $this.addClass("valid");
                    // 成功状态只显示图标，不显示文本
                    $tip.removeClass('error').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                } else if (!$this.val() && !$this.hasClass("error")) {
                    // 如果没有值且没有错误，隐藏提示
                    $tip.removeClass('error success focus');
                }
                
                // 如果有错误，保持错误状态（由errorPlacement处理）
            });
            
            // 初始化已填写字段的验证状态
            const filledInputs = document.querySelectorAll('input[value]:not([value=""]), select[value]:not([value=""])');
            filledInputs.forEach(function(input) {
                if (input.value && input.value.trim() !== '') {
                    const tip = input.nextElementSibling;
                    if (tip && tip.classList.contains('inputTip')) {
                        const originalText = tip.getAttribute('data-default');
                        // 如果已填写且不是错误状态，设为成功
                        if (!input.classList.contains('error')) {
                            tip.classList.add('success');
                            tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                            $(input).addClass('valid');
                        }
                    }
                }
            });
            
            // 单独处理文本域，确保与其他表单元素行为一致
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(function(textarea) {
                // 如果有内容才显示成功状态，否则不显示任何提示
                if (textarea.value && textarea.value.trim() !== '') {
                    const tip = textarea.nextElementSibling;
                    if (tip && tip.classList.contains('inputTip')) {
                        const originalText = tip.getAttribute('data-default');
                        if (!textarea.classList.contains('error')) {
                            tip.classList.add('success');
                            tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                            $(textarea).addClass('valid');
                        }
                    }
                }
            });
            
            // 非必填字段输入处理
            $("#contact_address, #contact_weixin").on("input", function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.attr('data-default');
                
                if ($this.val()) {
                    $this.addClass("valid");
                    // 成功状态只显示图标，不显示文本
                    $tip.removeClass('error focus').addClass('success').html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                } else {
                    // 无值时隐藏提示
                    $tip.removeClass('error success focus');
                    $this.removeClass('valid error');
                }
            });
            
            // 初始化时对已有值的必填字段进行验证
            $("#post-form input[required], #post-form select[required], #post-form textarea[required]").each(function() {
                var $this = $(this);
                if ($this.val()) {
                    // 触发验证
                    $this.valid();
                }
            });
        });
        
        // 图片上传预览和压缩
        const input = document.getElementById('image_uploads');
        const preview = document.getElementById('image-previews');

        if (!input || !preview) {
            return;
        }

        // 初始化图片压缩器
        let compressor;

        if (typeof ImageCompressor !== 'undefined') {
            compressor = new ImageCompressor({
                maxWidth: 1920,
                maxHeight: 1080,
                quality: 0.8,
                targetSize: <?php echo (isset($upload_config['max_size'])) ? $upload_config['max_size'] : ""; ?> * 1024 * 1024,
                maxSize: 8 * 1024 * 1024,
                debug: false
            });
        } else {
            compressor = new SimpleImageCompressor();
        }

        // 全局变量存储压缩后的文件
        window.compressedImageFiles = [];

        // 创建进度提示
        function createProgressIndicator() {
            const progressHtml = `
                <div id="upload-progress" style="display: none; position: fixed; top: 50%; left: 50%;
                     transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white;
                     padding: 20px; border-radius: 8px; z-index: 9999; text-align: center;">
                    <div style="margin-bottom: 10px;">正在处理图片...</div>
                    <div style="width: 300px; height: 6px; background: #333; border-radius: 3px; overflow: hidden;">
                        <div id="progress-bar" style="width: 0%; height: 100%; background: #007bff;
                             border-radius: 3px; transition: width 0.3s;"></div>
                    </div>
                    <div id="progress-text" style="margin-top: 10px; font-size: 12px;">准备中...</div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', progressHtml);
        }

        // 更新进度
        function updateProgress(percentage, text) {
            const progressEl = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            if (progressEl) progressEl.style.display = 'block';
            if (progressBar) progressBar.style.width = percentage + '%';
            if (progressText && text) progressText.textContent = text;

            if (percentage >= 100) {
                setTimeout(() => {
                    if (progressEl) progressEl.style.display = 'none';
                }, 500);
            }
        }

        // 创建进度指示器
        createProgressIndicator();

        input.addEventListener('change', async function(event) {
            const files = Array.from(this.files);

            // 清空预览
            while(preview.firstChild) {
                preview.removeChild(preview.firstChild);
            }

            var maxCount = <?php echo (isset($upload_config['max_count'])) ? $upload_config['max_count'] : ""; ?>;
            if (files.length > maxCount) {
                showErrorPopup('最多只能上传' + maxCount + '张图片');
                this.value = '';
                return;
            }

            if (files.length === 0) {
                return;
            }

            try {
                updateProgress(0, '开始处理图片...');

                // 处理每个文件
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    if (!file.type.match('image.*')) {
                        continue;
                    }

                    const progressPercent = Math.round(((i + 1) / files.length) * 100);
                    updateProgress(progressPercent, `正在处理第 ${i + 1} 张图片...`);

                    try {
                        let result;

                        // 尝试压缩图片，如果失败则使用原图
                        try {
                            result = await compressor.compressFile(file);
                        } catch (compressError) {
                            // 使用原图
                            result = {
                                file: file,
                                originalSize: file.size,
                                compressedSize: file.size,
                                compressionRatio: '1.00',
                                quality: '1.00',
                                dimensions: { width: 0, height: 0 }
                            };
                        }

                        // 创建预览
                        const div = document.createElement('div');
                        div.className = 'image-item';
                        div.dataset.index = i;

                        const img = document.createElement('img');
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            img.src = e.target.result;
                        };
                        reader.readAsDataURL(result.file);

                        const removeBtn = document.createElement('span');
                        removeBtn.className = 'remove-image';
                        removeBtn.textContent = '×';
                        removeBtn.onclick = () => div.remove();

                        // 添加压缩信息提示
                        const infoBtn = document.createElement('span');
                        infoBtn.className = 'image-info';
                        infoBtn.innerHTML = 'ℹ';
                        infoBtn.style.cssText = `
                            position: absolute; top: 5px; left: 5px;
                            background: rgba(0,0,0,0.7); color: white;
                            border-radius: 50%; width: 20px; height: 20px;
                            text-align: center; line-height: 20px;
                            font-size: 12px; cursor: help;
                        `;

                        const originalSize = (file.size / 1024 / 1024).toFixed(2);
                        const compressedSize = (result.compressedSize / 1024 / 1024).toFixed(2);
                        const ratio = result.compressionRatio;

                        infoBtn.title = `原始大小: ${originalSize}MB\n压缩后: ${compressedSize}MB\n压缩比: ${ratio}`;

                        div.appendChild(img);
                        div.appendChild(removeBtn);
                        div.appendChild(infoBtn);
                        preview.appendChild(div);

                        // 将压缩后的文件添加到全局数组
                        window.compressedImageFiles.push(result.file);

                        // 更新文件输入框（用压缩后的文件替换原文件）
                        const dt = new DataTransfer();
                        for (let j = 0; j < files.length; j++) {
                            if (j === i) {
                                dt.items.add(result.file);
                            } else {
                                dt.items.add(files[j]);
                            }
                        }
                        // 注意：由于安全限制，实际上无法直接修改input.files
                        // 这里我们需要在表单提交时处理压缩后的文件

                    } catch (error) {
                        showErrorPopup(`图片 "${file.name}" 处理失败: ${error.message}`);
                    }
                }

                updateProgress(100, '所有图片处理完成');

            } catch (error) {
                showErrorPopup('图片处理出错: ' + error.message);
                updateProgress(0, '');
            }
        });
        
        // 微信号与手机号同步
        const mobileInput = document.getElementById('contact_mobile');
        const wechatInput = document.getElementById('contact_weixin');
        const wechatSame = document.getElementById('weixin_same');
        
        wechatSame.addEventListener('change', function() {
            if (this.checked) {
                wechatInput.value = mobileInput.value;
                wechatInput.disabled = true;
                
                // 更新微信输入框的验证图标状态
                const tip = wechatInput.nextElementSibling;
                if (tip) {
                    const originalText = tip.getAttribute('data-default');
                    if (wechatInput.value) {
                        tip.classList.remove('error', 'focus');
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                        $(wechatInput).addClass('valid');
                    } else {
                        tip.classList.remove('error', 'success', 'focus');
                        $(wechatInput).removeClass('valid');
                    }
                }
            } else {
                wechatInput.disabled = false;
                
                // 如果没有值，移除提示
                if (!wechatInput.value) {
                    const tip = wechatInput.nextElementSibling;
                    if (tip) {
                        tip.classList.remove('error', 'success', 'focus');
                        $(wechatInput).removeClass('valid');
                    }
                }
            }
        });
        
        mobileInput.addEventListener('input', function() {
            if (wechatSame.checked) {
                wechatInput.value = this.value;
                
                // 确保微信输入框也更新验证状态
                if (this.value) {
                    const tip = wechatInput.nextElementSibling;
                    if (tip) {
                        const originalText = tip.getAttribute('data-default');
                        tip.classList.remove('error', 'focus');
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                        $(wechatInput).addClass('valid');
                    }
                } else {
                    const tip = wechatInput.nextElementSibling;
                    if (tip) {
                        tip.classList.remove('error', 'success', 'focus');
                        $(wechatInput).removeClass('valid');
                    }
                }
            }
        });
        
        // 表单提交处理
        $('#post-form').on('submit', function(e) {
            // 阻止默认表单提交
            e.preventDefault();
            
            const form = $(this);
            
            // 表单验证
            if (!form.valid()) {
                return false;
            }
            
            // 显示加载遮罩
            $('#loading-overlay').css('display', 'flex').fadeIn();
            
            // 创建FormData对象
            const formData = new FormData(form[0]);

            // 替换压缩后的图片文件
            const compressedFiles = window.compressedImageFiles || [];
            if (compressedFiles.length > 0) {
                // 移除原始图片文件
                formData.delete('images[]');

                // 添加压缩后的文件
                compressedFiles.forEach((file, index) => {
                    formData.append('images[]', file);
                });

                console.log('PC端使用压缩后的图片文件:', compressedFiles);
            }

            // 添加submit字段，确保服务器端能识别表单提交
            formData.append('submit', '1');

            // 添加ajax标记
            formData.append('ajax', '1');
            
            // AJAX提交表单
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // 隐藏加载遮罩
                    $('#loading-overlay').fadeOut();
                    
                    // 尝试解析响应（如果它还不是对象）
                    var responseData = response;
                    if (typeof response === 'string') {
                        try {
                            responseData = JSON.parse(response);
                        } catch (e) {
                            // 如果响应中包含成功信息，视为成功
                            if (response.indexOf('成功') > -1) {
                                $('#success-overlay').css('display', 'flex').fadeIn();
                                return;
                            } else {
                                showErrorPopup("提交失败: 服务器返回非JSON格式数据");
                                return;
                            }
                        }
                    }
                    
                    // 处理JSON响应
                    if (responseData && responseData.success) {
                        // 显示成功提示
                        $('#success-overlay').css('display', 'flex').fadeIn();
                    } else {
                        // 显示错误信息
                        var message = responseData && responseData.message ? responseData.message : '提交失败，请稍后再试';
                        showErrorPopup(message);
                    }
                },
                error: function() {
                    // 隐藏加载遮罩
                    $('#loading-overlay').fadeOut();
                    showErrorPopup('提交失败，请稍后重试');
                }
            });
            
            return false;
        });

        // 自定义下拉框处理
        const customSelects = document.querySelectorAll('.custom-select');
        
        customSelects.forEach(select => {
            const trigger = select.querySelector('.select-trigger');
            const dropdown = select.querySelector('.select-dropdown');
            const options = select.querySelectorAll('.select-option');
            const input = select.querySelector('input[type="hidden"]');
            const tip = select.nextElementSibling;
            
            // 点击触发器显示/隐藏下拉框
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                customSelects.forEach(s => {
                    if (s !== select) {
                        s.classList.remove('active');
                    }
                });
                select.classList.toggle('active');
                
                // 点击触发器时显示蓝色提示
                if (select.classList.contains('active') && !select.classList.contains('error')) {
                    const tip = select.nextElementSibling;
                    if (tip) {
                        const originalText = tip.getAttribute('data-default');
                        if (!select.classList.contains('valid') && !input.value) {
                            tip.classList.remove('success', 'error');
                            tip.classList.add('focus');
                            tip.innerHTML = '<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>';
                        }
                    }
                }
            });
            
            // 点击选项
            options.forEach(option => {
                option.addEventListener('click', () => {
                    const value = option.getAttribute('data-value');
                    const text = option.textContent;
                    
                    // 更新隐藏输入值和显示文本
                    input.value = value;
                    trigger.textContent = text;
                    
                    // 更新选中状态
                    options.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    
                    // 关闭下拉框
                    select.classList.remove('active');
                    
                    // 更新验证状态
                    select.classList.add('valid');
                    select.classList.remove('error');
                    
                    // 切换图标
                    if (select.nextElementSibling) {
                        const tip = select.nextElementSibling;
                        const originalText = tip.getAttribute('data-default');
                        tip.classList.remove('error', 'focus');
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                    }
                    
                    // 触发 change 事件
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                    
                    // 验证表单
                    $(input).valid();
                });
            });
        });
        
        // 初始化下拉框验证状态
        document.addEventListener('DOMContentLoaded', function() {
            const customSelects = document.querySelectorAll('.custom-select');
            customSelects.forEach(select => {
                const input = select.querySelector('input[type="hidden"]');
                if (input && input.value) {
                    select.classList.add('valid');
                    select.classList.remove('error');
                    
                    if (select.nextElementSibling) {
                        const tip = select.nextElementSibling;
                        const originalText = tip.getAttribute('data-default');
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                    }
                }
            });
        });
        
        // 点击其他地方关闭所有下拉框
        document.addEventListener('click', () => {
            customSelects.forEach(select => {
                select.classList.remove('active');
                // 如果没有选择值且是必填项，显示错误状态
                const input = select.querySelector('input[type="hidden"]');
                if (input && input.hasAttribute('required') && !input.value) {
                    select.classList.add('error');
                    select.classList.remove('valid');
                    
                    // 显示错误提示
                    const tip = select.nextElementSibling;
                    if (tip) {
                        const originalText = tip.getAttribute('data-default');
                        tip.classList.remove('success', 'focus');
                        tip.classList.add('error');
                        tip.innerHTML = '<i class="fas fa-exclamation-circle"></i> <span class="tip-text">请选择' + originalText + '</span>';
                    }
                }
            });
        });
        
        // 阻止下拉框内部点击事件冒泡
        document.querySelectorAll('.select-dropdown').forEach(dropdown => {
            dropdown.addEventListener('click', (e) => e.stopPropagation());
        });

        // 设置默认选中值（如果有）
        customSelects.forEach(select => {
            const input = select.querySelector('input[type="hidden"]');
            const options = select.querySelectorAll('.select-option');
            const trigger = select.querySelector('.select-trigger');
            const tip = select.nextElementSibling;
            
            if (input.value) {
                options.forEach(option => {
                    if (option.getAttribute('data-value') === input.value) {
                        option.classList.add('selected');
                        trigger.textContent = option.textContent;
                        select.classList.add('valid');
                        // 切换图标
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + tip.getAttribute('data-default') + '</span>';
                    }
                });
            }
        });

        // 设置初始状态为每个inputTip显示蓝色图标和提示文字
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载后先隐藏所有提示
            const allTips = document.querySelectorAll('.inputTip');
            allTips.forEach(function(tip) {
                // 初始不显示
                tip.classList.remove('focus', 'error', 'success');
            });
            
            // 初始化已填写字段的验证状态
            const filledInputs = document.querySelectorAll('input[value]:not([value=""]), select[value]:not([value=""])');
            filledInputs.forEach(function(input) {
                if (input.value && input.value.trim() !== '') {
                    const tip = input.nextElementSibling;
                    if (tip && tip.classList.contains('inputTip')) {
                        const originalText = tip.getAttribute('data-default');
                        // 如果已填写且不是错误状态，设为成功
                        if (!input.classList.contains('error')) {
                            tip.classList.add('success');
                            tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                            $(input).addClass('valid');
                        }
                    }
                }
            });
            
            // 单独处理文本域，确保与其他表单元素行为一致
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(function(textarea) {
                // 如果有内容才显示成功状态，否则不显示任何提示
                if (textarea.value && textarea.value.trim() !== '') {
                    const tip = textarea.nextElementSibling;
                    if (tip && tip.classList.contains('inputTip')) {
                        const originalText = tip.getAttribute('data-default');
                        if (!textarea.classList.contains('error')) {
                            tip.classList.add('success');
                            tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                            $(textarea).addClass('valid');
                        }
                    }
                }
            });
            
            // 为自定义下拉框添加特殊处理
            const customSelects = document.querySelectorAll('.custom-select');
            customSelects.forEach(select => {
                const input = select.querySelector('input[type="hidden"]');
                if (input && input.value) {
                    const tip = select.nextElementSibling;
                    if (tip && tip.classList.contains('inputTip')) {
                        const originalText = tip.getAttribute('data-default');
                        tip.classList.add('success');
                        tip.innerHTML = '<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>';
                    }
                }
            });
        });
    });
    </script>
    <style>
    /* 调整表单容器样式 */
    .content-wrap {
        width: 100%;
        max-width: 1200px;
     
     
    }
    
    .left-column {
        width: 100%;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .form-panel {
        padding: 20px;
    }
    
    /* 文本框相关样式 */
    textarea.form-textarea {
        resize: none;
        height: 120px;
        box-sizing: border-box;
        transition: border-color 0.3s;
    }

    /* 解决文本域获得焦点时位置移动的问题 */
    .form-group.required .form-right {
        position: relative;
    }
    
    /* 确保文本域的提示元素不影响布局 */
    .form-group textarea + .inputTip {
        position: absolute;
        margin-top: 0;
        top: 0;
        left: 610px;
        white-space: nowrap;
    }

    /* 确保表单元素尺寸计算包含边框 */
    .form-input,
    .form-textarea,
    .custom-select,
    .select-trigger {
        box-sizing: border-box;
    }

    /* 确保文本域的提示元素与文本框在同一行 */
    .form-group textarea + .inputTip {
        position: static;
        display: inline-block;
        vertical-align: top;
        margin-top: 0;
        white-space: nowrap;
    }
    
    /* 确保textarea失去焦点后提示消失 */
    textarea:not(:focus) + .inputTip:not(.error):not(.success) {
        display: none;
    }
    </style>
</body>
</html>
