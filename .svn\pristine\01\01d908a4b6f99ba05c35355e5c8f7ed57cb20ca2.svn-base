<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<!-- 页面标题 -->
<div class="page-title">
    <h1>
        <i class="fas fa-cog"></i>
        系统设置
    </h1>
    <div class="page-actions">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回首页</span>
        </a>
    </div>
</div>

<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    <?php if(null !== ($message ?? null) && !empty($message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <strong>操作成功</strong>
            <p><?php echo $message ?? ""; ?></p>
        </div>
    </div>
    <?php endif; ?>


    <!-- 设置选项卡 -->
    <div class="card">
        <div class="setting-tabs">
            <?php if(null !== ($groups ?? null) && is_array($groups)): foreach($groups as $group_key => $group_name): ?>
            <a href="?group=<?php echo $group_key ?? ""; ?>" class="setting-tab<?php if($current_group == $group_key): ?> active<?php endif; ?>">
                <?php echo $group_name ?? ""; ?>
            </a>
            <?php endforeach; endif; ?>
        </div>

        <!-- 设置表单 -->
        <form method="post" action="" class="form-horizontal">
            <input type="hidden" name="action" value="save_settings">
            <input type="hidden" name="group" value="<?php echo $current_group ?? ""; ?>">

            <?php if($settings): ?>
            <?php if(null !== ($settings ?? null) && is_array($settings)): foreach($settings as $setting): ?>
            <div class="form-group">
                <label class="form-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>">
                    <?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?>
                    <?php if(null !== ($setting ?? null) && is_array($setting) && array_key_exists('setting_description', $setting) && !empty($setting['setting_description'])): ?>
                    <i class="fas fa-question-circle help-icon" title="<?php echo (isset($setting['setting_description'])) ? $setting['setting_description'] : ""; ?>"></i>
                    <?php endif; ?>
                </label>

                <div class="form-field">
                    <?php if($setting['setting_type'] == 'text'): ?>
                    <input type="text"
                           id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                           name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                           value="<?php echo null !== ((null !== ($setting ?? null)) ? ($setting['setting_value']) : null) ? Template::escape((null !== ($setting ?? null)) ? ($setting['setting_value']) : null) : ""; ?>"
                           class="form-control"
                           placeholder="请输入<?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?>">

                    <?php elseif($setting['setting_type'] == 'number'): ?>
                    <input type="number"
                           id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                           name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                           value="<?php echo (isset($setting['setting_value'])) ? $setting['setting_value'] : ""; ?>"
                           class="form-control"
                           min="0"
                           step="1"
                           placeholder="请输入数字">

                    <?php elseif($setting['setting_type'] == 'textarea'): ?>
                    <textarea id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                              name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                              class="form-textarea"
                              rows="4"
                              placeholder="请输入<?php echo (isset($setting['setting_title'])) ? $setting['setting_title'] : ""; ?>"><?php echo null !== ((null !== ($setting ?? null)) ? ($setting['setting_value']) : null) ? Template::escape((null !== ($setting ?? null)) ? ($setting['setting_value']) : null) : ""; ?></textarea>

                    <?php elseif($setting['setting_type'] == 'radio' || $setting['setting_type'] == 'switch'): ?>
                    <div class="form-radio-group">
                        <div class="form-check">
                            <input type="radio"
                                   name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                                   id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_1"
                                   value="1"
                                   <?php if($setting['setting_value'] == '1'): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_1">
                                <i class="fas fa-check-circle"></i>
                                启用
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="radio"
                                   name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>"
                                   id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_0"
                                   value="0"
                                   <?php if($setting['setting_value'] == '0'): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_0">
                                <i class="fas fa-times-circle"></i>
                                禁用
                            </label>
                        </div>
                    </div>

                    <?php elseif($setting['setting_type'] == 'select'): ?>
                    <select id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>" class="form-select">
                        <?php if($setting['setting_key'] == 'mobile_pagination_mode'): ?>
                        <option value="pagination" <?php if($setting['setting_value'] == 'pagination'): ?>selected<?php endif; ?>>传统分页模式（上一页/下一页）</option>
                        <option value="loadmore" <?php if($setting['setting_value'] == 'loadmore'): ?>selected<?php endif; ?>>点击加载更多模式</option>
                        <option value="infinite" <?php if($setting['setting_value'] == 'infinite'): ?>selected<?php endif; ?>>滚动无限加载模式</option>
                        <?php else: ?>
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        <?php if($options): ?>
                        <?php if(null !== ($options ?? null) && is_array($options)): foreach($options as $option_key => $option_value): ?>
                        <option value="<?php echo $option_key ?? ""; ?>" <?php if($setting['setting_value'] == $option_key): ?>selected<?php endif; ?>><?php echo $option_value ?? ""; ?></option>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>
                    </select>

                    <?php elseif($setting['setting_type'] == 'checkbox'): ?>
                    <div class="form-checkbox-group">
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {assign var=selected_values value=explode(',', $setting.setting_value)}
                        <?php if($options): ?>
                        <?php if(null !== ($options ?? null) && is_array($options)): foreach($options as $option_key => $option_value): ?>
                        <div class="form-check">
                            <input type="checkbox"
                                   name="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>[]"
                                   id="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_<?php echo $option_key ?? ""; ?>"
                                   value="<?php echo $option_key ?? ""; ?>"
                                   <?php if(in_array($option_key, $selected_values)): ?>checked<?php endif; ?>>
                            <label class="form-check-label" for="<?php echo (isset($setting['setting_key'])) ? $setting['setting_key'] : ""; ?>_<?php echo $option_key ?? ""; ?>">
                                <?php echo $option_value ?? ""; ?>
                            </label>
                        </div>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if(null !== ($setting ?? null) && is_array($setting) && array_key_exists('setting_description', $setting) && !empty($setting['setting_description'])): ?>
                <div class="form-help">
                    <i class="fas fa-info-circle"></i>
                    <span class="help-text"><?php echo (isset($setting['setting_description'])) ? $setting['setting_description'] : ""; ?></span>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; endif; ?>
            <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-cog"></i>
                <h3>暂无设置项</h3>
                <p>当前分组下没有可配置的设置项</p>
            </div>
            <?php endif; ?>

            <!-- 提交按钮 -->
            <?php if($settings): ?>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    <span>保存设置</span>
                </button>
                <button type="reset" class="btn btn-outline">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
                <a href="?group=<?php echo $current_group ?? ""; ?>" class="btn btn-outline">
                    <i class="fas fa-refresh"></i>
                    <span>刷新</span>
                </a>
            </div>
            <?php endif; ?>
        </form>
    </div>
</div>

<!-- 页面脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const helpIcons = document.querySelectorAll('.help-icon');
    helpIcons.forEach(function(icon) {
        icon.addEventListener('mouseenter', function() {
            // 可以在这里添加更详细的工具提示逻辑
        });
    });

    // 表单验证
    const form = document.querySelector('.form-horizontal');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('请填写所有必填字段');
            }
        });
    }

    // 数字输入验证
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });
});
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>