<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>
 

<style>
    .form-group {
        margin-bottom: 15px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .form-label {
        display: block;
        margin-bottom: 0;
        font-weight: 500;
        width: 120px;
        text-align: right;
        padding-right: 15px;
        color: #666;
        line-height: 32px;
    }
    .form-field {
        flex: 0 0 auto;
        min-width: 300px;
        max-width: 350px;
    }
    .form-hint {
        flex: 1;
        margin-left: 15px;
        font-size: 12px;
        color: var(--text-secondary);
        padding-top: 10px;
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .form-control:focus {
        border-color: var(--primary-color);
        outline: 0;
    }
    textarea.form-control {
        min-height: 300px;
        width: 100%;
        padding: 12px;
        line-height: 1.6;
        resize: vertical;
    }
    .input-group {
        display: flex;
    }
    .input-group .form-control {
        border-radius: 4px 0 0 4px;
    }
    .input-group .input-group-append {
        display: flex;
    }
    .input-group .input-group-append .btn {
        border-radius: 0 4px 4px 0;
        border-left: 0;
    }
    .radio-group {
        margin-top: 8px;
    }
    .form-buttons {
        margin-top: 15px;
    }
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
        text-decoration: none;
    }
    
    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    
    .btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .btn-outline {
        color: #6c757d;
        background-color: transparent;
        border-color: #6c757d;
    }
    .section-title {
        font-size: 1.5rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    .card {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        padding: 20px;
    }
    .card-title {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }
    .top-settings {
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .top-settings-title {
        font-weight: 500;
        margin-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
    }
    .switch-label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    .switch-text {
        margin-left: 5px;
    }
    /* 美化日期选择器 */
    input[type="date"] {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
    }
    input[type="date"]:focus {
        border-color: var(--primary-color);
        outline: none;
    }
    
    /* 图片上传样式 */
    .image-upload {
        margin-top: 10px;
    }
    .upload-area {
        border: 2px dashed #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        margin-bottom: 15px;
    }
    .upload-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #1890ff;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s;
    }
    .upload-btn:hover {
        background: #40a9ff;
    }
    .image-hint {
        margin-top: 10px;
        color: #666;
        font-size: 13px;
    }
    .image-hint .warning {
        color: #ff4d4f;
    }
    .image-previews {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    .image-item {
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .image-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }
    .image-item .loading-placeholder {
        color: #999;
        font-size: 12px;
        text-align: center;
    }
    .remove-image {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        background: rgba(0,0,0,0.5);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s;
    }
    .remove-image:hover {
        background: rgba(0,0,0,0.7);
    }
    .hidden-input {
        display: none;
    }
    
    @media (max-width: 992px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
            max-width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
    
    @media (max-width: 768px) {
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
            padding-right: 0;
        }
        .form-field {
            width: 100%;
        }
        .form-hint {
            margin-left: 0;
            margin-top: 5px;
            width: 100%;
        }
    }
    
    /* 标签页样式 */
    .tabs {
        margin-bottom: 20px;
    }
    .tab-nav {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 20px;
    }
    .tab-nav-item {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        margin-right: 10px;
        font-weight: 500;
    }
    .tab-nav-item.active {
        border-bottom-color: var(--primary-color, #007bff);
        color: var(--primary-color, #007bff);
    }
    .tab-content {
        display: none;
    }
    .tab-content.active {
        display: block;
    }
    /* 移除UEditor编辑器样式调整 */
    .form-field.editor-container {
        max-width: 800px;
        width: 100%;
    }
</style>

<div class="section">
   
    <!-- 消息提示 -->
    <?php if($message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p><?php echo $message ?? ""; ?></p>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if($error): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        <div>
            <p><?php echo $error ?? ""; ?></p>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <form action="" method="post" enctype="multipart/form-data" autocomplete="off">
            <div class="tabs">
                <div class="tab-nav">
                    <div class="tab-nav-item active" data-tab="info">编辑信息</div>
                </div>
                
                <!-- 编辑信息标签页 -->
                <div class="tab-content active" id="tab-info">
                    <div class="form-group">
                        <label class="form-label" for="title">标题</label>
                        <div class="form-field">
                            <input type="text" id="title" name="title" class="form-control" value="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>" required autocomplete="off">
                        </div>
                        <span class="form-hint">必填，信息的标题</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">分类选择</label>
                        <div class="form-field" style="display: flex; gap: 15px; align-items: center;">
                            <div style="flex: 1; max-width: 200px;">
                                <select id="parent_category_id" name="parent_category_id" class="form-control" onchange="updateSubCategories()" style="width: 100%;">
                                    <option value="">请选择大分类</option>
                                    <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $category): ?>
                                    <option value="<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" data-is-leaf="<?php if(empty($category['children'])): ?>1<?php else: ?>0<?php endif; ?>"><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></option>
                                    <?php endforeach; endif; ?>
                                </select>
                            </div>
                            <div id="sub_category_group" style="flex: 1; max-width: 200px;">
                                <select id="category_id" name="category_id" class="form-control" style="width: 100%;">
                                    <option value="">请选择小分类</option>
                                </select>
                            </div>
                        </div>
                        <span class="form-hint">必填，先选择大分类，再选择小分类（如果有）</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="region_id">区域</label>
                        <div class="form-field">
                            <select id="region_id" name="region_id" class="form-control">
                                <option value="0">请选择区域</option>
                                <?php if(null !== ($regions ?? null) && is_array($regions)): foreach($regions as $province): ?>
                                <optgroup label="<?php echo (isset($province['name'])) ? $province['name'] : ""; ?>">
                                    <?php if(null !== ($province ?? null) && is_array($province['children'])): foreach($province['children'] as $city): ?>
                                    <option value="<?php echo (isset($city['id'])) ? $city['id'] : ""; ?>" <?php if($post['region_id'] == $city['id']): ?>selected<?php endif; ?>><?php echo (isset($city['name'])) ? $city['name'] : ""; ?></option>
                                    <?php endforeach; endif; ?>
                                </optgroup>
                                <?php endforeach; endif; ?>
                            </select>
                        </div>
                        <span class="form-hint">可选，选择信息所属区域，支持省市二级选择</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="status">状态</label>
                        <div class="form-field">
                            <select id="status" name="status" class="form-control">
                                <option value="0" <?php if($post['status'] == 0): ?>selected<?php endif; ?>>待审核</option>
                                <option value="1" <?php if($post['status'] == 1 || !$post): ?>selected<?php endif; ?>>正常</option>
                                <option value="2" <?php if($post['status'] == 2): ?>selected<?php endif; ?>>已下架</option>
                            </select>
                        </div>
                        <span class="form-hint">设置信息的状态</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="expire_days">有效天数</label>
                        <div class="form-field">
                            <input type="number" id="expire_days" name="expire_days" class="form-control" value="<?php if($post['expire_days']): ?><?php echo (isset($post['expire_days'])) ? $post['expire_days'] : ""; ?><?php else: ?>30<?php endif; ?>" min="1" max="365" style="width: 150px;">
                        </div>
                        <span class="form-hint">设置信息的有效天数，超过有效期将自动下架</span>
                    </div>
                    
                    <!-- 时间更新选项 -->
                    <?php if($action == 'edit'): ?>
                    <div class="form-group">
                        <label class="form-label">时间设置</label>
                        <div class="form-field">
                            <div class="form-check" style="margin-bottom: 8px;">
                                <input type="checkbox" id="update_time" name="update_time" class="form-check-input" style="margin-right: 5px;">
                                <label for="update_time" class="form-check-label">更新"最后更新时间"</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="update_expire" name="update_expire" class="form-check-input" style="margin-right: 5px;">
                                <label for="update_expire" class="form-check-label">更新"到期时间"</label>
                            </div>
                        </div>
                        <span class="form-hint">勾选后编辑信息会更新相应的时间，不勾选则保持原时间不变</span>
                    </div>
                    <?php endif; ?>

                    <!-- 置顶设置 -->
                    <div class="form-group">
                        <label class="form-label">置顶设置</label>
                        <div class="form-field">
                            <div class="top-settings">
                                <!-- 首页置顶 -->
                                <div class="top-option">
                                    <div class="form-check" style="margin-bottom: 8px;">
                                        <input type="checkbox" id="is_top_home" name="is_top_home" value="1" class="form-check-input top-checkbox" style="margin-right: 5px;" <?php if($post['is_top_home']): ?>checked<?php endif; ?>>
                                        <label for="is_top_home" class="form-check-label">首页置顶</label>
                                    </div>
                                    <div class="expire-setting" id="home_expire_setting" style="display: <?php if($post['is_top_home']): ?>block<?php else: ?>none<?php endif; ?>; margin-left: 20px; margin-bottom: 10px;">
                                        <label for="top_home_expire" style="font-size: 12px; color: #666;">到期时间：</label>
                                        <input type="datetime-local" id="top_home_expire" name="top_home_expire" class="form-control" style="width: 200px; display: inline-block;" value="<?php if($post['top_home_expire']): ?><?php echo null !== ((null !== ($post ?? null)) ? ($post['top_home_expire']) : null) ? Template::date_format((null !== ($post ?? null)) ? ($post['top_home_expire']) : null, 'Y-m-d\TH:i') : ""; ?><?php endif; ?>">
                                        <span class="help-text" style="font-size: 11px; color: #999;">留空表示永久置顶</span>
                                    </div>
                                </div>

                                <!-- 大分类置顶 -->
                                <div class="top-option">
                                    <div class="form-check" style="margin-bottom: 8px;">
                                        <input type="checkbox" id="is_top_category" name="is_top_category" value="1" class="form-check-input top-checkbox" style="margin-right: 5px;" <?php if($post['is_top_category']): ?>checked<?php endif; ?>>
                                        <label for="is_top_category" class="form-check-label">大分类置顶</label>
                                    </div>
                                    <div class="expire-setting" id="category_expire_setting" style="display: <?php if($post['is_top_category']): ?>block<?php else: ?>none<?php endif; ?>; margin-left: 20px; margin-bottom: 10px;">
                                        <label for="top_category_expire" style="font-size: 12px; color: #666;">到期时间：</label>
                                        <input type="datetime-local" id="top_category_expire" name="top_category_expire" class="form-control" style="width: 200px; display: inline-block;" value="<?php if($post['top_category_expire']): ?><?php echo null !== ((null !== ($post ?? null)) ? ($post['top_category_expire']) : null) ? Template::date_format((null !== ($post ?? null)) ? ($post['top_category_expire']) : null, 'Y-m-d\TH:i') : ""; ?><?php endif; ?>">
                                        <span class="help-text" style="font-size: 11px; color: #999;">留空表示永久置顶</span>
                                    </div>
                                </div>

                                <!-- 小分类置顶 -->
                                <div class="top-option">
                                    <div class="form-check" style="margin-bottom: 8px;">
                                        <input type="checkbox" id="is_top_subcategory" name="is_top_subcategory" value="1" class="form-check-input top-checkbox" style="margin-right: 5px;" <?php if($post['is_top_subcategory']): ?>checked<?php endif; ?>>
                                        <label for="is_top_subcategory" class="form-check-label">小分类置顶</label>
                                    </div>
                                    <div class="expire-setting" id="subcategory_expire_setting" style="display: <?php if($post['is_top_subcategory']): ?>block<?php else: ?>none<?php endif; ?>; margin-left: 20px; margin-bottom: 10px;">
                                        <label for="top_subcategory_expire" style="font-size: 12px; color: #666;">到期时间：</label>
                                        <input type="datetime-local" id="top_subcategory_expire" name="top_subcategory_expire" class="form-control" style="width: 200px; display: inline-block;" value="<?php if($post['top_subcategory_expire']): ?><?php echo null !== ((null !== ($post ?? null)) ? ($post['top_subcategory_expire']) : null) ? Template::date_format((null !== ($post ?? null)) ? ($post['top_subcategory_expire']) : null, 'Y-m-d\TH:i') : ""; ?><?php endif; ?>">
                                        <span class="help-text" style="font-size: 11px; color: #999;">留空表示永久置顶</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="form-hint">设置信息在不同位置的置顶显示，可设置到期时间</span>
                    </div>

                    <!-- 图片上传 -->
                    <div class="form-group">
                        <label class="form-label">上传图片</label>
                        <div class="form-input">
                            <div class="image-upload">
                                <div class="upload-area">
                                    <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                                    <label for="image_uploads" class="upload-btn">
                                        <i class="fas fa-image"></i>
                                        <span>添加图片</span>
                                    </label>
                                    <div class="image-hint">最多可上传9张图片，支持大图片自动压缩</div>
                                </div>
                                <div class="image-previews" id="image-previews">
                                    <?php if(null !== ($attachments ?? null) && $attachments): ?>
                                        <?php if(null !== ($attachments ?? null) && is_array($attachments)): foreach($attachments as $attachment): ?>
                                        <div class="image-item">
                                            <img src="<?php echo (isset($attachment['file_path'])) ? $attachment['file_path'] : ""; ?>" alt="已上传图片">
                                            <span class="remove-image" data-id="<?php echo (isset($attachment['id'])) ? $attachment['id'] : ""; ?>">×</span>
                                            <input type="hidden" name="existing_images[]" value="<?php echo (isset($attachment['id'])) ? $attachment['id'] : ""; ?>">
                                        </div>
                                        <?php endforeach; endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="content">内容</label>
                        <div class="form-field editor-container">
                            <!-- 替换UEditor编辑器为普通文本框 -->
                            <textarea id="content" name="content" class="form-control"><?php echo (isset($post['content'])) ? $post['content'] : ""; ?></textarea>
                        </div>
                        <span class="form-hint">信息的详细内容</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="contact_name">联系人</label>
                        <div class="form-field">
                            <input type="text" id="contact_name" name="contact_name" class="form-control" value="<?php echo (isset($post['contact_name'])) ? $post['contact_name'] : ""; ?>" autocomplete="off">
                        </div>
                        <span class="form-hint">可选，联系人姓名</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="contact_mobile">联系电话</label>
                        <div class="form-field">
                            <input type="text" id="contact_mobile" name="contact_mobile" class="form-control" value="<?php echo (isset($post['contact_mobile'])) ? $post['contact_mobile'] : ""; ?>" autocomplete="off">
                        </div>
                        <span class="form-hint">可选，联系人电话</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="contact_weixin">联系微信</label>
                        <div class="form-field">
                            <input type="text" id="contact_weixin" name="contact_weixin" class="form-control" value="<?php echo (isset($post['contact_weixin'])) ? $post['contact_weixin'] : ""; ?>" autocomplete="off">
                        </div>
                        <span class="form-hint">可选，联系人微信</span>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="contact_address">联系地址</label>
                        <div class="form-field">
                            <input type="text" id="contact_address" name="contact_address" class="form-control" value="<?php echo (isset($post['contact_address'])) ? $post['contact_address'] : ""; ?>">
                        </div>
                        <span class="form-hint">可选，联系地址</span>
                    </div>

                    <!-- 管理密码 -->
                    <div class="form-group">
                        <label class="form-label">管理密码</label>
                        <div class="form-field">
                            <div class="form-check">
                                <input type="checkbox" id="reset_password" class="form-check-input" style="margin-right: 5px;">
                                <label for="reset_password" class="form-check-label">重置密码</label>
                            </div>
                            <div id="password_input_group" style="display: none; margin-top: 10px;">
                                <input type="password" id="password" name="password" class="form-control" placeholder="请输入新密码">
                            </div>
                        </div>
                        <span class="form-hint">勾选后可以重置管理密码</span>
                    </div>
                </div>
            </div>
            
            <div class="form-buttons">
                <button type="submit" class="btn btn-light-primary">
                    <?php if($action == 'add'): ?>添加并继续编辑<?php else: ?>保存并继续编辑<?php endif; ?>
                </button>
                
                <button type="submit" name="save_and_back" class="btn btn-light-success" style="margin-left: 10px;">
                    <?php if($action == 'add'): ?>添加并返回列表<?php else: ?>保存并返回列表<?php endif; ?>
                </button>
                
                <a href="info.php" class="btn btn-light-secondary" style="margin-left: 10px;">
                    取消
                </a>
                
                <?php if($action == 'edit' && $post['id']): ?>
                <!-- 添加查看前台链接 -->
                <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('category_pinyin', $post) && $post['category_pinyin']): ?>
                <a href="../<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" target="_blank" class="btn btn-light-info" style="margin-left: 10px;">
                    查看前台
                </a>
                <?php else: ?>
                <a href="../view.php?id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" target="_blank" class="btn btn-light-info" style="margin-left: 10px;">
                    查看前台
                </a>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签页切换
    const tabNavItems = document.querySelectorAll('.tab-nav-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabNavItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有tab-nav-item的active类
            tabNavItems.forEach(navItem => {
                navItem.classList.remove('active');
            });
            
            // 移除所有tab-content的active类
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 为当前点击的tab-nav-item添加active类
            this.classList.add('active');
            
            // 获取对应的tab内容，并添加active类
            const tabId = 'tab-' + this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
    
    // 密码重置功能
    const resetPasswordCheckbox = document.getElementById('reset_password');
    const passwordInputGroup = document.getElementById('password_input_group');
    const passwordInput = document.getElementById('password');

    if (resetPasswordCheckbox && passwordInputGroup && passwordInput) {
        resetPasswordCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // 显示密码输入框
                passwordInputGroup.style.display = 'block';
                // 可以添加简单的动画效果
                passwordInputGroup.style.opacity = '0';
                setTimeout(() => {
                    passwordInputGroup.style.transition = 'opacity 0.3s ease';
                    passwordInputGroup.style.opacity = '1';
                }, 10);
            } else {
                // 隐藏密码输入框并清空内容
                passwordInputGroup.style.opacity = '0';
                setTimeout(() => {
                    passwordInputGroup.style.display = 'none';
                    passwordInput.value = '';
                }, 300);
            }
        });
    }

    // 置顶设置交互逻辑
    const topCheckboxes = document.querySelectorAll('.top-checkbox');
    topCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            var expireSetting;

            // 根据复选框ID找到对应的到期设置区域
            if (this.id === 'is_top_home') {
                expireSetting = document.getElementById('home_expire_setting');
            } else if (this.id === 'is_top_category') {
                expireSetting = document.getElementById('category_expire_setting');
            } else if (this.id === 'is_top_subcategory') {
                expireSetting = document.getElementById('subcategory_expire_setting');
            }

            if (expireSetting) {
                if (this.checked) {
                    // 显示到期设置区域
                    expireSetting.style.display = 'block';
                    // 添加淡入动画
                    expireSetting.style.opacity = '0';
                    setTimeout(() => {
                        expireSetting.style.transition = 'opacity 0.3s ease';
                        expireSetting.style.opacity = '1';
                    }, 10);
                } else {
                    // 隐藏到期设置区域
                    expireSetting.style.opacity = '0';
                    setTimeout(() => {
                        expireSetting.style.display = 'none';
                        // 清空到期时间
                        var expireInput = expireSetting.querySelector('input[type="datetime-local"]');
                        if (expireInput) {
                            expireInput.value = '';
                        }
                    }, 300);
                }
            }
        });
    });

    // 后台管理图片上传预览和压缩
    const input = document.getElementById('image_uploads');
    const preview = document.getElementById('image-previews');

    console.log('图片上传初始化:', {
        input: !!input,
        preview: !!preview,
        ImageCompressor: typeof ImageCompressor
    });

    if (input && preview) {
        // 初始化图片压缩器（后台管理配置）
        let adminCompressor = null;

        if (typeof ImageCompressor !== 'undefined') {
            adminCompressor = new ImageCompressor({
                maxWidth: 1920,
                maxHeight: 1080,
                quality: 0.85,
                targetSize: 4 * 1024 * 1024, // 后台允许更大的文件
                maxSize: 10 * 1024 * 1024, // 10MB
                outputFormat: 'image/jpeg',
                debug: false
            });
            console.log('图片压缩器初始化成功');
        } else {
            console.warn('ImageCompressor未找到，将使用简化预览模式');
        }

        // 创建后台进度提示
        function createAdminProgress() {
            if (document.getElementById('admin-upload-progress')) return;

            const progressHtml = `
                <div id="admin-upload-progress" style="display: none; position: fixed; top: 50%; left: 50%;
                     transform: translate(-50%, -50%); background: rgba(0,0,0,0.9); color: white;
                     padding: 30px; border-radius: 8px; z-index: 10000; text-align: center; min-width: 350px;">
                    <div style="margin-bottom: 15px; font-size: 16px;">正在处理图片...</div>
                    <div style="width: 300px; height: 8px; background: #333; border-radius: 4px; overflow: hidden;">
                        <div id="admin-progress-bar" style="width: 0%; height: 100%; background: #007bff;
                             border-radius: 4px; transition: width 0.3s;"></div>
                    </div>
                    <div id="admin-progress-text" style="margin-top: 15px; font-size: 14px; color: #ccc;">准备中...</div>
                    <div id="admin-progress-details" style="margin-top: 10px; font-size: 12px; color: #999;"></div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', progressHtml);
        }

        // 更新后台进度
        function updateAdminProgress(percentage, text, details) {
            const progressEl = document.getElementById('admin-upload-progress');
            const progressBar = document.getElementById('admin-progress-bar');
            const progressText = document.getElementById('admin-progress-text');
            const progressDetails = document.getElementById('admin-progress-details');

            if (progressEl) progressEl.style.display = 'block';
            if (progressBar) progressBar.style.width = percentage + '%';
            if (progressText && text) progressText.textContent = text;
            if (progressDetails && details) progressDetails.textContent = details;

            if (percentage >= 100) {
                setTimeout(() => {
                    if (progressEl) progressEl.style.display = 'none';
                }, 1000);
            }
        }

        createAdminProgress();

        // 简化的图片预览函数（不压缩）
        function createSimplePreview(file) {
            console.log('创建简化预览:', file.name);

            const div = document.createElement('div');
            div.className = 'image-item';
            div.style.position = 'relative';

            const loadingPlaceholder = document.createElement('div');
            loadingPlaceholder.className = 'loading-placeholder';
            loadingPlaceholder.innerHTML = '加载中...';
            div.appendChild(loadingPlaceholder);

            const img = document.createElement('img');
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: none;
                position: absolute;
                top: 0;
                left: 0;
            `;

            const removeBtn = document.createElement('span');
            removeBtn.className = 'remove-image';
            removeBtn.innerHTML = '×';
            removeBtn.onclick = function() {
                console.log('删除简化预览');
                div.remove();
            };

            div.appendChild(img);
            div.appendChild(removeBtn);
            preview.appendChild(div);

            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('简化预览FileReader完成');
                img.src = e.target.result;
                img.alt = '预览图片';

                img.onload = function() {
                    console.log('简化预览图片加载完成:', file.name);
                    loadingPlaceholder.style.display = 'none';
                    img.style.display = 'block';
                };

                img.onerror = function() {
                    console.error('简化预览图片加载失败:', file.name);
                    loadingPlaceholder.innerHTML = '预览失败';
                    loadingPlaceholder.style.color = '#ff4d4f';
                };
            };

            reader.onerror = function() {
                console.error('简化预览FileReader失败:', file.name);
                loadingPlaceholder.innerHTML = '读取失败';
                loadingPlaceholder.style.color = '#ff4d4f';
            };

            reader.readAsDataURL(file);
        }

        input.addEventListener('change', async function() {
            const files = Array.from(this.files);

            console.log('选择了文件:', files.length, '个');

            // 限制上传数量
            if (files.length > 9) {
                alert('最多只能上传9张图片');
                this.value = '';
                return;
            }

            if (files.length === 0) return;

            try {
                updateAdminProgress(0, '开始处理图片...', '');

                // 处理每个文件
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    console.log(`处理文件 ${i + 1}:`, file.name, file.type, file.size);

                    // 检查文件类型
                    if (!file.type.match('image.*')) {
                        console.warn('跳过非图片文件:', file.name);
                        continue;
                    }

                    const progressPercent = Math.round(((i + 1) / files.length) * 100);
                    updateAdminProgress(progressPercent, `正在处理第 ${i + 1} 张图片...`, file.name);

                    // 如果没有压缩器，直接使用简化预览
                    if (!adminCompressor) {
                        console.log('使用简化预览模式');
                        createSimplePreview(file);
                        continue;
                    }

                    try {
                        // 压缩图片
                        console.log('开始压缩图片:', file.name);
                        const result = await adminCompressor.compressFile(file);
                        console.log('图片压缩完成:', result);

                        // 验证压缩结果
                        if (!result || !result.file) {
                            throw new Error('压缩结果无效');
                        }

                        console.log('压缩后文件大小:', result.file.size, '字节');

                        // 创建预览容器
                        const div = document.createElement('div');
                        div.className = 'image-item';
                        div.style.position = 'relative';

                        // 添加临时加载占位符
                        const loadingPlaceholder = document.createElement('div');
                        loadingPlaceholder.className = 'loading-placeholder';
                        loadingPlaceholder.innerHTML = '加载中...';
                        div.appendChild(loadingPlaceholder);

                        const img = document.createElement('img');
                        img.style.cssText = `
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            display: none;
                            position: absolute;
                            top: 0;
                            left: 0;
                        `;

                        const removeBtn = document.createElement('span');
                        removeBtn.className = 'remove-image';
                        removeBtn.innerHTML = '×';
                        removeBtn.onclick = function() {
                            console.log('删除图片预览');
                            div.remove();
                        };

                        // 添加详细的压缩信息（后台管理版）
                        const infoPanel = document.createElement('div');
                        infoPanel.className = 'admin-image-info';
                        infoPanel.style.cssText = `
                            position: absolute; top: 0; left: 0; right: 0;
                            background: linear-gradient(rgba(0,0,0,0.8), transparent);
                            color: white; font-size: 11px; padding: 5px;
                            border-radius: 4px 4px 0 0; opacity: 0;
                            transition: opacity 0.3s;
                        `;

                        const originalSize = (file.size / 1024 / 1024).toFixed(2);
                        const compressedSize = (result.compressedSize / 1024 / 1024).toFixed(2);
                        const ratio = result.compressionRatio;
                        const dimensions = result.dimensions;

                        infoPanel.innerHTML = `
                            <div>原始: ${originalSize}MB</div>
                            <div>压缩: ${compressedSize}MB (${ratio})</div>
                            <div>尺寸: ${dimensions.width}×${dimensions.height}</div>
                        `;

                        // 鼠标悬停显示信息
                        div.addEventListener('mouseenter', () => {
                            infoPanel.style.opacity = '1';
                        });
                        div.addEventListener('mouseleave', () => {
                            infoPanel.style.opacity = '0';
                        });

                        // 先添加元素到DOM
                        div.appendChild(img);
                        div.appendChild(removeBtn);
                        div.appendChild(infoPanel);
                        preview.appendChild(div);

                        // 然后异步加载图片
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            console.log('FileReader读取完成，设置图片src');
                            img.src = e.target.result;
                            img.alt = '预览图片';

                            // 确保图片加载完成后显示
                            img.onload = function() {
                                console.log('图片预览加载完成:', file.name);
                                // 隐藏加载占位符，显示图片
                                loadingPlaceholder.style.display = 'none';
                                img.style.display = 'block';
                            };

                            img.onerror = function() {
                                console.error('图片预览加载失败:', file.name);
                                loadingPlaceholder.innerHTML = '预览失败';
                                loadingPlaceholder.style.color = '#ff4d4f';
                            };
                        };

                        reader.onerror = function() {
                            console.error('FileReader读取失败:', file.name);
                            loadingPlaceholder.innerHTML = '读取失败';
                            loadingPlaceholder.style.color = '#ff4d4f';
                        };

                        console.log('开始读取文件为DataURL');
                        reader.readAsDataURL(result.file);

                    } catch (error) {
                        console.error('图片压缩失败:', error);
                        console.log('使用简化预览作为备用方案');

                        // 如果压缩失败，使用简化预览
                        createSimplePreview(file);
                    }
                }

                updateAdminProgress(100, '所有图片处理完成', '');

            } catch (error) {
                console.error('图片处理出错:', error);
                alert('图片处理出错: ' + error.message);
                updateAdminProgress(0, '', '');
            }
        });
        
        // 处理已有图片的删除
        const removeButtons = document.querySelectorAll('.remove-image[data-id]');
        removeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const imageId = this.getAttribute('data-id');
                const postId = '<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>'; // 获取当前信息ID
                const imageItem = this.closest('.image-item');
                
                if(!confirm('确定要删除这张图片吗？')) {
                    return false;
                }
                
                // 发送Ajax请求删除图片
                fetch('info.php?action=delete_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'id=' + imageId + '&post_id=' + postId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 删除成功，移除图片预览
                        imageItem.remove();
                    } else {
                        alert(data.message || '删除图片失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除图片失败，请稍后重试');
                });
            });
        });
    }
});
</script>

<!-- 保存成功弹出层 -->
<div id="saveSuccessModal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="modal-close">&times;</span>
        <div class="modal-header">
            <h4>保存成功</h4>
        </div>
        <div class="modal-body">
            <p>信息已成功保存，您可以选择：</p>
            <div class="modal-buttons">
                <button id="viewFrontend" class="btn btn-light-info">查看前台</button>
                <button id="continueEdit" class="btn btn-light-primary">继续编辑</button>
            </div>
        </div>
    </div>
</div>

<style>
/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fff;
    border-radius: 5px;
    width: 400px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    padding: 5px 10px;
}

.modal-close:hover {
    color: black;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h4 {
    margin: 0;
    font-size: 18px;
}

.modal-body {
    padding: 20px;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 10px;
}
</style>

<script>
// 分类数据
const categoryData = {
    <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $category): ?>
    <?php echo (isset($category['id'])) ? $category['id'] : ""; ?>: {
        name: '<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>',
        children: [
            <?php if(null !== ($category ?? null) && is_array($category['children'])): foreach($category['children'] as $child): ?>
            {id: <?php echo (isset($child['id'])) ? $child['id'] : ""; ?>, name: '<?php echo (isset($child['name'])) ? $child['name'] : ""; ?>'},
            <?php endforeach; endif; ?>
        ]
    },
    <?php endforeach; endif; ?>
};

// 当前选中的分类ID
const currentCategoryId = <?php if($post['category_id']): ?><?php echo (isset($post['category_id'])) ? $post['category_id'] : ""; ?><?php else: ?>0<?php endif; ?>;

// 更新子分类选项
function updateSubCategories() {
    const parentSelect = document.getElementById('parent_category_id');
    const subSelect = document.getElementById('category_id');
    const subGroup = document.getElementById('sub_category_group');
    const parentId = parentSelect.value;

    // 清空子分类选项
    subSelect.innerHTML = '<option value="">请选择小分类</option>';

    if (parentId && categoryData[parentId]) {
        const isLeaf = parentSelect.options[parentSelect.selectedIndex].getAttribute('data-is-leaf') === '1';

        if (isLeaf) {
            // 如果是叶子节点（没有子分类），隐藏子分类选择框，直接设置分类ID
            subGroup.style.display = 'none';
            subSelect.removeAttribute('required');
            // 创建一个隐藏的input来提交分类ID
            let hiddenInput = document.getElementById('hidden_category_id');
            if (!hiddenInput) {
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.id = 'hidden_category_id';
                hiddenInput.name = 'category_id';
                parentSelect.parentNode.appendChild(hiddenInput);
            }
            hiddenInput.value = parentId;
        } else {
            // 如果有子分类，显示子分类选择框
            subGroup.style.display = 'block';
            subSelect.setAttribute('required', 'required');

            // 移除隐藏的input
            const hiddenInput = document.getElementById('hidden_category_id');
            if (hiddenInput) {
                hiddenInput.remove();
            }

            // 添加子分类选项
            categoryData[parentId].children.forEach(function(child) {
                const option = document.createElement('option');
                option.value = child.id;
                option.textContent = child.name;
                if (child.id == currentCategoryId) {
                    option.selected = true;
                }
                subSelect.appendChild(option);
            });
        }
    } else {
        // 没有选择父分类，显示子分类选择框但禁用
        subGroup.style.display = 'block';
        subSelect.removeAttribute('required');

        // 移除隐藏的input
        const hiddenInput = document.getElementById('hidden_category_id');
        if (hiddenInput) {
            hiddenInput.remove();
        }
    }
}

// 根据当前分类ID找到对应的父分类ID
function findParentCategoryId(categoryId) {
    for (const parentId in categoryData) {
        // 检查是否是父分类本身
        if (parentId == categoryId) {
            return parentId;
        }
        // 检查是否在子分类中
        for (const child of categoryData[parentId].children) {
            if (child.id == categoryId) {
                return parentId;
            }
        }
    }
    return null;
}

// 检查是否需要显示保存成功弹窗
document.addEventListener('DOMContentLoaded', function() {
    // 初始化分类选择
    if (currentCategoryId > 0) {
        // 编辑模式：根据当前分类ID设置父分类选择
        const parentId = findParentCategoryId(currentCategoryId);

        if (parentId) {
            const parentSelect = document.getElementById('parent_category_id');
            parentSelect.value = parentId;

            // 设置选中状态
            for (let i = 0; i < parentSelect.options.length; i++) {
                if (parentSelect.options[i].value == parentId) {
                    parentSelect.options[i].selected = true;
                    break;
                }
            }
        }
    }

    // 更新子分类选项
    updateSubCategories();
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('saved') === '1') {
        // 显示保存成功弹窗
        const modal = document.getElementById('saveSuccessModal');
        if (modal) {
            modal.style.display = 'flex';
            
            // 点击关闭按钮关闭弹窗
            const closeBtn = modal.querySelector('.modal-close');
            if (closeBtn) {
                closeBtn.onclick = function() {
                    modal.style.display = 'none';
                }
            }
            
            // 点击弹窗外区域关闭弹窗
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            }
            
            // 设置"查看前台"按钮链接
            const viewFrontendBtn = document.getElementById('viewFrontend');
            if (viewFrontendBtn) {
                viewFrontendBtn.onclick = function() {
                    <?php if($action == 'edit' && $post['id']): ?>
                    <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('category_pinyin', $post) && $post['category_pinyin']): ?>
                    window.open('../<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html', '_blank');
                    <?php else: ?>
                    window.open('../view.php?id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>', '_blank');
                    <?php endif; ?>
                    <?php endif; ?>
                    modal.style.display = 'none';
                }
            }
            
            // 设置"继续编辑"按钮
            const continueEditBtn = document.getElementById('continueEdit');
            if (continueEditBtn) {
                continueEditBtn.onclick = function() {
                    modal.style.display = 'none';
                }
            }
        }
        
        // 清除URL中的saved参数，防止刷新页面再次显示弹窗
        const newUrl = window.location.href.replace('&saved=1', '').replace('?saved=1&', '?').replace('?saved=1', '');
        window.history.replaceState({}, document.title, newUrl);
    }
});
</script>
<script src="/static/js/image-compress.js"></script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>