<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1755401031,
  'data' => 
  array (
    'news_list' => 
    array (
      0 => 
      array (
        'id' => '43950',
        'catid' => '7',
        'title' => '测试文章943 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '416',
        'is_top' => '1',
        'is_recommend' => '0',
        'addtime' => '1744853956',
        'description' => '这是第943篇测试文章的内容。所属栏目：新闻资讯生成时间：2025-05-13 18:17:28这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      1 => 
      array (
        'id' => '43971',
        'catid' => '10',
        'title' => 'sdssdadsa',
        'author' => 'dsadsdsdsa',
        'click' => '0',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1754284083',
        'description' => 'dsadssddsaasdsdsa',
        'thumb' => '',
        'catname' => '测试栏目',
      ),
      2 => 
      array (
        'id' => '43970',
        'catid' => '10',
        'title' => '啊实打实的撒大苏打',
        'author' => '阿斯顿萨达萨达是',
        'click' => '4',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1754004996',
        'description' => '111111111111111111111111111111111111111111111111',
        'thumb' => '111',
        'catname' => '测试栏目',
      ),
      3 => 
      array (
        'id' => '43969',
        'catid' => '7',
        'title' => '啊实打实大苏打的',
        'author' => '啊实打实的',
        'click' => '11',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1753691189',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      4 => 
      array (
        'id' => '43968',
        'catid' => '8',
        'title' => 'asdsaas',
        'author' => '新闻测试测试测试',
        'click' => '39',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1753664818',
        'description' => '红星资本局注意到，昨日，开市客批量下架了麻六记酸辣粉，或与多名消费者投诉产品质量有关。据南方都市报报道，7月25日，开市客上海、深圳、苏州、杭州六地门店客服均回应麻六记酸辣粉确已全部下架。同时，在开市客App也检索不到麻六记的相关产品。针对门店下架麻六记，开市客相关负责人回应称，是在近期例行商品品质巡检中，发现该产品存在质量波动。出于商品质量管控要求，主动暂停该产品销售。麻六记酸辣粉在开市客目前没',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      5 => 
      array (
        'id' => '19650',
        'catid' => '7',
        'title' => '测试文章643 - 泊头新闻的子栏目222233',
        'author' => '系统管理员',
        'click' => '373',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126777',
        'description' => 'meta公司近日宣布了一项重大人事任命，赵晟佳将担任其全新成立的“超级智能实验室”（MSL）的首席科学家。赵晟佳，这位在AI界迅速崭露头角的年轻科学家，曾是OpenAI的核心研究员，参与了ChatGPT、GPT-4等多个重要AI模型的研发，与OpenAI联合创始人Ilya Sutskever共同被视为o1模型的“奠基者”。meta首席执行官马克·扎克伯格亲自宣布了这一消息，称赞赵晟佳为新实验室的联',
        'thumb' => '/uploads/images/2025/0726/1753510734555963.jpg',
        'catname' => '泊头新闻的子栏目',
      ),
      6 => 
      array (
        'id' => '17020',
        'catid' => '6',
        'title' => '测试文章13 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '781',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126736',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      7 => 
      array (
        'id' => '15512',
        'catid' => '7',
        'title' => '测试文章505 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '152',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126723',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      8 => 
      array (
        'id' => '16647',
        'catid' => '7',
        'title' => '测试文章640 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '475',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126707',
        'description' => '这是第640篇测试文章的内容。所属栏目：泊头新闻的子栏目生成时间：2025-05-13 18:14:59这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      9 => 
      array (
        'id' => '14838',
        'catid' => '8',
        'title' => '测试文章831 - 子栏目',
        'author' => '系统管理员',
        'click' => '657',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126689',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
    ),
    'total_row' => 
    array (
      'total' => '43886',
    ),
  ),
);
