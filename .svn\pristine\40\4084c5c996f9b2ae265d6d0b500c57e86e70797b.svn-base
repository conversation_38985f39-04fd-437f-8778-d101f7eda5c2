<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>
 

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
.dropdown-toggle::after {
    display: none !important;
}
.dropdown-item {
    text-decoration: none;
    padding: 0.25rem 1rem;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.filter-tag.active-all { background-color: #007bff; }
.filter-tag.inactive-all { background-color: #6c757d; }
.filter-tag.active-pending { background-color: #007bff; }
.filter-tag.inactive-pending { background-color: #6c757d; }
.filter-tag.active-online { background-color: #007bff; }
.filter-tag.inactive-online { background-color: #6c757d; }
.filter-tag.active-offline { background-color: #007bff; }
.filter-tag.inactive-offline { background-color: #6c757d; }
.filter-tag.active-deleted { background-color: #007bff; }
.filter-tag.inactive-deleted { background-color: #6c757d; }
.filter-tag.active-expired { background-color: #007bff; }
.filter-tag.inactive-expired { background-color: #6c757d; }
.filter-tag.active-top { background-color: #ff4d4f; }
.filter-tag.inactive-top { background-color: #6c757d; }
.filter-tag.active-home { background-color: #ff4d4f; }
.filter-tag.inactive-home { background-color: #6c757d; }
.filter-tag.active-category { background-color: #fa8c16; }
.filter-tag.inactive-category { background-color: #6c757d; }
.filter-tag.active-subcategory { background-color: #52c41a; }
.filter-tag.inactive-subcategory { background-color: #6c757d; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-checkbox { width: 40px; }
.table .col-id { width: 60px; }
.table .col-title { width: 380px; }
.table .col-category { width: 120px; }
.table .col-top { width: 80px; }
.table .col-time { width: 120px; }
.table .col-created { width: 120px; }
.table .col-expire { width: 80px; }
.table .col-actions { width: 180px; }
</style>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success"><?php echo $message ?? ""; ?></div>
<?php endif; ?>
<?php if($error): ?>
<div class="alert alert-danger"><?php echo $error ?? ""; ?></div>
<?php endif; ?>

<!-- 信息管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速筛选按钮 -->
        <div style="margin-bottom: 20px;">
            <div style="display: flex; flex-wrap: wrap;">
                <a href="info.php?status=1" class="filter-tag <?php if($status == 1 && $is_expired != 1 && empty($id_value) && empty($keyword) && empty($top_type)): ?>active-all<?php else: ?>inactive-all<?php endif; ?>">正常信息</a>
                <a href="info.php?status=0" class="filter-tag <?php if(null !== ($status ?? null) && $status == 0): ?>active-pending<?php else: ?>inactive-pending<?php endif; ?>">待审核</a>
                <a href="info.php?status=2" class="filter-tag <?php if(null !== ($status ?? null) && $status == 2): ?>active-offline<?php else: ?>inactive-offline<?php endif; ?>">已下架</a>
                <a href="info.php?status=3" class="filter-tag <?php if(null !== ($status ?? null) && $status == 3): ?>active-deleted<?php else: ?>inactive-deleted<?php endif; ?>">用户删除</a>
                <a href="info.php?is_expired=1" class="filter-tag <?php if(null !== ($is_expired ?? null) && $is_expired == 1): ?>active-expired<?php else: ?>inactive-expired<?php endif; ?>">已过期</a>

                <!-- 置顶筛选 -->
                <a href="info.php?top_type=all_top" class="filter-tag <?php if($top_type == 'all_top'): ?>active-top<?php else: ?>inactive-top<?php endif; ?>">全部置顶</a>
                <a href="info.php?top_type=home" class="filter-tag <?php if($top_type == 'home'): ?>active-home<?php else: ?>inactive-home<?php endif; ?>">首页置顶</a>
                <a href="info.php?top_type=category" class="filter-tag <?php if($top_type == 'category'): ?>active-category<?php else: ?>inactive-category<?php endif; ?>">分类置顶</a>
                <a href="info.php?top_type=subcategory" class="filter-tag <?php if($top_type == 'subcategory'): ?>active-subcategory<?php else: ?>inactive-subcategory<?php endif; ?>">子分类置顶</a>

                <?php if(null !== ($id_value ?? null) && !empty($id_value) || null !== ($keyword ?? null) && !empty($keyword)): ?>
                <span class="filter-tag active-all">全局搜索: <?php if(null !== ($id_value ?? null) && !empty($id_value)): ?>ID:<?php echo $id_value ?? ""; ?><?php elseif(null !== ($keyword ?? null) && !empty($keyword)): ?>关键词:<?php echo $keyword ?? ""; ?><?php endif; ?></span>
                <?php endif; ?>

                <?php if(null !== ($top_type ?? null) && !empty($top_type)): ?>
                <span class="filter-tag active-top">置顶筛选:
                    <?php if($top_type == 'all_top'): ?>全部置顶
                    <?php elseif($top_type == 'home'): ?>首页置顶
                    <?php elseif($top_type == 'category'): ?>分类置顶
                    <?php elseif($top_type == 'subcategory'): ?>子分类置顶
                    <?php endif; ?>
                </span>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 筛选功能 -->
        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
            <form action="info.php" method="get" style="display: flex; align-items: center; flex-wrap: wrap; gap: 15px;">

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">分类:</span>
                    <select name="parent_category_id" id="parentCategory" onchange="updateFilterSubCategories()" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                        <option value="0">不限大分类</option>
                        <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $category): ?>
                        <option value="<?php echo (isset($category['id'])) ? $category['id'] : ""; ?>" data-is-leaf="<?php if(empty($category['children'])): ?>1<?php else: ?>0<?php endif; ?>"><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></option>
                        <?php endforeach; endif; ?>
                    </select>
                    <select name="category_id" id="subCategory" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px; margin-left: 5px;">
                        <option value="0">不限小分类</option>
                    </select>
                    <input type="hidden" id="hiddenCategoryId" name="category_id" value="<?php echo $category_id ?? ""; ?>">
                </div>

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">ID:</span>
                    <input type="text" name="id" value="<?php echo $id_value ?? ""; ?>" placeholder="输入ID..." style="width: 100px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                </div>
                
                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">关键词:</span>
                    <input type="text" name="keyword" value="<?php echo $keyword ?? ""; ?>" placeholder="搜索标题、内容..." style="width: 200px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                </div>

                <div style="display: flex; align-items: center; white-space: nowrap;">
                    <span style="margin-right: 5px; color: #333;">置顶:</span>
                    <select name="top_type" style="width: 120px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                        <option value="">不限</option>
                        <option value="all_top" <?php if($top_type == 'all_top'): ?>selected<?php endif; ?>>全部置顶</option>
                        <option value="home" <?php if($top_type == 'home'): ?>selected<?php endif; ?>>首页置顶</option>
                        <option value="category" <?php if($top_type == 'category'): ?>selected<?php endif; ?>>分类置顶</option>
                        <option value="subcategory" <?php if($top_type == 'subcategory'): ?>selected<?php endif; ?>>子分类置顶</option>
                    </select>
                </div>

                <div>
                    <button type="submit" class="btn btn-sm btn-light-primary" style="margin-right: 5px; height: 32px; line-height: 1; padding: 0 12px;">筛选</button>
                    <a href="info.php" class="btn btn-sm btn-light-secondary" style="height: 32px; line-height: 1; padding: 0 12px;">重置</a>
                </div>
                
                <div style="margin-left: auto;">
                    <a href="info.php?action=add" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 1; padding: 0 12px;">添加信息</a>
                </div>
            </form>
        </div>
        
        <!-- 信息列表 -->
        <form id="postForm" action="" method="post">
            <div class="table-responsive">
                <table class="table table-vcenter table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="col-checkbox"></th>
                            <th class="col-id">ID</th>
                            <th class="col-title">标题</th>
                            <th class="col-category">分类</th>
                            <th class="col-top">置顶状态</th>
                            <th class="col-created">发布时间</th>
                            <th class="col-time">更新时间</th>
                            <th class="col-expire">过期时间</th>
                            <th class="col-actions" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(!$posts): ?>
                        <tr>
                            <td colspan="9" class="text-center">暂无信息数据</td>
                        </tr>
                        <?php else: ?>
                        <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="post_ids[]" class="post-checkbox" value="<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>">
                            </td>
                            <td><?php echo (isset($post['id'])) ? $post['id'] : ""; ?></td>
                            <td title="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>" style="font-weight: 500;">
                                <a href="info.php?action=edit&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" target="_blank" style="color: #333; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: block;">
                                    <?php echo (isset($post['title'])) ? $post['title'] : ""; ?>
                                    <?php if($post['image_count'] > 0): ?>
                                    <span style="display: inline-block; margin-left: 5px; padding: 1px 5px; font-size: 12px; background-color: #e6f7ff; color: #1890ff; border-radius: 3px; font-weight: normal;">图<?php echo (isset($post['image_count'])) ? $post['image_count'] : ""; ?></span>
                                    <?php endif; ?>
                                </a>
                            </td>
                            <td><?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?></td>
                            <td>
                                <?php 
                                $current_time = time();
                                $top_status = array();

                                // 检查首页置顶
                                if (!empty($post['is_top_home']) && $post['is_top_home'] == 1) {
                                    if (empty($post['top_home_expire']) || $post['top_home_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #ff4d4f; color: white; border-radius: 3px;">首页</span>';
                                    }
                                }

                                // 检查分类置顶
                                if (!empty($post['is_top_category']) && $post['is_top_category'] == 1) {
                                    if (empty($post['top_category_expire']) || $post['top_category_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #fa8c16; color: white; border-radius: 3px;">分类</span>';
                                    }
                                }

                                // 检查子分类置顶
                                if (!empty($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
                                    if (empty($post['top_subcategory_expire']) || $post['top_subcategory_expire'] > $current_time) {
                                        $top_status[] = '<span style="display: inline-block; margin: 1px; padding: 2px 6px; font-size: 11px; background-color: #52c41a; color: white; border-radius: 3px;">子分类</span>';
                                    }
                                }

                                if (empty($top_status)) {
                                    echo '<span style="color: #999;">--</span>';
                                } else {
                                    echo implode('', $top_status);
                                }
                                 ?>
                            </td>
                            <td><?php echo null !== ((null !== ($post ?? null)) ? ($post['created_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['created_at']) : null) : ""; ?></td>
                            <td><?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?></td>
                            <td>
                                <?php 
                                if (!empty($post['expired_at'])) {
                                    $days = getRemainingDaysInt($post['expired_at']);
                                    $guoqi = $days > 0 ? $days.'天' : '已过期';
                                } else {
                                    $guoqi = '长期';
                                }
                                 ?>
                                <?php echo $guoqi ?? ""; ?>
                            </td>
                            <td>
                                <div class="info-actions">
                                    <a href="info.php?action=edit&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" target="_blank" class="btn btn-sm btn-light-primary">编辑</a>
                                    <?php if($post['status'] == 0): ?>
                                    <a href="info.php?action=toggle_status&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>&to=1" class="btn btn-sm btn-light-success">通过</a>
                                    <?php elseif($post['status'] == 1): ?>
                                    <a href="info.php?action=toggle_status&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>&to=2" class="btn btn-sm btn-light-warning">下架</a>
                                    <?php elseif($post['status'] == 2): ?>
                                    <a href="info.php?action=toggle_status&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>&to=1" class="btn btn-sm btn-light-info">上架</a>
                                    <?php elseif($post['status'] == 3): ?>
                                    <a href="info.php?action=toggle_status&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>&to=0" class="btn btn-sm btn-light-secondary">恢复</a>
                                    <?php endif; ?>
                                    <a href="info.php?action=delete&id=<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这条信息吗？删除后无法恢复！')">删除</a>
                                    <a href="../<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" target="_blank" class="btn btn-sm btn-light-info">查看前台</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    <?php if($pagination['total_pages'] > 1): ?>
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            <?php if($pagination['current_page'] > 1): ?>
                            <a href="<?php echo (isset($pagination['previous_link'])) ? $pagination['previous_link'] : ""; ?>" class="pagination-btn">上一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">上一页</span>
                            <?php endif; ?>
                            
                            <?php if(null !== ($pagination ?? null) && is_array($pagination['page_links'])): foreach($pagination['page_links'] as $page => $link): ?>
                            <a href="<?php echo $link ?? ""; ?>" class="pagination-btn <?php if($page == $pagination['current_page']): ?>active<?php endif; ?>"><?php echo $page ?? ""; ?></a>
                            <?php endforeach; endif; ?>
                            
                            <?php if($pagination['current_page'] < $pagination['total_pages']): ?>
                            <a href="<?php echo (isset($pagination['next_link'])) ? $pagination['next_link'] : ""; ?>" class="pagination-btn">下一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">下一页</span>
                            <?php endif; ?>
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 <?php echo (isset($pagination['total_pages'])) ? $pagination['total_pages'] : ""; ?> 页</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html> 

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选
    var selectAll = document.getElementById('selectAll');
    var checkboxes = document.getElementsByClassName('post-checkbox');
    
    // 顶部全选按钮事件
    selectAll.addEventListener('change', function() {
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = selectAll.checked;
        }
    });
    
    // 当单个checkbox改变时，检查是否需要更新全选框状态
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < checkboxes.length; j++) {
                if (!checkboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            // 同步顶部全选按钮
            selectAll.checked = allChecked;
        });
    }
    
    // 批量删除
    var batchDeleteBtn = document.getElementById('batchDeleteBtn');
    var postForm = document.getElementById('postForm');
    
    function handleBatchDelete() {
        var checkedCount = 0;
        for (var i = 0; i < checkboxes.length; i++) {
            if (checkboxes[i].checked) {
                checkedCount++;
            }
        }
        
        if (checkedCount === 0) {
            alert('请至少选择一条信息进行删除');
            return;
        }
        
        if (confirm('确定要删除选中的 ' + checkedCount + ' 条信息吗？删除后无法恢复！')) {
            // 设置表单提交到批量删除处理页面
            postForm.action = '?action=batch_delete';
            postForm.submit();
        }
    }
    
    // 顶部批量删除按钮事件
    batchDeleteBtn.addEventListener('click', handleBatchDelete);

    // 分类联动功能
    var parentCategory = document.getElementById('parentCategory');
    var subCategory = document.getElementById('subCategory');
    var hiddenCategoryId = document.getElementById('hiddenCategoryId');

    // 使用新的分类树数据结构
    var categoryData = {
        <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $category): ?>
        <?php echo (isset($category['id'])) ? $category['id'] : ""; ?>: {
            name: '<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>',
            children: [
                <?php if(null !== ($category ?? null) && is_array($category['children'])): foreach($category['children'] as $child): ?>
                {id: <?php echo (isset($child['id'])) ? $child['id'] : ""; ?>, name: '<?php echo (isset($child['name'])) ? $child['name'] : ""; ?>'},
                <?php endforeach; endif; ?>
            ]
        },
        <?php endforeach; endif; ?>
    };

    // 当前选中的分类ID
    var currentCategoryId = <?php if($category_id): ?><?php echo $category_id ?? ""; ?><?php else: ?>0<?php endif; ?>;

    function updateFilterSubCategories() {
        var parentId = parentCategory.value;
        subCategory.innerHTML = '<option value="0">不限小分类</option>';

        if (parentId != '0' && categoryData[parentId]) {
            var isLeaf = parentCategory.options[parentCategory.selectedIndex].getAttribute('data-is-leaf') === '1';

            if (isLeaf) {
                // 没有子分类，隐藏子分类选择框，直接使用父分类ID
                subCategory.style.opacity = '0.5';
                subCategory.disabled = true;
                hiddenCategoryId.value = parentId;
            } else {
                // 有子分类，显示子分类选择框
                subCategory.style.opacity = '1';
                subCategory.disabled = false;
                hiddenCategoryId.value = '0';

                categoryData[parentId].children.forEach(function(child) {
                    var option = document.createElement('option');
                    option.value = child.id;
                    option.text = child.name;
                    if (currentCategoryId == child.id) {
                        option.selected = true;
                    }
                    subCategory.appendChild(option);
                });
            }
        } else {
            // 未选择父分类
            subCategory.style.opacity = '0.5';
            subCategory.disabled = true;
            hiddenCategoryId.value = '0';
        }
    }

    // 子分类选择变化时更新隐藏字段
    subCategory.addEventListener('change', function() {
        if (!this.disabled && this.value != '0') {
            hiddenCategoryId.value = this.value;
        }
    });

    // 初始化分类选择状态
    if (currentCategoryId > 0) {
        // 查找父分类
        for (var parentId in categoryData) {
            if (parentId == currentCategoryId) {
                // 选中的是父分类
                parentCategory.value = parentId;
                break;
            }

            // 检查子分类
            for (var i = 0; i < categoryData[parentId].children.length; i++) {
                if (categoryData[parentId].children[i].id == currentCategoryId) {
                    parentCategory.value = parentId;
                    break;
                }
            }
        }
    }

    parentCategory.addEventListener('change', updateFilterSubCategories);
    updateFilterSubCategories(); // 初始化子分类
});
</script> 