<?php
/**
 * 高效文件缓存类
 * 用于缓存分类数据、区域数据和查询结果
 */
class FileCache {
    private $cacheDir;
    private $defaultExpire;
    
    public function __construct($cacheDir = null, $defaultExpire = 3600) {
        $this->cacheDir = $cacheDir ?: DATA_PATH . 'cache/';
        $this->defaultExpire = $defaultExpire;
        
        // 确保缓存目录存在
        if (!is_dir($this->cacheDir)) {
            @mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * 生成缓存文件路径
     */
    private function getCacheFile($key) {
        // 生成有意义的缓存文件名
        $filename = $this->generateMeaningfulFilename($key);
        return $this->cacheDir . $filename . '.cache';
    }

    /**
     * 生成有意义的缓存文件名
     */
    private function generateMeaningfulFilename($key) {
        // 缓存类型映射
        $cacheTypes = array(
            // 分类相关缓存
            'categories_all' => 'category_all',
            'categories_tree' => 'category_tree',
            'category_' => 'category_',
            'category_posts_' => 'category_posts_',
            'category_count_' => 'category_count_',

            // 区域相关缓存
            'regions_all' => 'region_all',
            'regions_tree' => 'region_tree',
            'region_' => 'region_',
            'region_posts_' => 'region_posts_',
            'region_count_' => 'region_count_',

            // 信息详情缓存
            'post_detail_' => 'detail_',
            'post_' => 'detail_',

            // 列表页缓存
            'list_' => 'list_',
            'posts_list_' => 'list_',
            'search_' => 'search_',

            // 首页缓存
            'index_' => 'index_',
            'home_' => 'index_',

            // 用户相关缓存
            'user_' => 'user_',
            'user_posts_' => 'user_posts_',

            // 统计缓存
            'stats_' => 'stats_',
            'count_' => 'count_',

            // 新闻缓存
            'news_' => 'news_',
            'news_home_' => 'news_home_',
            'news_list_' => 'news_list_',
            'news_detail_' => 'news_detail_',
            'news_hot_' => 'news_hot_',
            'news_recommend_' => 'news_recommend_',
            'news_categories' => 'news_categories',
        );

        // 检查是否匹配已知的缓存类型
        foreach ($cacheTypes as $pattern => $prefix) {
            if (strpos($key, $pattern) === 0) {
                // 提取ID或参数部分
                $suffix = substr($key, strlen($pattern));
                if (!empty($suffix)) {
                    // 清理后缀，只保留字母数字和下划线
                    $suffix = preg_replace('/[^a-zA-Z0-9_]/', '_', $suffix);
                    $suffix = trim($suffix, '_');
                    if (strlen($suffix) > 20) {
                        $suffix = substr($suffix, 0, 20) . '_' . substr(md5($suffix), 0, 8);
                    }
                    return $prefix . $suffix;
                } else {
                    return rtrim($prefix, '_');
                }
            }
        }

        // 如果没有匹配到已知类型，使用原来的MD5方式，但加上类型前缀
        $hash = md5($key);

        // 尝试从key中提取类型信息
        if (preg_match('/^([a-zA-Z]+)/', $key, $matches)) {
            $type = strtolower($matches[1]);
            return $type . '_' . substr($hash, 0, 16);
        }

        return 'misc_' . substr($hash, 0, 16);
    }
    
    /**
     * 设置缓存
     */
    public function set($key, $data, $expire = null) {
        $expire = $expire ?: $this->defaultExpire;
        $cacheFile = $this->getCacheFile($key);
        
        $cacheData = array(
            'expire' => time() + $expire,
            'data' => $data
        );
        
        $content = "<?php\nif (!defined('IN_BTMPS')) { exit('Access Denied'); }\nreturn " . var_export($cacheData, true) . ";\n";
        
        return @file_put_contents($cacheFile, $content, LOCK_EX) !== false;
    }
    
    /**
     * 获取缓存
     */
    public function get($key) {
        $cacheFile = $this->getCacheFile($key);
        
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $cacheData = @include $cacheFile;
        
        if (!is_array($cacheData) || !isset($cacheData['expire']) || !isset($cacheData['data'])) {
            return false;
        }
        
        // 检查是否过期
        if (time() > $cacheData['expire']) {
            @unlink($cacheFile);
            return false;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * 删除缓存
     */
    public function delete($key) {
        $cacheFile = $this->getCacheFile($key);
        return @unlink($cacheFile);
    }
    
    /**
     * 清空所有缓存
     */
    public function clear() {
        $files = glob($this->cacheDir . '*.cache');
        $count = 0;
        
        foreach ($files as $file) {
            if (@unlink($file)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 检查缓存是否存在且未过期
     */
    public function exists($key) {
        return $this->get($key) !== false;
    }
    
    /**
     * 获取缓存目录路径
     */
    public function getCacheDir() {
        return $this->cacheDir;
    }

    /**
     * 获取缓存信息
     */
    public function getInfo() {
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $count = 0;
        $fileList = array();
        
        foreach ($files as $file) {
            $size = filesize($file);
            $totalSize += $size;
            $count++;
            
            $fileList[] = array(
                'file' => basename($file),
                'size' => $size,
                'mtime' => filemtime($file)
            );
        }
        
        return array(
            'count' => $count,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'files' => $fileList
        );
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

/**
 * 全局缓存实例
 */
$GLOBALS['file_cache'] = new FileCache();

/**
 * 缓存辅助函数
 */
function cache_get($key) {
    return $GLOBALS['file_cache']->get($key);
}

function cache_set($key, $data, $expire = null) {
    return $GLOBALS['file_cache']->set($key, $data, $expire);
}

function cache_delete($key) {
    return $GLOBALS['file_cache']->delete($key);
}

function cache_clear() {
    return $GLOBALS['file_cache']->clear();
}

function cache_exists($key) {
    return $GLOBALS['file_cache']->exists($key);
}

/**
 * 获取缓存的分类帖子统计
 */
function getCachedCategoryPostsCount($catId, $subCatId = 0, $areaId = 0, $isProvinceArea = false) {
    global $config;

    // 检查全局缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_data_time = isset($config['cache_data']) ? intval($config['cache_data']) : 600;

    $cacheKey = "category_posts_count_{$catId}_{$subCatId}_{$areaId}_" . ($isProvinceArea ? '1' : '0');

    // 只有在缓存开启且缓存时间大于0时才使用缓存
    if ($cache_enable && $cache_data_time > 0) {
        $cached = cache_get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 从数据库获取数据
    $count = getCategoryPostsCount($catId, $subCatId, $areaId, $isProvinceArea);

    // 只有在缓存开启且缓存时间大于0时才设置缓存
    if ($cache_enable && $cache_data_time > 0) {
        cache_set($cacheKey, $count, $cache_data_time);
    }

    return $count;
}

/**
 * 清除分类相关缓存
 */
function clearCategoryCache() {
    cache_delete('categories_all');

    // 同时清理首页缓存，因为首页包含分类数据
    cache_delete('index_page_pc');
    cache_delete('index_page_m');
    cache_delete('index_page_wx');
    cache_delete('index_page_app');

    // 清除所有分类相关的缓存
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    foreach ($files as $file) {
        $filename = basename($file);
        // 清除分类帖子统计缓存
        if (strpos($filename, md5('category_posts_count_')) === 0) {
            @unlink($file);
        }
        // 清除分类帖子数据缓存
        if (strpos($filename, md5('category_posts_')) === 0) {
            @unlink($file);
        }
        // 清除分类列表缓存
        if (strpos($filename, 'category_list_') === 0) {
            @unlink($file);
        }
    }

    // 记录缓存清理日志
    error_log('[Cache] 分类缓存已清理 - ' . date('Y-m-d H:i:s'));
}

/**
 * 清除区域相关缓存
 */
function clearRegionCache() {
    cache_delete('regions_all');

    // 清除所有区域相关的缓存
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    foreach ($files as $file) {
        $filename = basename($file);
        // 清除包含区域筛选的帖子缓存
        if (strpos($filename, md5('category_posts_')) === 0) {
            $content = @file_get_contents($file);
            if ($content && strpos($content, '_area_') !== false) {
                @unlink($file);
            }
        }
    }

    // 记录缓存清理日志
    error_log('[Cache] 区域缓存已清理 - ' . date('Y-m-d H:i:s'));
}

/**
 * 清除导航相关缓存
 */
function clearNavigationCache() {
    cache_delete('navigation_all');

    // 记录缓存清理日志
    error_log('[Cache] 导航缓存已清理 - ' . date('Y-m-d H:i:s'));
}

/**
 * 清理信息详情缓存（所有端）
 * @param int $post_id 信息ID
 */
function clearPostDetailCache($post_id) {
    // 清理所有版本的详情缓存（PC版、手机版、微信版、APP版）
    $template_types = array('pc', 'm', 'wx', 'app');

    foreach ($template_types as $template_type) {
        // 使用实际的缓存键名格式：detail_{id}_{template}
        $cache_key = "detail_{$post_id}_{$template_type}";
        cache_delete($cache_key);
    }

    // 为了兼容旧版本，也清理其他可能的缓存键名格式
    $old_cache_keys = array(
        "post_detail_{$post_id}",
        "detail_{$post_id}",
        "post_{$post_id}"
    );

    foreach ($old_cache_keys as $old_cache_key) {
        cache_delete($old_cache_key);
    }

    // 清理图片缓存（重要：修改图片后必须清理）
    $image_cache_key = "post_images_{$post_id}";
    cache_delete($image_cache_key);

    // 记录缓存清理日志
    error_log("[Cache] 信息详情缓存已清理 - post_id: {$post_id} (包含图片缓存) - " . date('Y-m-d H:i:s'));
}



/**
 * 清理首页缓存（所有端）
 */
function clearHomepageCache() {
    $template_types = array('pc', 'm', 'wx', 'app');

    foreach ($template_types as $template_type) {
        $cache_key = "index_page_{$template_type}";
        cache_delete($cache_key);
    }

    // 清理其他首页相关缓存
    $homepage_patterns = array(
        'index_',
        'homepage_',
        'top_posts_',
        'latest_posts_',
        'featured_posts_'
    );

    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    foreach ($files as $file) {
        $filename = basename($file, '.cache');
        foreach ($homepage_patterns as $pattern) {
            if (strpos($filename, $pattern) === 0) {
                @unlink($file);
                break;
            }
        }
    }

    // 记录缓存清理日志
    error_log('[Cache] 首页缓存已清理 - ' . date('Y-m-d H:i:s'));
}

/**
 * 清理分类相关的所有缓存
 * @param int $category_id 分类ID
 */
function clearCategoryRelatedCache($category_id) {
    // 清理分类列表缓存（所有分页、所有排序、所有筛选条件）
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    foreach ($files as $file) {
        $filename = basename($file, '.cache');

        // 清理分类帖子缓存 - 支持新旧两种命名格式
        // 旧格式: category_posts_{category_id}_*
        // 新格式: category_posts_v2_{category_id}_*
        if (strpos($filename, "category_posts_{$category_id}_") === 0 ||
            strpos($filename, "category_posts_v2_{$category_id}_") === 0) {
            @unlink($file);
        }
        // 清理分类统计缓存 - category_posts_count_{category_id}_*
        elseif (strpos($filename, "category_posts_count_{$category_id}_") === 0) {
            @unlink($file);
        }
        // 清理分类列表缓存 - category_list_pc_{category_id}_*
        elseif (strpos($filename, "category_list_pc_{$category_id}_") === 0) {
            @unlink($file);
        }
        // 清理分类信息缓存
        elseif ($filename === "category_info_{$category_id}") {
            @unlink($file);
        }
        // 清理分类字段缓存
        elseif ($filename === "category_fields_{$category_id}") {
            @unlink($file);
        }
        // 清理分类统计缓存
        elseif ($filename === "category_stats_{$category_id}") {
            @unlink($file);
        }
    }

    // 记录缓存清理日志
    error_log("[Cache] 分类相关缓存已清理 - category_id: {$category_id} - " . date('Y-m-d H:i:s'));
}

/**
 * 清理相关信息缓存
 * @param int $category_id 分类ID
 */
function clearRelatedPostsCache($category_id) {
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    $patterns = array(
        "related_posts_{$category_id}_",
        "similar_posts_{$category_id}_",
        "category_hot_posts_{$category_id}_"
    );

    foreach ($files as $file) {
        $filename = basename($file, '.cache');

        foreach ($patterns as $pattern) {
            if (strpos($filename, $pattern) === 0) {
                @unlink($file);
                break;
            }
        }
    }

    // 记录缓存清理日志
    error_log("[Cache] 相关信息缓存已清理 - category_id: {$category_id} - " . date('Y-m-d H:i:s'));
}

/**
 * 清理搜索结果缓存
 */
function clearSearchResultCache() {
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $files = glob($cacheDir . '*.cache');

    $patterns = array(
        'search_results_',
        'search_count_',
        'hot_keywords_'
    );

    foreach ($files as $file) {
        $filename = basename($file, '.cache');

        foreach ($patterns as $pattern) {
            if (strpos($filename, $pattern) === 0) {
                @unlink($file);
                break;
            }
        }
    }

    // 记录缓存清理日志
    error_log('[Cache] 搜索结果缓存已清理 - ' . date('Y-m-d H:i:s'));
}

/**
 * 精确清理信息相关缓存
 * @param int $post_id 信息ID
 * @param int $category_id 分类ID（可选）
 * @param string $operation 操作类型：create(新建), update(修改), delete(删除), refresh(刷新)
 */
function clearPostRelatedCaches($post_id, $category_id = null, $operation = 'update') {
    global $db;

    // 如果没有提供分类ID，从数据库获取
    if (!$category_id && $post_id) {
        $sql = "SELECT category_id FROM posts WHERE id = ?";
        $result = $db->query($sql, [$post_id]);
        if ($result && $row = $db->fetch_array($result)) {
            $category_id = $row['category_id'];
        }
    }

    // 1. 清理信息详情缓存（所有端）
    clearPostDetailCache($post_id);

    // 2. 清理当前分类和父分类的缓存
    if ($category_id) {
        // 获取分类信息，包括父分类
        $category_info = getCategoryInfo($category_id);

        // 清理当前分类的缓存
        clearCategoryRelatedCache($category_id);

        // 如果有父分类，也清理父分类的缓存
        if ($category_info && isset($category_info['parent_id']) && $category_info['parent_id'] > 0) {
            clearCategoryRelatedCache($category_info['parent_id']);
        }

        // 清理相关信息缓存
        clearRelatedPostsCache($category_id);
    }

    // 3. 根据操作类型决定是否清理首页缓存
    if ($operation === 'create' || $operation === 'refresh') {
        // 新建信息或刷新信息时清理首页缓存
        // 新建：首页显示最新信息
        // 刷新：更新时间会影响首页排序
        clearHomepageCache();
    }

    // 4. 不清理搜索缓存，因为搜索结果通常有自己的过期时间
    // 搜索缓存会自然过期，无需每次都清理

    // 记录缓存清理日志
    error_log("[Cache] 精确清理信息相关缓存完成 - post_id: {$post_id}, category_id: {$category_id}, operation: {$operation} - " . date('Y-m-d H:i:s'));
}

/**
 * 清理置顶相关缓存（智能检测变化）
 * @param int $post_id 信息ID
 * @param int $category_id 分类ID
 * @param array $old_top_status 原置顶状态 ['home' => bool, 'category' => bool, 'subcategory' => bool]
 * @param array $new_top_status 新置顶状态 ['home' => bool, 'category' => bool, 'subcategory' => bool]
 */
function clearTopRelatedCaches($post_id, $category_id, $old_top_status = null, $new_top_status = null) {
    $cleared_caches = array();

    // 如果没有提供旧状态，则按新状态清理（用于新建信息）
    if ($old_top_status === null) {
        $old_top_status = array('home' => false, 'category' => false, 'subcategory' => false);
    }

    // 如果没有提供新状态，则不清理任何缓存
    if ($new_top_status === null) {
        return;
    }

    // 检查首页置顶状态变化
    if ($old_top_status['home'] !== $new_top_status['home']) {
        clearHomepageCache();
        $status = $new_top_status['home'] ? '设置' : '取消';
        $cleared_caches[] = "首页缓存({$status}首页置顶)";
    }

    // 检查分类置顶状态变化
    if ($category_id && ($old_top_status['category'] !== $new_top_status['category'] || $old_top_status['subcategory'] !== $new_top_status['subcategory'])) {
        // 获取分类信息
        $category_info = getCategoryInfo($category_id);

        if ($category_info) {
            // 检查子分类置顶变化
            if ($old_top_status['subcategory'] !== $new_top_status['subcategory']) {
                if ($category_info['parent_id'] > 0) {
                    // 这是子分类，清理子分类的缓存
                    clearCategoryRelatedCache($category_id);
                    $status = $new_top_status['subcategory'] ? '设置' : '取消';
                    $cleared_caches[] = "子分类缓存({$status}子分类置顶, ID:{$category_id})";
                } else {
                    // 这是大分类，清理大分类缓存
                    clearCategoryRelatedCache($category_id);
                    $status = $new_top_status['subcategory'] ? '设置' : '取消';
                    $cleared_caches[] = "大分类缓存({$status}子分类置顶, ID:{$category_id})";
                }
            }

            // 检查分类置顶变化
            if ($old_top_status['category'] !== $new_top_status['category']) {
                if ($category_info['parent_id'] > 0) {
                    // 这是子分类，需要清理其父分类（大分类）的缓存
                    clearCategoryRelatedCache($category_info['parent_id']);
                    $status = $new_top_status['category'] ? '设置' : '取消';
                    $cleared_caches[] = "大分类缓存({$status}分类置顶, 父分类ID:{$category_info['parent_id']})";
                } else {
                    // 这本身就是大分类，清理大分类缓存
                    clearCategoryRelatedCache($category_id);
                    $status = $new_top_status['category'] ? '设置' : '取消';
                    $cleared_caches[] = "大分类缓存({$status}分类置顶, ID:{$category_id})";
                }
            }
        }
    }

    // 记录缓存清理日志
    if (!empty($cleared_caches)) {
        $cache_list = implode(', ', $cleared_caches);
        error_log("[Cache] 置顶缓存清理完成 - post_id: {$post_id}, 已清理: {$cache_list} - " . date('Y-m-d H:i:s'));
    }
}

/**
 * 简化版置顶缓存清理（向后兼容）
 * @param int $post_id 信息ID
 * @param int $category_id 分类ID
 * @param bool $is_top_home 是否首页置顶
 * @param bool $is_top_category 是否分类置顶
 * @param bool $is_top_subcategory 是否子分类置顶
 */
function clearTopRelatedCachesSimple($post_id, $category_id, $is_top_home = false, $is_top_category = false, $is_top_subcategory = false) {
    $new_status = array(
        'home' => $is_top_home,
        'category' => $is_top_category,
        'subcategory' => $is_top_subcategory
    );

    clearTopRelatedCaches($post_id, $category_id, null, $new_status);
}

/**
 * 兼容旧版本的全面缓存清理函数
 * @param int $post_id 信息ID
 * @param int $category_id 分类ID（可选）
 * @param bool $clear_homepage 是否清理首页缓存
 * @deprecated 建议使用 clearPostRelatedCaches() 替代
 */
function clearAllRelatedCaches($post_id, $category_id = null, $clear_homepage = true) {
    $operation = $clear_homepage ? 'create' : 'update';
    clearPostRelatedCaches($post_id, $category_id, $operation);
}

/**
 * 按模式清理缓存文件
 * @param string $pattern 缓存键名模式（支持通配符*）
 */
function clearCacheByPattern($pattern) {
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    if (!is_dir($cacheDir)) {
        return;
    }

    // 将模式转换为文件名模式
    $file_pattern = str_replace('*', '*', $pattern);
    $files = glob($cacheDir . $file_pattern . '.cache');

    $count = 0;
    foreach ($files as $file) {
        if (@unlink($file)) {
            $count++;
        }
    }

    // 记录缓存清理日志
    error_log("[Cache] 按模式清理缓存完成 - pattern: {$pattern}, 清理文件数: {$count} - " . date('Y-m-d H:i:s'));

    return $count;
}

/**
 * 清除所有缓存
 */
function clearAllCache() {
    $count = cache_clear();
    error_log('[Cache] 所有缓存已清理，共清理 ' . $count . ' 个文件 - ' . date('Y-m-d H:i:s'));
    return $count;
}

/**
 * 获取缓存统计信息
 */
function getCacheStats() {
    $info = $GLOBALS['file_cache']->getInfo();

    $stats = [
        'total_files' => $info['count'],
        'total_size' => $info['total_size'],
        'total_size_formatted' => $info['total_size_formatted'],
        'category_cache_exists' => cache_exists('categories_all'),
        'region_cache_exists' => cache_exists('regions_all'),
        'cache_types' => []
    ];

    // 分析缓存类型
    foreach ($info['files'] as $file) {
        $filename = $file['file'];
        if (strpos($filename, md5('categories_all')) === 0) {
            $stats['cache_types']['categories'] = [
                'size' => $file['size'],
                'mtime' => $file['mtime']
            ];
        } elseif (strpos($filename, md5('regions_all')) === 0) {
            $stats['cache_types']['regions'] = [
                'size' => $file['size'],
                'mtime' => $file['mtime']
            ];
        } elseif (strpos($filename, md5('category_posts_count_')) === 0) {
            if (!isset($stats['cache_types']['posts_count'])) {
                $stats['cache_types']['posts_count'] = 0;
            }
            $stats['cache_types']['posts_count']++;
        } elseif (strpos($filename, md5('category_posts_')) === 0) {
            if (!isset($stats['cache_types']['posts_data'])) {
                $stats['cache_types']['posts_data'] = 0;
            }
            $stats['cache_types']['posts_data']++;
        }
    }

    return $stats;
}
