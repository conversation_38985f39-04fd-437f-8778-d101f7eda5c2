<?php if (!defined('IN_BTMPS')) { exit('Access Denied'); } ?>
/* 表: about */
DROP TABLE IF EXISTS `about`;
CREATE TABLE `about` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '页面标题',
  `path` varchar(255) NOT NULL COMMENT '页面路径（相对于根目录）',
  `url` varchar(255) NOT NULL COMMENT '页面URL',
  `content` longtext COMMENT '页面内容',
  `meta_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `meta_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `path` (`path`),
  UNIQUE KEY `url` (`url`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单页表';

/* 表: admins */
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `realname` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=正常',
  `last_login` int(10) DEFAULT NULL COMMENT '最后登录时间',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

/* 表: area */
DROP TABLE IF EXISTS `area`;
CREATE TABLE `area` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '区域名称',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `level` tinyint(1) DEFAULT '1' COMMENT '级别: 1=省, 2=市, 3=区县',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区域表';

/* 表: attachments */
DROP TABLE IF EXISTS `attachments`;
CREATE TABLE `attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '信息ID',
  `type` varchar(10) NOT NULL DEFAULT 'post' COMMENT '附件类型：post=信息附件，news=新闻附件',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `thumb_path` varchar(255) DEFAULT NULL COMMENT '缩略图路径',
  `file_size` int(11) DEFAULT '0' COMMENT '文件大小(KB)',
  `file_type` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_post_type` (`post_id`,`type`) COMMENT '信息ID和类型联合索引',
  KEY `idx_type_post_sort` (`type`,`post_id`,`sort_order`),
  KEY `idx_created_time` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片附件表';

/* 表: categories */
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `seo_title` varchar(100) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(255) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` varchar(255) DEFAULT NULL COMMENT 'SEO描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=正常',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  `template` varchar(50) DEFAULT NULL COMMENT '栏目使用的模板',
  `detail_template` varchar(50) DEFAULT NULL COMMENT '详情页使用的模板',
  `subcategory_ids` text COMMENT '子分类ID列表，逗号分隔',
  `count` int(11) NOT NULL DEFAULT '0' COMMENT '分类下的信息数量',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `pinyin` (`pinyin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

/* 表: content_blocks */
DROP TABLE IF EXISTS `content_blocks`;
CREATE TABLE `content_blocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '块名称',
  `identifier` varchar(50) NOT NULL COMMENT '调用标识符',
  `content` longtext NOT NULL COMMENT 'HTML内容',
  `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容块表';

/* 表: fields */
DROP TABLE IF EXISTS `fields`;
CREATE TABLE `fields` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '所属分类ID',
  `name` varchar(50) NOT NULL COMMENT '字段名称',
  `field_key` varchar(50) NOT NULL COMMENT '字段键名',
  `type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '字段类型: text, textarea, radio, checkbox, select, number, date',
  `options` text COMMENT '选项值，用于单选、多选、下拉',
  `required` tinyint(1) DEFAULT '0' COMMENT '是否必填: 0=否, 1=是',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0=禁用, 1=正常',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自定义字段表';

/* 表: links */
DROP TABLE IF EXISTS `links`;
CREATE TABLE `links` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `name` varchar(100) NOT NULL COMMENT '链接名称',
  `url` varchar(255) NOT NULL COMMENT '链接地址',
  `description` text COMMENT '链接描述',
  `logo` varchar(255) DEFAULT NULL COMMENT 'LOGO地址',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序（数字越小越靠前）',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态（1=启用，0=禁用）',
  `target` varchar(10) DEFAULT '_blank' COMMENT '打开方式（_blank=新窗口，_self=当前窗口）',
  `created_at` int(11) NOT NULL COMMENT '创建时间',
  `updated_at` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`,`sort_order`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='友情链接表';

/* 表: mobile_daily_stats */
DROP TABLE IF EXISTS `mobile_daily_stats`;
CREATE TABLE `mobile_daily_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `date` date NOT NULL COMMENT '统计日期',
  `post_count` int(11) DEFAULT '0' COMMENT '当日发布数量',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  `updated_at` int(10) NOT NULL COMMENT '更新时间',
  `server_timestamp` int(10) DEFAULT NULL COMMENT '服务器时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_mobile_date` (`mobile`,`date`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_date` (`date`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_server_timestamp` (`server_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号每日发布统计表';

/* 表: mobile_post_records */
DROP TABLE IF EXISTS `mobile_post_records`;
CREATE TABLE `mobile_post_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `post_timestamp` int(10) NOT NULL COMMENT '发布时间戳（客户端时间）',
  `server_timestamp` int(10) NOT NULL COMMENT '服务器时间戳（真实时间）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_server_timestamp` (`server_timestamp`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_mobile_server_time` (`mobile`,`server_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号发布记录表（防时间篡改）';

/* 表: navigation */
DROP TABLE IF EXISTS `navigation`;
CREATE TABLE `navigation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `navname` varchar(50) NOT NULL COMMENT '导航名称',
  `url` varchar(255) NOT NULL COMMENT '链接地址',
  `target` varchar(20) DEFAULT '_self' COMMENT '打开方式',
  `navorder` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态: 0-禁用, 1-启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站导航菜单';

/* 表: news */
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `catid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '栏目ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `imgurl` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `author` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '作者',
  `click` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '点击数',
  `is_recommend` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐，1推荐，0不推荐',
  `is_top` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否置顶，1置顶，0不置顶',
  `is_show` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否显示，1显示，0隐藏',
  `addtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '文章摘要',
  `keywords` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关键词',
  `thumb` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '缩略图',
  `tags` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '标签，多个用逗号分隔',
  `view_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '阅读量（预留给新统计系统）',
  PRIMARY KEY (`id`),
  KEY `catid` (`catid`),
  KEY `idx_recommend_show_time` (`is_recommend`,`is_show`,`addtime`),
  KEY `idx_top_show_time` (`is_top`,`is_show`,`addtime`),
  KEY `idx_show_time` (`is_show`,`addtime`),
  KEY `idx_click` (`click`),
  KEY `idx_hot_articles` (`is_show`,`click`,`addtime`),
  KEY `idx_catid_show_time` (`catid`,`is_show`,`addtime`),
  KEY `idx_show_click_time` (`is_show`,`click`,`addtime`),
  KEY `idx_update_time_desc` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章主表';

/* 表: news_category */
DROP TABLE IF EXISTS `news_category`;
CREATE TABLE `news_category` (
  `catid` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '栏目ID',
  `catname` varchar(90) NOT NULL DEFAULT '' COMMENT '栏目名称',
  `parentid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父栏目ID',
  `sort_order` smallint(6) unsigned NOT NULL DEFAULT '50' COMMENT '排序',
  `is_show` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否显示，1显示，0隐藏',
  `addtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `pinyin` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`catid`),
  KEY `parent_id` (`parentid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文章栏目表';

/* 表: news_content */
DROP TABLE IF EXISTS `news_content`;
CREATE TABLE `news_content` (
  `id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文章ID',
  `content` text NOT NULL COMMENT '文章内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文章内容表';

/* 表: operation_logs */
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) DEFAULT '0' COMMENT '操作用户ID，0表示游客',
  `user_type` varchar(20) DEFAULT 'guest' COMMENT '用户类型：admin=管理员, user=普通用户, guest=游客',
  `username` varchar(50) DEFAULT '' COMMENT '操作用户名',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：create=创建, update=修改, delete=删除, login=登录, logout=登出',
  `target_type` varchar(30) NOT NULL COMMENT '操作对象类型：post=信息, news=新闻, category=分类, admin=管理员, user=用户等',
  `target_id` int(11) DEFAULT '0' COMMENT '操作对象ID',
  `target_title` varchar(200) DEFAULT '' COMMENT '操作对象标题或名称',
  `operation_desc` varchar(500) DEFAULT '' COMMENT '操作描述',
  `old_data` text COMMENT '修改前的数据（JSON格式）',
  `new_data` text COMMENT '修改后的数据（JSON格式）',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址（支持IPv6）',
  `port` int(11) DEFAULT '0' COMMENT '端口号',
  `user_agent` varchar(500) DEFAULT '' COMMENT '用户代理信息',
  `referer` varchar(500) DEFAULT '' COMMENT '来源页面',
  `request_method` varchar(10) DEFAULT 'GET' COMMENT '请求方法：GET, POST, PUT, DELETE等',
  `request_uri` varchar(500) DEFAULT '' COMMENT '请求URI',
  `session_id` varchar(100) DEFAULT '' COMMENT '会话ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '操作状态：1=成功, 0=失败',
  `error_message` varchar(500) DEFAULT '' COMMENT '错误信息（如果操作失败）',
  `execution_time` decimal(8,3) DEFAULT '0.000' COMMENT '执行时间（毫秒）',
  `created_at` int(10) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_target_type_id` (`target_type`,`target_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_operation_time` (`user_id`,`operation_type`,`created_at`),
  KEY `idx_target_operation_time` (`target_type`,`operation_type`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

/* 表: post_contents */
DROP TABLE IF EXISTS `post_contents`;
CREATE TABLE `post_contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `content` longtext,
  `fields_data` text,
  `ip` varchar(50) DEFAULT NULL COMMENT '发布IP',
  `password` varchar(50) DEFAULT NULL COMMENT '管理密码',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_mobile` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_weixin` varchar(50) DEFAULT NULL COMMENT '联系微信',
  `contact_address` varchar(255) DEFAULT NULL COMMENT '联系地址',
  PRIMARY KEY (`id`),
  UNIQUE KEY `post_id` (`post_id`),
  KEY `idx_content_search` (`content`(100)),
  KEY `idx_contact_mobile` (`contact_mobile`),
  FULLTEXT KEY `ft_content` (`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/* 表: post_count */
DROP TABLE IF EXISTS `post_count`;
CREATE TABLE `post_count` (
  `category_id` int(11) NOT NULL,
  `post_count` int(11) NOT NULL DEFAULT '0',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='栏目信息统计表';

/* 表: posts */
DROP TABLE IF EXISTS `posts`;
CREATE TABLE `posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT '0' COMMENT '用户ID，0表示游客',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `region_id` int(11) DEFAULT NULL COMMENT '区域ID',
  `expire_days` int(11) NOT NULL DEFAULT '30',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `is_top_home` tinyint(1) DEFAULT '0' COMMENT '是否首页置顶: 0=否, 1=是',
  `is_top_category` tinyint(1) DEFAULT '0' COMMENT '是否大分类置顶: 0=否, 1=是',
  `is_top_subcategory` tinyint(1) DEFAULT '0' COMMENT '是否小分类置顶: 0=否, 1=是',
  `top_home_expire` int(10) DEFAULT NULL COMMENT '首页置顶到期时间',
  `top_category_expire` int(10) DEFAULT NULL COMMENT '大分类置顶到期时间',
  `top_subcategory_expire` int(10) DEFAULT NULL COMMENT '小分类置顶到期时间',
  `view_count` int(11) NOT NULL DEFAULT '1' COMMENT '查看次数',
  `image_count` int(10) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态: 0=待审核, 1=正常, 2=下架',
  `created_at` int(10) DEFAULT NULL COMMENT '创建时间',
  `updated_at` int(10) DEFAULT NULL COMMENT '更新时间',
  `expired_at` int(10) DEFAULT NULL COMMENT '过期时间',
  `is_expired` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已过期',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `region_id` (`region_id`),
  KEY `user_id` (`user_id`),
  KEY `is_top_home` (`is_top_home`),
  KEY `is_top_category` (`is_top_category`),
  KEY `is_top_subcategory` (`is_top_subcategory`),
  KEY `idx_category_status_updated` (`category_id`,`status`,`updated_at`,`id`),
  KEY `idx_region_category_status` (`region_id`,`category_id`,`status`),
  KEY `idx_top_expired` (`is_top_category`,`top_category_expire`,`status`),
  KEY `idx_title_status` (`title`,`status`),
  KEY `idx_status_updated` (`status`,`updated_at`,`id`),
  KEY `idx_category_status` (`category_id`,`status`),
  KEY `idx_status_updated_id` (`status`,`updated_at`,`id`),
  KEY `idx_status_updated_desc` (`status`,`updated_at`,`id`),
  KEY `idx_category_status_time` (`category_id`,`status`,`updated_at`),
  KEY `idx_region_status_time` (`region_id`,`status`,`updated_at`),
  KEY `idx_home_top_status_time` (`is_top_home`,`status`,`updated_at`),
  KEY `idx_expired_status_time` (`is_expired`,`status`,`expired_at`),
  FULLTEXT KEY `ft_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息表';

/* 表: reports */
DROP TABLE IF EXISTS `reports`;
CREATE TABLE `reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `type` varchar(11) NOT NULL,
  `content` text NOT NULL,
  `tel` varchar(255) DEFAULT NULL,
  `ip` varchar(50) NOT NULL,
  `created_at` int(10) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/* 表: settings */
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `setting_group` varchar(30) DEFAULT 'general' COMMENT '设置分组',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';

