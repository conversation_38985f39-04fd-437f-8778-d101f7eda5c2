<?php
/**
 * 新闻文章管理
 */

// 定义入口常量
define('IN_BTMPS', true);

require_once dirname(__FILE__) . '/../include/common.inc.php';

// 引入后台通用函数
require_once dirname(__FILE__) . '/include/admin.fun.php';

// 获取数据库表前缀
$db_prefix = isset($config['db_prefix']) ? $config['db_prefix'] : '';  

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 检查新闻表是否包含click字段，如果不存在则添加
$sql = "SHOW COLUMNS FROM `{$db_prefix}news` LIKE 'click'";
$result = $db->query($sql);
if ($db->num_rows($result) == 0) {
    $sql = "ALTER TABLE `{$db_prefix}news` ADD `click` int(11) NOT NULL DEFAULT '0'";
    $db->query($sql);
}

// 操作类型
$op = isset($_REQUEST['op']) ? trim($_REQUEST['op']) : 'list';

// 使用系统自带的Template类而不是Smarty
// $tpl变量已在common.inc.php中初始化
// 设置当前模板目录为hao/template
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 当前页面
$current_page = 'news';

// 设置模板目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 消息提示
$message = '';
$error = '';

// 获取URL中的消息参数
if (isset($_GET['message'])) {
    $message = $_GET['message'];
}
if (isset($_GET['error'])) {
    $error = $_GET['error'];
}

// 设置页面标题和面包屑导航
$page_title = '新闻文章管理';
$breadcrumb = '新闻文章管理';

// 设置公共变量
$tpl->assign('current_page', $current_page);
$tpl->assign('page_title', $page_title);
$tpl->assign('breadcrumb', $breadcrumb);
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作类型处理
switch ($op) {
    case 'list': // 文章列表
        // 分页参数
        $page = isset($_REQUEST['page']) ? intval($_REQUEST['page']) : 1;
        $perpage = 20; // 每页显示数量
        $start = ($page - 1) * $perpage;
        
        // 查询条件
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        $title = isset($_REQUEST['title']) ? trim($_REQUEST['title']) : '';
        $id = isset($_REQUEST['id']) ? intval($_REQUEST['id']) : 0;
        
        $where = "1=1";
        $query_string = [];
        
        if ($catid > 0) {
            $where .= " AND n.catid = $catid";
            $query_string[] = "catid=$catid";
        }
        
        if (!empty($title)) {
            $where .= " AND n.title LIKE '%" . $db->escape($title) . "%'";
            $query_string[] = "title=" . urlencode($title);
        }
        
        if ($id > 0) {
            $where .= " AND n.id = $id";
            $query_string[] = "id=$id";
        }
        
        // 使用SQL_CALC_FOUND_ROWS优化分页查询
        $sql = "SELECT SQL_CALC_FOUND_ROWS n.id, n.title, n.catid, n.addtime, n.click, n.is_top, n.is_recommend, n.is_show, c.catname FROM `news` n 
                LEFT JOIN `news_category` c ON n.catid = c.catid
                WHERE $where ORDER BY n.is_top DESC, n.is_recommend DESC, n.id DESC LIMIT $start, $perpage";
        $result = $db->query($sql);
        $news_list = [];
        while ($row = $db->fetch_array($result)) {
            $news_list[] = $row;
        }
        
        // 获取总记录数，不需要额外COUNT查询
        $count_result = $db->query("SELECT FOUND_ROWS() AS total");
        $count_data = $db->fetch_array($count_result);
        $total = $count_data['total'];
        
        // 获取所有栏目
        $sql = "SELECT * FROM `news_category` ORDER BY `sort_order` ASC, `catid` ASC";
        $result = $db->query($sql);
        $categories = [];
        while ($row = $db->fetch_array($result)) {
            $categories[] = $row;
        }
        
        // 构建栏目树
        $cat_tree = [];
        foreach ($categories as $cat) {
            if ($cat['parentid'] == 0) {
                $cat_tree[$cat['catid']] = $cat;
                $cat_tree[$cat['catid']]['children'] = [];
            }
        }
        
        foreach ($categories as $cat) {
            if ($cat['parentid'] > 0 && isset($cat_tree[$cat['parentid']])) {
                $cat_tree[$cat['parentid']]['children'][] = $cat;
            }
        }
        
        // URL参数
        $page_url = 'news.php?' . implode('&', $query_string);
        if (!empty($query_string)) {
            $page_url .= '&';
        }
        
        // 使用通用分页函数生成分页数据
        $params = [];
        if ($catid > 0) {
            $params['catid'] = $catid;
        }
        if (!empty($title)) {
            $params['title'] = $title;
        }
        if ($id > 0) {
            $params['id'] = $id;
        }
        
        // 生成分页数据
        $pagination = generate_pagination($total, $page, $perpage, 'news.php', $params);
        
        // 生成分页HTML代码
        $pagination_html = pagination_html($pagination, true); // 使用简单分页样式
        
        // 设置模板变量
        $tpl->assign('news_list', $news_list);
        $tpl->assign('categories', $cat_tree);
        $tpl->assign('page', $pagination['page']);
        $tpl->assign('total_page', $pagination['total_page']);
        $tpl->assign('total', $pagination['total']);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('pagination_html', $pagination_html); // 添加分页HTML变量
        $tpl->assign('page_url', $page_url);
        $tpl->assign('catid', $catid);
        $tpl->assign('title', $title);
        $tpl->assign('id', $id);
        $tpl->assign('current_page', $current_page);
        $tpl->assign('breadcrumb', $breadcrumb);
        $tpl->assign('perpage', $perpage);
        
        // 显示模板
        $tpl->display('news_list.htm');
        break;
        
    case 'add': // 添加文章
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $catid = isset($_POST['catid']) ? intval($_POST['catid']) : 0;
            $title = isset($_POST['title']) ? trim($_POST['title']) : '';
            $author = isset($_POST['author']) ? trim($_POST['author']) : '';
            $content = isset($_POST['content']) ? $_POST['content'] : ''; // 直接使用原始内容，不进行trim
            $is_recommend = isset($_POST['is_recommend']) ? intval($_POST['is_recommend']) : 0;
            $is_top = isset($_POST['is_top']) ? intval($_POST['is_top']) : 0;
            $is_show = isset($_POST['is_show']) ? intval($_POST['is_show']) : 1;
            
            // 自动提取摘要和关键词
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            if (empty($description) && !empty($content)) {
                // 如果没有提供摘要，则从内容中提取前200个字符作为摘要
                $description = mb_substr(strip_tags($content), 0, 200, 'UTF-8');
            }
            
            $keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
            $thumb = isset($_POST['thumb']) ? trim($_POST['thumb']) : '';
            
            // 保存已输入数据，防止验证失败后数据丢失
            $post_data = [
                'catid' => $catid,
                'title' => $title,
                'author' => $author,
                'content' => $content,
                'is_recommend' => $is_recommend,
                'is_top' => $is_top,
                'is_show' => $is_show,
                'description' => $description,
                'keywords' => $keywords,
                'thumb' => $thumb
            ];
            
            // 验证必填项
            if ($catid == 0) {
                $error = '请选择文章栏目';
            } elseif (empty($title)) {
                $error = '请输入文章标题';
            } elseif (empty($content)) {
                $error = '请输入文章内容';
            } else {
                // 检查选择的栏目信息
                $sql = "SELECT catid, catname, parentid,
                        (SELECT COUNT(*) FROM `{$db_prefix}news_category` WHERE parentid = c.catid) as has_children
                        FROM `{$db_prefix}news_category` c WHERE catid = ?";
                $result = $db->query($sql, [$catid]);
                $cat_info = $db->fetch_array($result);

                if (!$cat_info) {
                    $error = '选择的栏目不存在';
                } elseif ($cat_info['parentid'] == 0 && $cat_info['has_children'] > 0) {
                    // 如果选择的是有子栏目的一级栏目，则要求选择二级栏目
                    $error = '该栏目有子栏目，请选择具体的二级栏目发布文章';
                } else {
                    // 使用事务保证数据一致性
                    $db->beginTransaction();
                    
                    try {
                        // 添加文章
                        $now = time();
                        $sql = "INSERT INTO `news` 
                                (`catid`, `title`, `author`, `is_recommend`, `is_top`, `is_show`, 
                                `description`, `keywords`, `thumb`, `addtime`, `update_time`) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        $params = [
                            $catid, $title, $author, $is_recommend, $is_top, $is_show,
                            $description, $keywords, $thumb, $now, $now
                        ];
                        
                        $result = $db->query($sql, $params);
                        
                        if ($result) {
                            $news_id = $db->insert_id();
                            
                            // 添加文章内容
                            $sql = "INSERT INTO `news_content` (`id`, `content`) VALUES (?, ?)";
                            $result = $db->query($sql, [$news_id, $content]);
                            
                            if ($result) {
                                $db->commit();

                                // 清理新闻相关缓存
                                news_cache_clear_home();
                                news_cache_clear_list();
                                $GLOBALS['news_cache']->clearHotAndRecommendCache();
                                $GLOBALS['news_cache']->clearCategoryCache();

                                header("Location: news.php?message=" . urlencode('添加文章成功！'));
                                exit;
                            } else {
                                $db->rollback();
                                $error = '添加文章内容失败: ' . $db->error();
                            }
                        } else {
                            $db->rollback();
                            $error = '添加文章失败: ' . $db->error();
                        }
                    } catch (Exception $e) {
                        $db->rollback();
                        $error = '操作异常: ' . $e->getMessage();
                    }
                }
            }
        } else {
            // 初始化$post_data，避免模板中出现未定义变量错误
            $post_data = [
                'catid' => 0,
                'title' => '',
                'author' => '',
                'content' => '',
                'is_recommend' => 0,
                'is_top' => 0,
                'is_show' => 1,
                'description' => '',
                'keywords' => '',
                'thumb' => ''
            ];
        }
        
        // 获取栏目列表 - 根据是否有子栏目来决定显示一级还是二级栏目
        $sql = "SELECT c.catid, c.catname, c.parentid, 
                (SELECT COUNT(*) FROM `{$db_prefix}news_category` WHERE parentid = c.catid) as has_children 
                FROM `{$db_prefix}news_category` c 
                WHERE c.is_show = 1 
                ORDER BY c.sort_order ASC, c.catid ASC";
        $result = $db->query($sql);
        $cat_options = [];
        
        // 先获取所有栏目
        $all_categories = [];
        while ($row = $db->fetch_array($result)) {
            $all_categories[$row['catid']] = $row;
        }
        
        // 遍历所有栏目，构建选项列表
        foreach ($all_categories as $category) {
            if ($category['parentid'] == 0) {
                // 一级栏目
                if ($category['has_children'] == 0) {
                    // 没有子栏目的一级栏目可以直接选择
                    $cat_options[] = [
                        'catid' => $category['catid'],
                        'catname' => $category['catname'],
                        'is_parent' => true,
                        'level' => 1
                    ];
                } else {
                    // 有子栏目的一级栏目作为分组标题显示，但不可选
                    $cat_options[] = [
                        'catid' => 'parent_' . $category['catid'],
                        'catname' => $category['catname'],
                        'is_parent' => true,
                        'disabled' => true,
                        'level' => 1
                    ];
                    
                    // 添加其子栏目
                    $sub_categories = [];
                    foreach ($all_categories as $sub) {
                        if ($sub['parentid'] == $category['catid']) {
                            $sub_categories[] = $sub;
                        }
                    }
                    
                    // 添加子栏目
                    foreach ($sub_categories as $index => $sub) {
                        $is_last = ($index == count($sub_categories) - 1);
                        $cat_options[] = [
                            'catid' => $sub['catid'],
                            'catname' => $sub['catname'],
                            'is_parent' => false,
                            'level' => 2,
                            'is_last' => $is_last
                        ];
                    }
                }
            }
        }
        
        $tpl->assign('post_data', $post_data);
        $tpl->assign('cat_options', $cat_options);
        $tpl->assign('error', isset($error) ? $error : '');
        $tpl->display('news_add.htm');
        break;
        
    case 'edit': // 编辑文章
        $id = isset($_REQUEST['id']) ? intval($_REQUEST['id']) : 0;
        if ($id <= 0) {
            show_error('文章ID不正确');
        }
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $catid = isset($_POST['catid']) ? intval($_POST['catid']) : 0;
            $title = isset($_POST['title']) ? trim($_POST['title']) : '';
            $author = isset($_POST['author']) ? trim($_POST['author']) : '';
            $content = isset($_POST['content']) ? $_POST['content'] : ''; // 直接使用原始内容，不进行trim
            // 对内容进行HTML实体解码，防止重复转义
            $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            
            $is_recommend = isset($_POST['is_recommend']) ? intval($_POST['is_recommend']) : 0;
            $is_top = isset($_POST['is_top']) ? intval($_POST['is_top']) : 0;
            $is_show = isset($_POST['is_show']) ? intval($_POST['is_show']) : 1;
            
            // 获取其他字段
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            if (empty($description) && !empty($content)) {
                // 如果没有提供摘要，则从内容中提取前200个字符作为摘要
                $description = mb_substr(strip_tags($content), 0, 200, 'UTF-8');
            }
            
            // 如果提交的摘要与数据库中的相同，则重新从内容中提取摘要
            if (!empty($content)) {
                // 查询原数据以比较摘要是否有变化
                $sql = "SELECT description FROM `news` WHERE id = ?";
                $result = $db->query($sql, [$id]);
                $old_data = $db->fetch_array($result);
                
                // 如果提交的摘要与原来的相同，说明用户没有手动修改，应重新提取
                if ($description == $old_data['description']) {
                    $description = mb_substr(strip_tags($content), 0, 200, 'UTF-8');
                }
            }
            
            $keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
            $thumb = isset($_POST['thumb']) ? trim($_POST['thumb']) : '';
            
            // 验证必填项
            if ($catid == 0) {
                $error = '请选择文章栏目';
            } elseif (empty($title)) {
                $error = '请输入文章标题';
            } elseif (empty($content)) {
                $error = '请输入文章内容';
            } else {
                // 检查选择的栏目信息
                $sql = "SELECT catid, catname, parentid,
                        (SELECT COUNT(*) FROM `{$db_prefix}news_category` WHERE parentid = c.catid) as has_children
                        FROM `{$db_prefix}news_category` c WHERE catid = ?";
                $result = $db->query($sql, [$catid]);
                $cat_info = $db->fetch_array($result);

                if (!$cat_info) {
                    $error = '选择的栏目不存在';
                } elseif ($cat_info['parentid'] == 0 && $cat_info['has_children'] > 0) {
                    // 如果选择的是有子栏目的一级栏目，则要求选择二级栏目
                    $error = '该栏目有子栏目，请选择具体的二级栏目发布文章';
                } else {
                    // 使用事务保证数据一致性
                    $db->beginTransaction();
                    
                    try {
                        // 更新文章
                        $now = time();
                        $sql = "UPDATE `news` SET 
                                `catid` = ?, 
                                `title` = ?, 
                                `author` = ?, 
                                `is_recommend` = ?, 
                                `is_top` = ?, 
                                `is_show` = ?, 
                                `description` = ?,
                                `keywords` = ?,
                                `thumb` = ?,
                                `update_time` = ? 
                                WHERE `id` = ?";
                        $params = [
                            $catid, $title, $author, $is_recommend, $is_top, $is_show,
                            $description, $keywords, $thumb, $now, $id
                        ];
                        
                        $result = $db->query($sql, $params);
                        
                        if ($result) {
                            // 检查内容表中是否已有记录
                            $check_sql = "SELECT id FROM `news_content` WHERE id = ?";
                            $check_result = $db->query($check_sql, [$id]);
                            
                            if ($db->num_rows($check_result) > 0) {
                                // 更新文章内容
                                $sql = "UPDATE `news_content` SET `content` = ? WHERE `id` = ?";
                                $result = $db->query($sql, [$content, $id]);
                            } else {
                                // 插入文章内容
                                $sql = "INSERT INTO `news_content` (`id`, `content`) VALUES (?, ?)";
                                $result = $db->query($sql, [$id, $content]);
                            }
                            
                            if ($result) {
                                $db->commit();
                                
                                // 清理新闻相关缓存
                                news_cache_clear_home();
                                news_cache_clear_list();
                                news_cache_clear_detail($id); // 清理特定新闻的详情缓存
                                $GLOBALS['news_cache']->clearHotAndRecommendCache();
                                $GLOBALS['news_cache']->clearCategoryCache();
                                
                                $tpl->assign('edit_success', true);
                                $tpl->assign('news_id', $id);
                                $tpl->assign('news_title', $title);
                                $tpl->assign('news_url', '/news/' . $id . '.html');
                                $tpl->assign('news_edit_url', 'news.php?op=edit&id=' . $id);
                                $tpl->assign('news_list_url', 'news.php');
                                $tpl->display('news_edit.htm');
                                exit;
                            } else {
                                $db->rollback();
                                $error = '更新文章内容失败: ' . $db->error();
                            }
                        } else {
                            $db->rollback();
                            $error = '更新文章失败: ' . $db->error();
                        }
                    } catch (Exception $e) {
                        $db->rollback();
                        $error = '操作异常: ' . $e->getMessage();
                    }
                }
            }
        }
        
        // 查询文章信息 - 分开查询主表和内容表以提高性能
        $sql = "SELECT n.* FROM `news` n WHERE n.id = ?";
        $result = $db->query($sql, [$id]);
        $news = $db->fetch_array($result);
        if (!$news) {
            show_error('文章不存在');
        }
        
        // 查询文章内容
        $sql = "SELECT content FROM `news_content` WHERE id = ?";
        $result = $db->query($sql, [$id]);
        $content_data = $db->fetch_array($result);
        if ($content_data) {
            $news['content'] = $content_data['content'];
            // 对显示的内容进行HTML实体解码
            $news['content'] = html_entity_decode($news['content'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
        } else {
            $news['content'] = '';
        }
        
        // 获取栏目列表 - 根据是否有子栏目来决定显示一级还是二级栏目
        $sql = "SELECT c.catid, c.catname, c.parentid, 
                (SELECT COUNT(*) FROM `{$db_prefix}news_category` WHERE parentid = c.catid) as has_children 
                FROM `{$db_prefix}news_category` c 
                WHERE c.is_show = 1 
                ORDER BY c.sort_order ASC, c.catid ASC";
        $result = $db->query($sql);
                
        // 先获取所有栏目
        $all_categories = [];
        while ($row = $db->fetch_array($result)) {
            $all_categories[$row['catid']] = $row;
        }
        
        // 遍历所有栏目，构建选项列表
        $cat_options = [];
        foreach ($all_categories as $category) {
            if ($category['parentid'] == 0) {
                // 一级栏目
                if ($category['has_children'] == 0) {
                    // 没有子栏目的一级栏目可以直接选择
                    $cat_options[] = [
                        'catid' => $category['catid'],
                        'catname' => $category['catname'],
                        'is_parent' => true,
                        'level' => 1
                    ];
                } else {
                    // 有子栏目的一级栏目作为分组标题显示，但不可选
                    $cat_options[] = [
                        'catid' => 'parent_' . $category['catid'],
                        'catname' => $category['catname'],
                        'is_parent' => true,
                        'disabled' => true,
                        'level' => 1
                    ];
                    
                    // 添加其子栏目
                    $sub_categories = [];
                    foreach ($all_categories as $sub) {
                        if ($sub['parentid'] == $category['catid']) {
                            $sub_categories[] = $sub;
                        }
                    }
                    
                    // 添加子栏目
                    foreach ($sub_categories as $index => $sub) {
                        $is_last = ($index == count($sub_categories) - 1);
                        $cat_options[] = [
                            'catid' => $sub['catid'],
                            'catname' => $sub['catname'],
                            'is_parent' => false,
                            'level' => 2,
                            'is_last' => $is_last
                        ];
                    }
                }
            }
        }
        
        $tpl->assign('news', $news);
        $tpl->assign('cat_options', $cat_options);
        $tpl->assign('error', isset($error) ? $error : '');
        $tpl->display('news_edit.htm');
        break;
        
    case 'delete': // 删除文章
        $id = isset($_REQUEST['id']) ? intval($_REQUEST['id']) : 0;
        if ($id <= 0) {
            show_error('文章ID不正确');
        }
        
        // 使用事务保证数据一致性
        $db->beginTransaction();
        
        try {
            // 删除文章
            $sql = "DELETE FROM `news` WHERE `id` = ?";
            $result = $db->query($sql, [$id]);
            
            if ($result) {
                // 删除文章内容
                $sql = "DELETE FROM `news_content` WHERE `id` = ?";
                $result = $db->query($sql, [$id]);
                
                if ($result) {
                    $db->commit();
                    
                    // 清除可能的缓存
                    $cache_key = 'news_' . $id;
                    clear_cache($cache_key);
                    
                    header("Location: news.php?message=" . urlencode('删除文章成功！'));
                    exit;
                } else {
                    $db->rollback();
                    show_message('删除文章内容失败！', 'news.php');
                }
            } else {
                $db->rollback();
                show_message('删除文章失败！', 'news.php');
            }
        } catch (Exception $e) {
            $db->rollback();
            show_message('操作异常: ' . $e->getMessage(), 'news.php');
        }
        break;
        
    case 'batch_delete': // 批量删除文章
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            show_error('请求方式不正确');
        }
        
        $ids = isset($_POST['ids']) ? $_POST['ids'] : [];
        if (empty($ids) || !is_array($ids)) {
            show_message('请选择要删除的文章！', 'news.php');
        }
        
        // 使用事务保证数据一致性
        $db->beginTransaction();
        $deleted_count = 0;
        
        try {
            foreach ($ids as $id) {
                $id = intval($id);
                if ($id <= 0) {
                    continue;
                }
                
                // 删除文章
                $sql = "DELETE FROM `news` WHERE `id` = ?";
                $result = $db->query($sql, [$id]);
                
                if ($result) {
                    // 删除文章内容
                    $sql = "DELETE FROM `news_content` WHERE `id` = ?";
                    $result = $db->query($sql, [$id]);
                    
                    if ($result) {
                        // 清除可能的缓存
                        $cache_key = 'news_' . $id;
                        clear_cache($cache_key);
                        $deleted_count++;
                    }
                }
            }
            
            if ($deleted_count > 0) {
                $db->commit();
                header("Location: news.php?message=" . urlencode("成功删除 {$deleted_count} 篇文章！"));
                exit;
            } else {
                $db->rollback();
                show_message('没有删除任何文章！', 'news.php');
            }
        } catch (Exception $e) {
            $db->rollback();
            show_message('操作异常: ' . $e->getMessage(), 'news.php');
        }
        break;

    // 快捷设置推荐状态
    case 'toggle_recommend':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            show_message('参数错误！', 'news.php');
        }

        // 获取当前状态
        $sql = "SELECT is_recommend FROM `news` WHERE id = ?";
        $result = $db->query($sql, [$id]);
        $news = $db->fetch_array($result);

        if (!$news) {
            show_message('文章不存在！', 'news.php');
        }

        // 切换推荐状态
        $new_status = $news['is_recommend'] ? 0 : 1;
        $sql = "UPDATE `news` SET is_recommend = ? WHERE id = ?";
        $result = $db->query($sql, [$new_status, $id]);

        if ($result) {
            $status_text = $new_status ? '推荐' : '取消推荐';
            header("Location: news.php?message=" . urlencode("文章{$status_text}成功！"));
        } else {
            show_message('操作失败！', 'news.php');
        }
        exit;
        break;

    // 快捷设置置顶状态
    case 'toggle_top':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if ($id <= 0) {
            show_message('参数错误！', 'news.php');
        }

        // 获取当前状态
        $sql = "SELECT is_top FROM `news` WHERE id = ?";
        $result = $db->query($sql, [$id]);
        $news = $db->fetch_array($result);

        if (!$news) {
            show_message('文章不存在！', 'news.php');
        }

        // 切换置顶状态
        $new_status = $news['is_top'] ? 0 : 1;
        $sql = "UPDATE `news` SET is_top = ? WHERE id = ?";
        $result = $db->query($sql, [$new_status, $id]);

        if ($result) {
            $status_text = $new_status ? '置顶' : '取消置顶';
            header("Location: news.php?message=" . urlencode("文章{$status_text}成功！"));
        } else {
            show_message('操作失败！', 'news.php');
        }
        exit;
        break;

    // 批量推荐操作
    case 'batch_recommend':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_recommend = 1 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功推荐 {$affected} 篇文章！"));
                } else {
                    show_message('批量推荐失败！', 'news.php');
                }
            } else {
                show_message('请选择要推荐的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 批量取消推荐操作
    case 'batch_unrecommend':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_recommend = 0 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功取消推荐 {$affected} 篇文章！"));
                } else {
                    show_message('批量取消推荐失败！', 'news.php');
                }
            } else {
                show_message('请选择要取消推荐的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 批量置顶操作
    case 'batch_top':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_top = 1 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功置顶 {$affected} 篇文章！"));
                } else {
                    show_message('批量置顶失败！', 'news.php');
                }
            } else {
                show_message('请选择要置顶的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 批量取消置顶操作
    case 'batch_untop':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_top = 0 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功取消置顶 {$affected} 篇文章！"));
                } else {
                    show_message('批量取消置顶失败！', 'news.php');
                }
            } else {
                show_message('请选择要取消置顶的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 批量显示操作
    case 'batch_show':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_show = 1 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功显示 {$affected} 篇文章！"));
                } else {
                    show_message('批量显示失败！', 'news.php');
                }
            } else {
                show_message('请选择要显示的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 批量隐藏操作
    case 'batch_hide':
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
            $ids = array_map('intval', $_POST['ids']);
            $ids = array_filter($ids, function($id) { return $id > 0; });

            if (!empty($ids)) {
                $ids_str = implode(',', $ids);
                $sql = "UPDATE `news` SET is_show = 0 WHERE id IN ({$ids_str})";
                $result = $db->query($sql);

                if ($result) {
                    $affected = $db->affectedRows();
                    header("Location: news.php?message=" . urlencode("成功隐藏 {$affected} 篇文章！"));
                } else {
                    show_message('批量隐藏失败！', 'news.php');
                }
            } else {
                show_message('请选择要隐藏的文章！', 'news.php');
            }
        } else {
            show_message('参数错误！', 'news.php');
        }
        exit;
        break;

    // 全部取消置顶与推荐
    case 'reset_all_status':
        $sql = "UPDATE `news` SET is_top = 0, is_recommend = 0";
        $result = $db->query($sql);

        if ($result) {
            $affected = $db->affectedRows();
            header("Location: news.php?message=" . urlencode("成功重置所有文章状态，共影响 {$affected} 篇文章！"));
        } else {
            show_message('重置失败！', 'news.php');
        }
        exit;
        break;

    default: // 文章列表
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        $perpage = 15; // 每页显示数量
        $start = ($page - 1) * $perpage;
        
        // 查询条件
        $catid = isset($_REQUEST['catid']) ? intval($_REQUEST['catid']) : 0;
        $title = isset($_REQUEST['title']) ? trim($_REQUEST['title']) : '';
        $id = isset($_REQUEST['id']) ? intval($_REQUEST['id']) : 0;
        $is_top = isset($_REQUEST['is_top']) ? intval($_REQUEST['is_top']) : -1;
        $is_recommend = isset($_REQUEST['is_recommend']) ? intval($_REQUEST['is_recommend']) : -1;
        $is_show = isset($_REQUEST['is_show']) ? intval($_REQUEST['is_show']) : -1;

        $where = "1=1";
        $query_string = [];

        if ($catid > 0) {
            $where .= " AND n.catid = $catid";
            $query_string[] = "catid=$catid";
        }

        if (!empty($title)) {
            $where .= " AND n.title LIKE '%" . $db->escape($title) . "%'";
            $query_string[] = "title=" . urlencode($title);
        }

        if ($id > 0) {
            $where .= " AND n.id = $id";
            $query_string[] = "id=$id";
        }

        // 推荐筛选
        if ($is_recommend >= 0) {
            $where .= " AND n.is_recommend = $is_recommend";
            $query_string[] = "is_recommend=$is_recommend";
        }

        // 置顶筛选
        if ($is_top >= 0) {
            $where .= " AND n.is_top = $is_top";
            $query_string[] = "is_top=$is_top";
        }

        // 显示状态筛选
        if ($is_show >= 0) {
            $where .= " AND n.is_show = $is_show";
            $query_string[] = "is_show=$is_show";
        } else {
            // 默认只显示已发布的文章（is_show = 1）
            $where .= " AND n.is_show = 1";
        }
        
        // 使用SQL_CALC_FOUND_ROWS优化分页查询
        $sql = "SELECT SQL_CALC_FOUND_ROWS n.id, n.title, n.catid, n.addtime, n.click, n.is_top, n.is_recommend, n.is_show, c.catname FROM `news` n 
                LEFT JOIN `news_category` c ON n.catid = c.catid 
                WHERE $where
                ORDER BY n.addtime DESC, n.id DESC
                LIMIT {$start}, {$perpage}";
        $result = $db->query($sql);
        $news_list = [];
        while ($row = $db->fetch_array($result)) {
            $news_list[] = $row;
        }
        
        // 获取总记录数，不需要额外COUNT查询
        $count_result = $db->query("SELECT FOUND_ROWS() AS total");
        $count_data = $db->fetch_array($count_result);
        $total = $count_data['total'];
        
        // 获取所有栏目
        $sql = "SELECT * FROM `news_category` ORDER BY `sort_order` ASC, `catid` ASC";
        $result = $db->query($sql);
        $categories = [];
        while ($row = $db->fetch_array($result)) {
            $categories[] = $row;
        }
        
        // 构建栏目树
        $cat_tree = [];
        foreach ($categories as $cat) {
            if ($cat['parentid'] == 0) {
                $cat_tree[$cat['catid']] = $cat;
                $cat_tree[$cat['catid']]['children'] = [];
            }
        }
        
        foreach ($categories as $cat) {
            if ($cat['parentid'] > 0 && isset($cat_tree[$cat['parentid']])) {
                $cat_tree[$cat['parentid']]['children'][] = $cat;
            }
        }
        
        // URL参数
        $page_url = 'news.php?';
        if (!empty($query_string)) {
            $page_url .= implode('&', $query_string) . '&';
        }
        
        // 生成分页数据
        $pagination_params = [
            'catid' => $catid,
            'title' => $title,
            'id' => $id
        ];

        // 添加筛选参数到分页
        if ($is_recommend >= 0) {
            $pagination_params['is_recommend'] = $is_recommend;
        }
        if ($is_top >= 0) {
            $pagination_params['is_top'] = $is_top;
        }
        if ($is_show >= 0) {
            $pagination_params['is_show'] = $is_show;
        }

        $pagination = generate_pagination($total, $page, $perpage, 'news.php', $pagination_params);
        
        // 生成分页HTML代码
        $pagination_html = pagination_html($pagination, true);
        
        $tpl->assign('news_list', $news_list);
        $tpl->assign('categories', $cat_tree);
        $tpl->assign('page', $pagination['page']);
        $tpl->assign('total_page', $pagination['total_page']);
        $tpl->assign('total', $pagination['total']);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('pagination_html', $pagination_html);
        $tpl->assign('page_url', $page_url);
        $tpl->assign('catid', $catid);
        $tpl->assign('title', $title);
        $tpl->assign('id', $id);
        $tpl->assign('is_recommend', $is_recommend);
        $tpl->assign('is_top', $is_top);
        $tpl->assign('is_show', $is_show);
        $tpl->assign('perpage', $perpage);
        $tpl->display('news_list.htm');
        break;
}

/**
 * 清除缓存函数
 */
function clear_cache($key) {
    // 实现缓存清理逻辑，可以是文件缓存或其他缓存机制
    // 这里仅作为示例
    return true;
}