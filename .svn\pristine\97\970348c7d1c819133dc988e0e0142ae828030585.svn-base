<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
    <title><?php echo $seo_title ?? ""; ?></title>
    <meta name="keywords" content="<?php if($category['seo_keywords']): ?><?php echo (isset($category['seo_keywords'])) ? $category['seo_keywords'] : ""; ?><?php else: ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?><?php endif; ?>">
    <meta name="description" content="<?php if($category['seo_description']): ?><?php echo (isset($category['seo_description'])) ? $category['seo_description'] : ""; ?><?php else: ?><?php echo (isset($category['description'])) ? $category['description'] : ""; ?><?php endif; ?>">
		<link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/category.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/filter.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
	</head>
	<body>
		    <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 使用缓存的导航数据（基于分类自动生成，永不过期）
			$navList = getCachedNavigation();

			// 输出导航菜单（跳过首页，因为已经单独显示）
			foreach ($navList as $nav) {
				if ($nav['id'] != 0) { // 跳过首页
					echo '<li><a href="'.$nav['url'].'">'.$nav['name'].'</a></li>';
				}
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

	<?php if($site_analytics): ?>
	<!-- 网站统计代码 -->
	<?php echo $site_analytics ?? ""; ?>
	<?php endif; ?>

<div class="yui-clear"></div>

<!-- 主内容区域 -->
<div class="yui-1200">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">></span>
            <?php if($category['parent_id'] > 0 && $parent_category): ?>
            <a href="/<?php echo (isset($parent_category['pinyin'])) ? $parent_category['pinyin'] : ""; ?>/"><?php echo (isset($parent_category['name'])) ? $parent_category['name'] : ""; ?></a>
            <span class="separator">></span>
            <?php endif; ?>
            
            <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/"><?php echo (isset($category['name'])) ? $category['name'] : ""; ?></a>
        </div>
    </div>
</div>

<!-- 栏目列表筛选区 -->
<div class="yui-content  yui-1200">
    <div class="filter-container">
        <div class="filter-section">
            <div class="filter-label">分类：</div>
            <div class="filter-options">
				<?php if($current_is_subcategory): ?>
				<a href="<?php echo $parentUrl ?? ""; ?>" class="filter-item <?php if(null !== ($category ?? null) && is_array($category) && array_key_exists('id', $category) && null !== ($parent_category ?? null) && is_array($parent_category) && array_key_exists('id', $parent_category) && $category['id'] == $parent_category['id']): ?>active<?php endif; ?>">全部<span></span></a>
				<?php else: ?>
				<a href="<?php echo $baseUrl ?? ""; ?>" class="filter-item active">全部</a>
				<?php endif; ?>
				
			    <?php if(null !== ($subCategories ?? null) && is_array($subCategories)): foreach($subCategories as $subCat): ?>
                <a href="<?php echo (isset($subCat['url'])) ? $subCat['url'] : ""; ?>" class="filter-item <?php if((null !== ($subCat ?? null) && is_array($subCat) && array_key_exists('is_current', $subCat) && $subCat['is_current']) || (null !== ($current_subcategory_id ?? null) && null !== ($subCat ?? null) && is_array($subCat) && array_key_exists('id', $subCat) && $current_subcategory_id == $subCat['id'])): ?>active<?php endif; ?>"><?php echo (isset($subCat['name'])) ? $subCat['name'] : ""; ?><span></span></a>
				<?php endforeach; endif; ?>
              
            </div>
        </div>
        <div class="filter-section">
            <div class="filter-label">地区：</div>
            <div class="filter-options">
			<?php if($showCities): ?>
				<?php if(null !== ($area_arr ?? null) && is_array($area_arr)): foreach($area_arr as $area): ?>
					<?php if(null !== ($area ?? null) && is_array($area) && array_key_exists('is_unlimited', $area) && $area['is_unlimited']): ?>
					<a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="filter-item">不限</a>
					<?php elseif(null !== ($area ?? null) && is_array($area) && array_key_exists('is_city', $area) && $area['is_city']): ?>
					<a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="filter-item <?php if($areaId == $area['id']): ?>active<?php endif; ?>"><?php echo (isset($area['name'])) ? $area['name'] : ""; ?></a>
					<?php endif; ?>
				<?php endforeach; endif; ?>
			<?php else: ?>
				<?php if(null !== ($area_arr ?? null) && is_array($area_arr)): foreach($area_arr as $area): ?>
					<?php if(null !== ($area ?? null) && is_array($area) && array_key_exists('is_unlimited', $area) && $area['is_unlimited']): ?>
					<a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="filter-item <?php if($areaId == 0): ?>active<?php endif; ?>">全部</a>
					<?php elseif(null !== ($area ?? null) && is_array($area) && array_key_exists('is_province', $area) && $area['is_province']): ?>
					<a href="<?php echo (isset($area['url'])) ? $area['url'] : ""; ?>" class="filter-item <?php if($areaId == $area['id']): ?>active<?php endif; ?>"><?php echo (isset($area['name'])) ? $area['name'] : ""; ?></a>
					<?php endif; ?>
				<?php endforeach; endif; ?>
			<?php endif; ?>
           </div>
        </div>
    </div>
</div>

<div class="yui-content yui-1200">
    <div class="yui-index-info-list">

				<!-- 列表左侧 -->
				<div class="yui-index-info-list-left">
					<div class="yui-list-tabs">
						<div class="tab-header">
							<ul>
								<li class="active" style="font-weight: bold; padding-left: 15px; position: relative;">
									<span style="border-left: 2px solid #4285f4; padding-left: 15px; line-height: 1.2;"><?php if(null !== ($category ?? null) && is_array($category) && array_key_exists('name', $category)): ?><?php echo (isset($category['name'])) ? $category['name'] : ""; ?><?php else: ?>全部信息<?php endif; ?></span>
								</li>

							</ul>
							<span class="index-list-post yui-right"><a href="/post.php?cid=<?php if(null !== ($category ?? null) && is_array($category) && array_key_exists('id', $category)): ?><?php echo (isset($category['id'])) ? $category['id'] : ""; ?><?php endif; ?>">在<?php echo (isset($category['name'])) ? $category['name'] : ""; ?>分类下发布信息</a></span>
						</div>
						<?php if(null !== ($posts ?? null) && !empty($posts)): ?>
						<div class="tab-content">
							<div class="tab-pane active">
								<div class="list-header">
									<div class="list-title">信息主题</div>
									<div class="list-meta">
										<span>地区</span>
										<span>有效期</span>
										<!-- <span>价格</span> -->
										<span>时间</span>
									</div>
								</div>
								<ul class="info-list">
									<?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
									<?php 
									if (!empty($post['expired_at'])) {
										$days = getRemainingDaysInt($post['expired_at']);
										$guoqi = $days > 0 ? '<span style="color: red; font-weight: bold;">'.$days.'</span> 天' : '已过期';
									} else {
										$guoqi = '长期';
									}
									 ?>
									<?php 
										// 检查是否显示置顶标记（需要检查过期时间和分类层级关系）
										$current_time = time();
										$show_top_tag = false;
										$icon_class = 'icon_title1.gif'; // 默认普通图标

										// 检查大分类置顶（在大分类页面和该大分类下的所有小分类页面都显示）
										// 由于SQL查询已经过滤了分类，这里显示的信息都属于当前分类体系
										if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
											if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
												$show_top_tag = true;
												$icon_class = 'icon_title2.gif'; // 置顶图标
											}
										}

										// 检查小分类置顶（只在小分类页面显示）
										if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
											// 只有在小分类页面才显示小分类置顶标记
											if (isset($is_secondary_category) && $is_secondary_category) {
												if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
													$show_top_tag = true;
													$icon_class = 'icon_title2.gif'; // 置顶图标
												}
											}
										}

										// 输出li标签和图标，为置顶信息添加CSS类
										$li_class = $show_top_tag ? ' class="top-item"' : '';
										echo '<li' . $li_class . ' style="background:url(/template/pc/images/' . $icon_class . ') left no-repeat; padding-left: 15px;">';
										 ?>
										<div class="info-title">
											<?php if($post['category_name']): ?><a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/" style="color: #999; text-decoration: none; margin-right: 5px;">[<?php echo (isset($post['category_name'])) ? $post['category_name'] : ""; ?>]</a><?php endif; ?>
										<a <?php if($guoqi=='已过期'): ?> style="text-decoration: line-through; color: #999;"<?php endif; ?> target="_blank" href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" <?php if ($show_top_tag && $guoqi!='已过期') echo 'style="color: #EE3131;"'; ?>><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a><?php 
										if ($show_top_tag) {
											echo '<span class="top-tag">顶</span>';
										}
										 ?></div>
										<div class="info-meta">
											<span class="area"><?php echo (isset($post['region_name'])) ? $post['region_name'] : ""; ?></span>
											<span class="validity"><?php echo $guoqi ?? ""; ?></span>
											<!-- <span class="price">面议</span> -->
											<span class="time"><?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?></span>
										</div>
									<?php echo '</li>'; ?>
									<?php endforeach; endif; ?>
									</ul>
							</div>
						</div>
						<?php else: ?>
						<div class="no-info-message" style="text-align: center;">
							<i class="no-info-icon"></i>
							<p>暂无相关信息</p>
						</div>
						<?php endif; ?>
					</div>
					
					<?php if($totalPages > 1): ?>
					<?php echo $pagination ?? ""; ?>
					<?php endif; ?>
					
					
				</div>
				<!-- 列表左侧 end -->
                <!-- 列表右侧 -->
				<div class="yui-index-info-list-right">
					<div class="bbs-hot">
						<div class="yui-h-title">
							<h3>公益广告</h3><span></span>
						</div>
						<div class="yui-img-list">
                           <a href="#"><img src="/template/pc/images/16382506823782.png" /></a>
						</div>
					</div>
				</div>
				<!-- 列表右侧 end -->
			</div>

			<!-- 栏目列表end  -->
    </div>
</div>

<div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
<?php echo get_block('footer_nav'); ?>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
		<!-- 主区域  end-->
	
		<script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
		<script type="text/javascript" src="/template/pc/js/common.js"></script>
		<script>
			$(function() {
				// 筛选标签选择功能
				$('.filter-tags .filter-item').click(function(e) {
					e.preventDefault();
					$(this).toggleClass('active');
				});
				
				// 重置筛选按钮
				$('.filter-reset').click(function() {
					$('.filter-options .filter-item').removeClass('active');
					$('.filter-options .filter-item').first().addClass('active');
					$('.filter-tags .filter-item').removeClass('active');
				});
				
				// 确定筛选按钮
				$('.filter-submit').click(function() {
					alert('筛选条件已应用');
				});
				
				// 初始化加载更多功能
				initLoadMoreButton();
			})
			
			// 是否正在加载中的标志
			var isLoading = false;
			
			// 初始化加载更多按钮
			function initLoadMoreButton() {
				$('.load-more').on('click', function() {
					if (isLoading) return;
					
					const nextPage = parseInt($(this).data('next-page'));
					const catId = parseInt($(this).data('cat-id'));
					const areaId = parseInt($(this).data('area-id'));
					
					loadMorePosts(nextPage, catId, areaId);
				});
			}
			
			// 加载更多文章函数
			function loadMorePosts(page, catId, areaId) {
				if (isLoading) return;
				
				const loadMoreBtn = $('.load-more');
				if (!loadMoreBtn.length) return;
				
				isLoading = true;
				loadMoreBtn.addClass('loading');
				loadMoreBtn.text('加载中...');
				
				// 构建URL
				const url = `/api/posts_loadmore.php?cat_id=${catId}&page=${page}&area_id=${areaId}`;
				console.log('正在加载URL:', url);
				
				// 使用jQuery的AJAX
				$.ajax({
					url: url,
					type: 'GET',
					dataType: 'json',
					success: function(data) {
						isLoading = false;
						console.log('收到数据:', data);
						
						if (data.code === 200) {
							// 直接将HTML内容添加到列表中
							const html = data.html;
							$('.info-list').append(html);
							console.log('添加了新内容');
							
							// 更新加载更多按钮
							if (data.has_more) {
								loadMoreBtn.removeClass('loading');
								loadMoreBtn.text('点击加载更多');
								loadMoreBtn.data('next-page', data.next_page);
								console.log('设置下一页:', data.next_page);
							} else {
								// 没有更多内容
								const container = loadMoreBtn.closest('.pagination-container');
								if (container.length) {
									container.html('<div class="load-more-end">已加载全部内容</div>');
								} else {
									loadMoreBtn.parent().html('<div class="load-more-end">已加载全部内容</div>');
								}
								console.log('没有更多内容');
							}
						} else {
							// 加载失败
							loadMoreBtn.removeClass('loading');
							loadMoreBtn.text('加载失败，点击重试');
							console.error('加载失败:', data.msg);
						}
					},
					error: function(xhr, status, error) {
						isLoading = false;
						loadMoreBtn.removeClass('loading');
						loadMoreBtn.text('加载失败，点击重试');
						console.error('加载更多内容失败:', error);
					}
				});
			}
		</script>
	</body>
</html>
